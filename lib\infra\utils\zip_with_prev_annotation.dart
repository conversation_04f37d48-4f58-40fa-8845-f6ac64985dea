/// Annotation for creating a computed property that zips the current value with its previous value.
///
/// This annotation can be used on getter methods to create a computed property
/// that returns a list containing [currentValue, previousValue].
///
/// Example:
/// ```dart
/// @ZipWithPrev()
/// int get someValue => expensiveCalculation();
/// ```
///
/// The annotated property will return a List<T> containing [currentValue, previousValue].
/// If there is no previous value yet, both elements will be the same.
class ZipWithPrev {
  /// Creates a new [ZipWithPrev] annotation.
  const ZipWithPrev();
}
