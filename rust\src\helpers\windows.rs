use std::io;

use filetime::FileTime;
use std::path::Path as StdPath;
use tokio::fs;
use windows::{
    core::PC<PERSON><PERSON>,
    Win32::Foundation::{CloseHandle, FILETIME},
    Win32::Storage::FileSystem::{
        CreateFileW, SetFileTime, FILE_ATTRIBUTE_NORMAL, FILE_FLAG_BACKUP_SEMANTICS,
        FILE_SHARE_DELETE, FILE_SHARE_READ, FILE_SHARE_WRITE, FILE_WRITE_ATTRIBUTES, OPEN_EXISTING,
    },
};

use crate::api::domain::{CopyOptions, PathStats};
use crate::api::error::FileSystemError;
use crate::frb_generated::StreamSink;
use windows::Win32::Foundation::{
    ERROR_ALREADY_EXISTS, ERROR_BAD_NETPATH, ERROR_BROKEN_PIPE, ERROR_BUSY, ERROR_CANN<PERSON>_MAKE,
    ERROR_DIRECTORY, ERROR_DIR_NOT_EMPTY, ERROR_DISK_FULL, ERROR_FILENAME_EXCED_RANGE,
    ERROR_FILE_EXISTS, ERROR_FILE_NOT_FOUND, ERROR_FILE_TOO_LARGE, ERROR_HANDLE_EOF,
    ERROR_INVALID_DATA, ERROR_IO_PENDING, ERROR_LOCK_VIOLATION, ERROR_NETNAME_DELETED,
    ERROR_NOT_SAME_DEVICE, ERROR_NOT_SUPPORTED, ERROR_OPERATION_ABORTED, ERROR_PATH_NOT_FOUND,
    ERROR_POSSIBLE_DEADLOCK, ERROR_REQ_NOT_ACCEP, ERROR_SEEK_ON_DEVICE, ERROR_SHARING_VIOLATION,
    ERROR_TIMEOUT, ERROR_TOO_MANY_LINKS, ERROR_WRITE_PROTECT, E_ACCESSDENIED, E_INVALIDARG,
    E_NOTIMPL, E_OUTOFMEMORY, FACILITY_NTWIN32, STG_E_FILENOTFOUND, STG_E_PATHNOTFOUND,
};
use windows::Win32::Networking::WinSock::{
    WSAEADDRINUSE, WSAEADDRNOTAVAIL, WSAECONNABORTED, WSAECONNRESET, WSAEINPROGRESS, WSAEINTR,
    WSAENETDOWN, WSAENETUNREACH, WSAENOTCONN, WSAESTALE, WSAEWOULDBLOCK,
};
use windows::Win32::Storage::FileSystem::CopyFileW;

pub async fn list(
    path: String,
    sink: StreamSink<(String, PathStats)>,
) -> Result<(), FileSystemError> {
    let mut entries = match fs::read_dir(StdPath::new(&path)).await {
        Ok(entries) => entries,
        Err(e) => {
            let fs_error: FileSystemError = e.into();
            if let Err(sink_err) = sink.add_error(fs_error.clone()) {
                log::error!(
                    "Failed to add read_dir error to sink for path '{}': {}",
                    path,
                    sink_err
                );
            }
            return Err(fs_error);
        }
    };

    tokio::spawn(async move {
        loop {
            match entries.next_entry().await {
                Ok(Some(entry)) => {
                    let entry_path = entry.path().to_string_lossy().to_string();
                    let metadata: Result<std::fs::Metadata, io::Error> = entry.metadata().await;
                    let stats: PathStats = metadata.into();

                    if let Err(e) = sink.add((entry_path, stats)) {
                        log::debug!("Stream sink closed, stopping directory listing: {}", e);
                        return;
                    }
                }
                Ok(None) => break,
                Err(e) => {
                    let fs_error: FileSystemError = e.into();
                    if let Err(sink_err) = sink.add_error(fs_error) {
                        log::error!(
                            "Failed to add entry processing error to sink for path '{}': {}",
                            path,
                            sink_err
                        );
                    }
                    return;
                }
            }
        }
    });

    Ok(())
}

pub async fn copy_file(
    source: &str,
    dest: &str,
    options: &CopyOptions,
) -> Result<(), FileSystemError> {
    let source_wide = to_wide_string(&source);
    let dest_wide = to_wide_string(&dest);

    let fail_if_exists = !options.overwrite_if_exists;

    // CopyFileW is blocking, so run it in a blocking task
    tokio::task::spawn_blocking(move || unsafe {
        CopyFileW(
            PCWSTR(source_wide.as_ptr()),
            PCWSTR(dest_wide.as_ptr()),
            windows::Win32::Foundation::BOOL::from(fail_if_exists),
        )
    })
    .await??;
    Ok(())
}

pub fn set_timestamps(
    path: impl AsRef<StdPath>+Send+'static,
    ctime: FileTime,
    mtime: FileTime,
    atime: FileTime,
) -> Result<(), FileSystemError> {
    let win_ft_create = ft_to_windows_filetime(ctime);
    let win_ft_modify = ft_to_windows_filetime(mtime);
    let win_ft_access = ft_to_windows_filetime(atime);

    let wide_path = to_wide_string(&path);

    let open_flags = FILE_ATTRIBUTE_NORMAL | FILE_FLAG_BACKUP_SEMANTICS;

    let handle = unsafe {
        CreateFileW(
            PCWSTR(wide_path.as_ptr()),
            FILE_WRITE_ATTRIBUTES.0, // Use .0 to get the u32 value
            FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
            None, // lpSecurityAttributes expects Option<*const SECURITY_ATTRIBUTES>
            OPEN_EXISTING,
            open_flags, // dwFlagsAndAttributes
            None,       // hTemplateFile expects Option<HANDLE>
        )
    }?;

    let set_time_success = unsafe {
        SetFileTime(
            handle,               // Pass the unwrapped HANDLE
            Some(&win_ft_create), // lpCreationTime expects Option<*const FILETIME>
            Some(&win_ft_access), // lpLastAccessTime expects Option<*const FILETIME>
            Some(&win_ft_modify), // lpLastWriteTime expects Option<*const FILETIME>
        )
    };

    // Close the handle regardless of SetFileTime success.
    let close_handle_success = unsafe { CloseHandle(handle) };

    if set_time_success.is_err() {
        // Check Result from SetFileTime
        // If SetFileTime failed, capture its error before potentially being overwritten by CloseHandle error.
        // The error from SetFileTime is already in set_time_success.Err(e)
        // but io::Error::last_os_error() might also be relevant if SetFileTime doesn't populate the Error well.
        // For now, let's assume the error from SetFileTime is sufficient or use last_os_error directly.
        let set_time_error = FileSystemError::from(io::Error::last_os_error()); // Or map from set_time_success.err().unwrap()
        if close_handle_success.is_err() {
            // Log CloseHandle error if it also failed, but prioritize SetFileTime error.
            log::error!(
                "Failed to close handle for {} after SetFileTime error: {:?}",
                path.as_ref().to_str().unwrap(),
                io::Error::last_os_error()
            );
        }
        return Err(set_time_error);
    }

    if close_handle_success.is_err() {
        // SetFileTime succeeded, but CloseHandle failed. This is less critical but still an error.
        log::error!(
            "SetFileTime succeeded but failed to close handle for {}: {:?}",
            path.as_ref().to_str().unwrap(),
            io::Error::last_os_error()
        );
        return Err(FileSystemError::Other {
            message: format!(
                "Failed to close handle for {}: {}",
                path.as_ref().to_str().unwrap(),
                io::Error::last_os_error()
            ),
        });
    }

    Ok(())
}

impl From<windows::core::Error> for FileSystemError {
    fn from(win_err: windows::core::Error) -> Self {
        // Define constants for HRESULT values to be used in match patterns
        const E_FNF: i32 = STG_E_FILENOTFOUND.0;
        const STG_PNF: i32 = STG_E_PATHNOTFOUND.0;
        const E_ACCESS: i32 = E_ACCESSDENIED.0;
        const E_OMEM: i32 = E_OUTOFMEMORY.0;
        const E_INVAL: i32 = E_INVALIDARG.0;
        const E_NI: i32 = E_NOTIMPL.0;

        const HR_ERROR_FILE_NOT_FOUND: i32 = self::hresult_from_win32(ERROR_FILE_NOT_FOUND.0);
        const HR_FILE_EXISTS: i32 = self::hresult_from_win32(ERROR_FILE_EXISTS.0);
        const HR_ALREADY_EXISTS: i32 = self::hresult_from_win32(ERROR_ALREADY_EXISTS.0);
        const HR_PATH_NOT_FOUND: i32 = self::hresult_from_win32(ERROR_PATH_NOT_FOUND.0);
        const HR_DISK_FULL: i32 = self::hresult_from_win32(ERROR_DISK_FULL.0);
        const HR_DIR_NOT_EMPTY: i32 = self::hresult_from_win32(ERROR_DIR_NOT_EMPTY.0);
        const HR_NOT_A_DIRECTORY: i32 = self::hresult_from_win32(ERROR_DIRECTORY.0);
        const HR_WRITE_PROTECT: i32 = self::hresult_from_win32(ERROR_WRITE_PROTECT.0);
        const HR_SHARING_VIOLATION: i32 = self::hresult_from_win32(ERROR_SHARING_VIOLATION.0);
        const HR_LOCK_VIOLATION: i32 = self::hresult_from_win32(ERROR_LOCK_VIOLATION.0);
        const HR_FILENAME_EXCED_RANGE: i32 = self::hresult_from_win32(ERROR_FILENAME_EXCED_RANGE.0);
        const HR_FILE_TOO_LARGE: i32 = self::hresult_from_win32(ERROR_FILE_TOO_LARGE.0);
        const HR_NOT_SUPPORTED: i32 = self::hresult_from_win32(ERROR_NOT_SUPPORTED.0);
        const HR_BAD_NETPATH: i32 = self::hresult_from_win32(ERROR_BAD_NETPATH.0);
        const HR_NETNAME_DELETED: i32 = self::hresult_from_win32(ERROR_NETNAME_DELETED.0);
        const HR_REQ_NOT_ACCEP: i32 = self::hresult_from_win32(ERROR_REQ_NOT_ACCEP.0);
        const HR_TIMEOUT: i32 = self::hresult_from_win32(ERROR_TIMEOUT.0);

        // Manually defined Win32 error codes if not available in windows crate with current features
        const CUSTOM_ERROR_SYMLINK_LOOP: u32 = 1159; // ERROR_SYMLINK_LOOP

        // Additional Win32 Foundation errors
        const HR_BROKEN_PIPE: i32 = self::hresult_from_win32(ERROR_BROKEN_PIPE.0);
        const HR_CANNOT_MAKE: i32 = self::hresult_from_win32(ERROR_CANNOT_MAKE.0); // For IsADirectory
        const HR_SYMLINK_LOOP: i32 = self::hresult_from_win32(CUSTOM_ERROR_SYMLINK_LOOP);
        const HR_INVALID_DATA: i32 = self::hresult_from_win32(ERROR_INVALID_DATA.0);
        const HR_SEEK_ON_DEVICE: i32 = self::hresult_from_win32(ERROR_SEEK_ON_DEVICE.0);
        const HR_BUSY: i32 = self::hresult_from_win32(ERROR_BUSY.0); // For ExecutableFileBusy
        const HR_POSSIBLE_DEADLOCK: i32 = self::hresult_from_win32(ERROR_POSSIBLE_DEADLOCK.0);
        const HR_NOT_SAME_DEVICE: i32 = self::hresult_from_win32(ERROR_NOT_SAME_DEVICE.0);
        const HR_TOO_MANY_LINKS: i32 = self::hresult_from_win32(ERROR_TOO_MANY_LINKS.0);
        const HR_OPERATION_ABORTED: i32 = self::hresult_from_win32(ERROR_OPERATION_ABORTED.0);
        const HR_HANDLE_EOF: i32 = self::hresult_from_win32(ERROR_HANDLE_EOF.0);
        const HR_IO_PENDING: i32 = self::hresult_from_win32(ERROR_IO_PENDING.0);

        // WinSock errors
        const HR_WSAECONNRESET: i32 = self::hresult_from_win32(WSAECONNRESET.0 as u32);
        const HR_WSAENETUNREACH: i32 = self::hresult_from_win32(WSAENETUNREACH.0 as u32);
        const HR_WSAECONNABORTED: i32 = self::hresult_from_win32(WSAECONNABORTED.0 as u32);
        const HR_WSAENOTCONN: i32 = self::hresult_from_win32(WSAENOTCONN.0 as u32);
        const HR_WSAEADDRINUSE: i32 = self::hresult_from_win32(WSAEADDRINUSE.0 as u32);
        const HR_WSAEADDRNOTAVAIL: i32 = self::hresult_from_win32(WSAEADDRNOTAVAIL.0 as u32);
        const HR_WSAENETDOWN: i32 = self::hresult_from_win32(WSAENETDOWN.0 as u32);
        const HR_WSAEWOULDBLOCK: i32 = self::hresult_from_win32(WSAEWOULDBLOCK.0 as u32);
        const HR_WSAESTALE: i32 = self::hresult_from_win32(WSAESTALE.0 as u32);
        const HR_WSAEINTR: i32 = self::hresult_from_win32(WSAEINTR.0 as u32);
        const HR_WSAEINPROGRESS: i32 = self::hresult_from_win32(WSAEINPROGRESS.0 as u32);

        let hresult = win_err.code().0;
        let message = win_err.message().to_string();

        match hresult {
            E_FNF | STG_PNF | HR_ERROR_FILE_NOT_FOUND => FileSystemError::NotFound { message },
            E_ACCESS => FileSystemError::PermissionDenied { message },
            E_OMEM => FileSystemError::OutOfMemory { message },
            E_INVAL => FileSystemError::InvalidInput { message },
            E_NI | HR_NOT_SUPPORTED => FileSystemError::Unsupported { message },

            HR_FILE_EXISTS => FileSystemError::AlreadyExists { message },
            HR_ALREADY_EXISTS => FileSystemError::AlreadyExists { message },
            HR_PATH_NOT_FOUND | HR_BAD_NETPATH => FileSystemError::NotFound { message },
            HR_DISK_FULL => FileSystemError::StorageFull { message },
            HR_FILE_TOO_LARGE => FileSystemError::FileTooLarge { message },
            HR_DIR_NOT_EMPTY => FileSystemError::DirectoryNotEmpty { message },
            HR_NOT_A_DIRECTORY => FileSystemError::NotADirectory { message },
            HR_WRITE_PROTECT => FileSystemError::ReadOnlyFilesystem { message },
            HR_SHARING_VIOLATION => FileSystemError::ResourceBusy { message },
            HR_LOCK_VIOLATION => FileSystemError::ResourceBusy { message },
            HR_FILENAME_EXCED_RANGE => FileSystemError::InvalidFilename { message },
            HR_NETNAME_DELETED => FileSystemError::HostUnreachable { message },
            HR_REQ_NOT_ACCEP => FileSystemError::ConnectionRefused { message },
            HR_TIMEOUT => FileSystemError::TimedOut { message },

            // Mappings for additional Win32 Foundation errors
            HR_BROKEN_PIPE => FileSystemError::BrokenPipe { message },
            HR_CANNOT_MAKE => FileSystemError::IsADirectory { message },
            HR_SYMLINK_LOOP => FileSystemError::FilesystemLoop { message },
            HR_INVALID_DATA => FileSystemError::InvalidData { message },
            HR_SEEK_ON_DEVICE => FileSystemError::NotSeekable { message },
            HR_BUSY => FileSystemError::ExecutableFileBusy { message }, // Or ResourceBusy if more general
            HR_POSSIBLE_DEADLOCK => FileSystemError::Deadlock { message },
            HR_NOT_SAME_DEVICE => FileSystemError::CrossesDevices { message },
            HR_TOO_MANY_LINKS => FileSystemError::TooManyLinks { message },
            HR_OPERATION_ABORTED | HR_WSAEINTR => FileSystemError::Interrupted { message },
            HR_HANDLE_EOF => FileSystemError::UnexpectedEof { message },
            HR_IO_PENDING | HR_WSAEINPROGRESS => FileSystemError::InProgress { message },

            // Mappings for WinSock errors
            HR_WSAECONNRESET => FileSystemError::ConnectionReset { message },
            HR_WSAENETUNREACH => FileSystemError::NetworkUnreachable { message },
            HR_WSAECONNABORTED => FileSystemError::ConnectionAborted { message },
            HR_WSAENOTCONN => FileSystemError::NotConnected { message },
            HR_WSAEADDRINUSE => FileSystemError::AddrInUse { message },
            HR_WSAEADDRNOTAVAIL => FileSystemError::AddrNotAvailable { message },
            HR_WSAENETDOWN => FileSystemError::NetworkDown { message },
            HR_WSAEWOULDBLOCK => FileSystemError::WouldBlock { message },
            HR_WSAESTALE => FileSystemError::StaleNetworkFileHandle { message },

            // Fallback for other errors
            _ => {
                let io_err: std::io::Error = win_err.into();
                match io_err.kind() {
                    io::ErrorKind::NotFound => FileSystemError::NotFound {
                        message: io_err.to_string(),
                    },
                    io::ErrorKind::PermissionDenied => FileSystemError::PermissionDenied {
                        message: io_err.to_string(),
                    },
                    io::ErrorKind::AlreadyExists => FileSystemError::AlreadyExists {
                        message: io_err.to_string(),
                    },
                    io::ErrorKind::InvalidInput => FileSystemError::InvalidInput {
                        message: io_err.to_string(),
                    },
                    io::ErrorKind::NotADirectory => FileSystemError::NotADirectory {
                        message: io_err.to_string(),
                    },
                    io::ErrorKind::IsADirectory => FileSystemError::IsADirectory {
                        message: io_err.to_string(),
                    },
                    io::ErrorKind::DirectoryNotEmpty => FileSystemError::DirectoryNotEmpty {
                        message: io_err.to_string(),
                    },
                    _ => FileSystemError::Other {
                        message: io_err.to_string(),
                    },
                }
            }
        }
    }
}

// Helper to construct HRESULT from Win32 error code
const fn hresult_from_win32(code: u32) -> i32 {
    (code & 0x0000FFFF) as i32 | ((FACILITY_NTWIN32.0 as i32) << 16) | 0x80000000_u32 as i32
}

// Convert a path string to a Windows wide string
fn to_wide_string<P: AsRef<StdPath>>(path: &P) -> Vec<u16> {
    path.as_ref().to_str().unwrap().encode_utf16().chain(std::iter::once(0)).collect()
}

// Sets file timestamps using Windows native API.
// Private helper function to convert filetime::FileTime to windows::Win32::Foundation::FILETIME
fn ft_to_windows_filetime(ft: FileTime) -> FILETIME {
    // This calculation is based on the internal logic of the filetime crate
    // for converting its FileTime to a Windows FILETIME.
    // It converts seconds and nanoseconds (from Unix epoch) into 100-nanosecond intervals.
    // The Windows FILETIME struct itself represents 100-nanosecond intervals since Jan 1, 1601.
    // The filetime crate handles the epoch conversion when constructing FileTime from SystemTime.
    let intervals = ft.seconds() * (1_000_000_000 / 100) + ((ft.nanoseconds() as i64) / 100);
    FILETIME {
        dwLowDateTime: intervals as u32,
        dwHighDateTime: (intervals >> 32) as u32,
    }
}
