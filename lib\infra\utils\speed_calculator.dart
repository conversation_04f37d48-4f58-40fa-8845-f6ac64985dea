/// A class that calculates speed based on input values.
class SpeedCalculator {
  num _lastTime = DateTime.now().millisecondsSinceEpoch;
  num _lastValue = 0;
  double _value = 0;
  num _time = 0;
  final int timeWindow;

  /// Creates a Speedometer instance.
  ///
  /// It tracks the rate of change over time and returns the speed in units per second.
  /// This is a Dart implementation of the npm speedometer package.
  SpeedCalculator({this.timeWindow = 5});

  /// Calculates the current speed based on the new value.
  double calculate(num newValue) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final elapsed = now - _lastTime;

    if (elapsed < 1) {
      return _value;
    }

    const maxElapsed = 1000; // 1 second max
    final elapsedCapped = elapsed < maxElapsed ? elapsed : maxElapsed;

    // Calculate the instantaneous speed
    _time += elapsedCapped;
    _value = ((newValue - _lastValue) * 1000) / elapsedCapped;

    // If we have enough samples in our window, start to decay old ones
    if (_time >= timeWindow * 1000) {
      // Decay the value exponentially
      _value = _value * 0.5;

      // Reset the window
      _time = 0;
    }

    _lastValue = newValue;
    _lastTime = now;

    return _value < 0 ? 0.0 : _value;
  }

  /// Resets the speedometer state.
  void reset() {
    _lastTime = DateTime.now().millisecondsSinceEpoch;
    _lastValue = 0;
    _value = 0;
    _time = 0;
  }
}
