import 'dart:async';

import 'package:dartx/dartx.dart';
import 'package:mobx/mobx.dart';

import './completable_future.dart';
import './observable_utils.dart';

part '.gen/async_value.g.dart';

// TODO: If we need to also keep state in an ObservableFuture, it has to be internal to the AsyncValue
// TODO: So that they change state at the same time, and not the ObservableFuture running as a reaction
// TODO: Which will mean it can be inconsistent with the AsyncValue for a short duration.

// TODO: Make this basically an ObservableFuture, possibly settable.
// TODO: Maybe only keep 1 state - isLoading, error / value and a prevValue.
abstract class AsyncValue<T> {
  bool get isLoading;
  Exception? get error;
  T get value;

  bool get hasError => error != null;
  bool get hasValue => !isLoading && !hasError;

  Future<T> get future;

  AsyncValueSnapshot<T> get snapshot {
    return AsyncValueSnapshot(
      isLoading: isLoading,
      error: error,
      value: value,
    );
  }

  @override
  String toString() {
    final className = runtimeType.toString();
    return isLoading
        ? '$className{loading...}'
        : error != null
            ? '$className{error: $error}'
            : '$className{$value}';
  }

  static AsyncValue<T> loading<T>(T initialValue) => AsyncValueSnapshot(value: initialValue, isLoading: true);

  static DisposableAsyncValue<R> reduce<T, R>(Iterable<AsyncValue<T>> asyncValues, R Function(Iterable<T>) reducer) {
    return ReducedAsyncValue(asyncValues, reducer);
  }
}

abstract class DisposableAsyncValue<T> extends AsyncValue<T> with Disposable {}

class MutableAsyncValue<T> extends MutableAsyncValueBase<T> with _$MutableAsyncValue<T> {
  MutableAsyncValue({required super.initialValue, super.initialLoading});
}

abstract class MutableAsyncValueBase<T> extends AsyncValue<T> with Store {
  @readonly
  bool _isLoading;

  @readonly
  Exception? _error;

  @readonly
  T _value;

  final _future = CompletableFuture<T>();

  MutableAsyncValueBase({required T initialValue, bool initialLoading = true})
      : _value = initialValue,
        _isLoading = initialLoading;

  @override
  @computed
  AsyncValueSnapshot<T> get snapshot => super.snapshot;

  @action
  void setLoading(bool isLoading) {
    if (_isLoading != isLoading) {
      _isLoading = isLoading;
      if (isLoading) {
        _future.setPending();
      }
    }
  }

  @action
  void setError(Exception? error) {
    if (_error != error) {
      _error = error;
      if (error != null) {
        // FIXME: This results in unhandled errors, if there's noone listening on the future.
        _future.completeError(error);
      }
    }
  }

  @action
  void setValue(T value, [EqualityComparer<T> equality = identical]) {
    if (!equality(_value, value)) {
      _value = value;
      _future.complete(value);
    }
  }

  @action
  void setValueClearError(T value, [EqualityComparer<T> equality = identical]) {
    setValue(value, equality);
    setError(null);
  }

  @action
  void set({bool isLoading = false, Exception? error, T? value, EqualityComparer<T> equality = identical}) {
    setLoading(isLoading);
    setError(error);
    if (value != null) {
      setValue(value, equality);
    }
  }

  @override
  Future<T> get future => _future;
}

class RunnableAsyncValue<T> extends MutableAsyncValue<T> {
  final FutureOr<T> Function() _run;
  final void Function(bool loading)? _onLoading;
  final FutureOr<void> Function(T)? _onValue;
  final Exception? Function(Exception)? _onError;

  RunnableAsyncValue(
    this._run, {
    required super.initialValue,
    super.initialLoading,
    void Function(bool loading)? onLoading,
    FutureOr<void> Function(T)? onValue,
    Exception? Function(Exception)? onError,
  })  : _onLoading = onLoading,
        _onValue = onValue,
        _onError = onError;

  Future<void> run() async {
    try {
      setLoading(true);
      _onLoading?.call(true);
      final valueOrFuture = _run();
      final T value;
      if (valueOrFuture is Future) {
        value = await valueOrFuture;
      } else {
        value = valueOrFuture;
      }
      final onValueResult = _onValue?.call(value);
      if (onValueResult is Future) {
        await onValueResult;
      }
      // This must be called last as onValue may do some processing.
      // Setting this last ensures that the future is not completed until onValue has a chance to do its thing.
      setValueClearError(value);
    } on Exception catch (e) {
      setError(_onError?.call(e) ?? e);
    } finally {
      setLoading(false);
      _onLoading?.call(false);
    }
  }
}

class MappedAsyncValue<T, R> extends DisposableAsyncValue<R> {
  final AsyncValue<T> _source;
  final R Function(T value) _map;
  final MutableAsyncValue<R> _result;

  late final ReactionDisposer _disposer;

  MappedAsyncValue(this._source, this._map) : _result = MutableAsyncValue(initialValue: _map(_source.value), initialLoading: true) {
    _disposer = autorun((_) {
      if (_source.isLoading) {
        _result.setLoading(true);
        return;
      }

      final error = _source.error;
      if (error != null) {
        _result.set(isLoading: false, error: error);
        return;
      }

      try {
        _result.setValue(_map(_source.value));
      } on Exception catch (e) {
        // TODO: Maybe... Don't catch Exception, allow dynamic. Create an AppException.catch() method?
        _result.setError(e);
      } finally {
        _result.setLoading(false);
      }
    });
  }

  @override
  bool get isLoading => _result.isLoading;

  @override
  Exception? get error => _result.error;

  @override
  R get value => _result.value;

  @override
  Future<R> get future => _result.future;

  @override
  void dispose() => _disposer();
}

class ReducedAsyncValue<T, R> extends DisposableAsyncValue<R> {
  final Iterable<AsyncValue<T>> _sources;
  final R Function(Iterable<T>) _reducer;
  final MutableAsyncValue<R> _result;

  late final ReactionDisposer _disposer;

  ReducedAsyncValue(this._sources, this._reducer)
      : _result = MutableAsyncValue(initialValue: _reducer(_sources.map((e) => e.value)), initialLoading: true) {
    _disposer = autorun((_) {
      if (_sources.any((e) => e.isLoading)) {
        _result.setLoading(true);
        return;
      }

      final error = _sources.firstOrNullWhere((e) => e.error != null)?.error;
      if (error != null) {
        _result.set(isLoading: false, error: error);
        return;
      }

      try {
        _result.setValue(_reducer(_sources.map((e) => e.value)));
      } on Exception catch (e) {
        _result.setError(e);
      } finally {
        _result.setLoading(false);
      }
    });
  }

  @override
  bool get isLoading => _result.isLoading;

  @override
  Exception? get error => _result.error;

  @override
  R get value => _result.value;

  @override
  Future<R> get future => _result.future;

  @override
  void dispose() => _disposer();
}

class AsyncValueSnapshot<T> extends AsyncValue<T> {
  @override
  final bool isLoading;

  @override
  final Exception? error;

  @override
  final T value;

  AsyncValueSnapshot({this.isLoading = false, this.error, required this.value});

  @override
  AsyncValueSnapshot<T> get snapshot => this;

  @override
  late final Future<T> future = error != null ? Future.error(error!) : Future.value(value);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AsyncValue && runtimeType == other.runtimeType && isLoading == other.isLoading && error == other.error && value == other.value;

  @override
  int get hashCode => isLoading.hashCode ^ error.hashCode ^ value.hashCode;

  AsyncValueSnapshot<R> map<R>(R Function(T value) map) {
    return AsyncValueSnapshot<R>(
      isLoading: isLoading,
      error: error,
      value: map(value),
    );
  }
}

// // This is a dummy implementation, AsyncValueSnapshot does not need to be disposed.
// class _DisposableAsyncValueSnapshot<T> extends AsyncValueSnapshot<T>
//     implements DisposableAsyncValue<T> {
//   _DisposableAsyncValueSnapshot({super.isLoading, super.error, required super.value});
//
//   @override
//   void dispose() {}
// }

extension AsyncValueExtension<T> on AsyncValue<T> {
  DisposableAsyncValue<R> map<R>(R Function(T value) map) => MappedAsyncValue(this, map);
}

extension AsyncSnapshotExtensions<T> on T {
  AsyncValueSnapshot<T> get asSnapshot => AsyncValueSnapshot(value: this);
}
