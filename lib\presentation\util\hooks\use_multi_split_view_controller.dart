import 'package:flutter/widgets.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:multi_split_view/multi_split_view.dart';

const useMultiSplitViewController = _MultiSplitViewControllerHookCreator();

class _MultiSplitViewControllerHookCreator {
  const _MultiSplitViewControllerHookCreator();

  MultiSplitViewController call({List<Area>? initialAreas, List<Object?>? keys}) {
    return use(_MultiSplitViewControllerHook(initialAreas, keys));
  }
}

class _MultiSplitViewControllerHook extends Hook<MultiSplitViewController> {
  const _MultiSplitViewControllerHook(this.initialAreas, [List<Object?>? keys]) : super(keys: keys);

  final List<Area>? initialAreas;

  @override
  _MultiSplitViewControllerHookState createState() => _MultiSplitViewControllerHookState();
}

class _MultiSplitViewControllerHookState extends HookState<MultiSplitViewController, _MultiSplitViewControllerHook> {
  late final _controller = MultiSplitViewController(areas: hook.initialAreas);

  @override
  MultiSplitViewController build(BuildContext context) => _controller;

  @override
  void dispose() => _controller.dispose();

  @override
  String get debugLabel => 'useMultiSplitViewController';
}
