import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/directory_view/widgets/file_list.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/root_store.dart';
import 'package:qfiler/presentation/util/setup.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  group('QFiler Widget Focus Tests', () {
    testWidgets('Left FileList widget should be focused when app starts', (WidgetTester tester) async {
      // Create temporary directories for testing
      final tempBaseDir = await io.Directory.systemTemp.createTemp('qfiler_widget_focus_test_');
      final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
      final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();

      try {
        // Create some test files in both directories
        await io.File(p.join(leftTempDir.path, 'test_file_left.txt')).writeAsString('Left pane test file');
        await io.File(p.join(rightTempDir.path, 'test_file_right.txt')).writeAsString('Right pane test file');

        // Use the default RootStore creation but override the initial directories
        final rootStore = await RootStore.create();

        // Override the initial directories after creation
        rootStore.allPanesStore.left.historyStore.addWithPath(RawPath(leftTempDir.path));
        rootStore.allPanesStore.right.historyStore.addWithPath(RawPath(rightTempDir.path));

        // Pump the app
        await tester.pumpWidget(App(rootStore));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Verify the app loaded successfully
        expect(find.byType(App), findsOneWidget);

        // Find the FileList widgets
        final fileLists = find.byType(FileList);
        expect(fileLists, findsNWidgets(2));

        // Get the left and right FileList widgets
        final leftFileList = fileLists.at(0);
        final rightFileList = fileLists.at(1);

        // Check which FileList widget has focus
        final leftFocusNode = tester
            .widget<Focus>(
              find
                  .descendant(
                    of: leftFileList,
                    matching: find.byType(Focus),
                  )
                  .first,
            )
            .focusNode;

        final rightFocusNode = tester
            .widget<Focus>(
              find
                  .descendant(
                    of: rightFileList,
                    matching: find.byType(Focus),
                  )
                  .first,
            )
            .focusNode;

        if (kDebugMode) {
          print('Left FileList has focus: ${leftFocusNode?.hasFocus ?? false}');
          print('Right FileList has focus: ${rightFocusNode?.hasFocus ?? false}');
          print('Left pane is source: ${rootStore.allPanesStore.left.isSource}');
          print('Right pane is source: ${rootStore.allPanesStore.right.isSource}');
        }

        // The left FileList should be focused when the app starts
        expect(leftFocusNode?.hasFocus, isTrue, reason: 'Left FileList should be focused on app start');
        expect(rightFocusNode?.hasFocus, isFalse, reason: 'Right FileList should not be focused on app start');

        // Also verify that the left pane is marked as the source pane
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));
      } finally {
        // Clean up temporary directories
        if (await tempBaseDir.exists()) {
          await tempBaseDir.delete(recursive: true);
        }
      }
    });

    testWidgets('Tab key should switch widget focus between FileList widgets', (WidgetTester tester) async {
      // Create temporary directories for testing
      final tempBaseDir = await io.Directory.systemTemp.createTemp('qfiler_tab_focus_test_');
      final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
      final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();

      try {
        // Create some test files in both directories
        await io.File(p.join(leftTempDir.path, 'test_file_left.txt')).writeAsString('Left pane test file');
        await io.File(p.join(rightTempDir.path, 'test_file_right.txt')).writeAsString('Right pane test file');

        // Use the default RootStore creation but override the initial directories
        final rootStore = await RootStore.create();

        // Override the initial directories after creation
        rootStore.allPanesStore.left.historyStore.addWithPath(RawPath(leftTempDir.path));
        rootStore.allPanesStore.right.historyStore.addWithPath(RawPath(rightTempDir.path));

        // Pump the app
        await tester.pumpWidget(App(rootStore));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Find the FileList widgets
        final fileLists = find.byType(FileList);
        expect(fileLists, findsNWidgets(2));

        // Get focus nodes
        final leftFocusNode = tester
            .widget<Focus>(
              find.descendant(of: fileLists.at(0), matching: find.byType(Focus)).first,
            )
            .focusNode;

        final rightFocusNode = tester
            .widget<Focus>(
              find.descendant(of: fileLists.at(1), matching: find.byType(Focus)).first,
            )
            .focusNode;

        // Initially, left should be focused
        expect(leftFocusNode?.hasFocus, isTrue);
        expect(rightFocusNode?.hasFocus, isFalse);

        // Press Tab key
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        if (kDebugMode) {
          print('After Tab - Left FileList has focus: ${leftFocusNode?.hasFocus ?? false}');
          print('After Tab - Right FileList has focus: ${rightFocusNode?.hasFocus ?? false}');
          print('After Tab - Source pane: ${rootStore.allPanesStore.sourcePaneSide}');
        }

        // After Tab, right should be focused
        expect(rightFocusNode?.hasFocus, isTrue, reason: 'Right FileList should be focused after Tab');
        expect(leftFocusNode?.hasFocus, isFalse, reason: 'Left FileList should not be focused after Tab');
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.right));

        // Press Tab again
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        // Should be back to left
        expect(leftFocusNode?.hasFocus, isTrue, reason: 'Left FileList should be focused after second Tab');
        expect(rightFocusNode?.hasFocus, isFalse, reason: 'Right FileList should not be focused after second Tab');
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));
      } finally {
        // Clean up temporary directories
        if (await tempBaseDir.exists()) {
          await tempBaseDir.delete(recursive: true);
        }
      }
    });
  });
}
