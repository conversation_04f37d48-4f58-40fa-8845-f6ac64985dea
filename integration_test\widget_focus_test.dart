import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/directory_view/widgets/file_list.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/root_store.dart';
import 'package:qfiler/presentation/util/setup.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  group('QFiler Widget Focus Tests', () {
    testWidgets('Left FileList widget should be focused when app starts', (WidgetTester tester) async {
      // Create temporary directories for testing
      final tempBaseDir = await io.Directory.systemTemp.createTemp('qfiler_widget_focus_test_');
      final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
      final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();

      try {
        // Create some test files in both directories
        await io.File(p.join(leftTempDir.path, 'test_file_left.txt')).writeAsString('Left pane test file');
        await io.File(p.join(rightTempDir.path, 'test_file_right.txt')).writeAsString('Right pane test file');

        // Use the default RootStore creation but override the initial directories
        final rootStore = await RootStore.create();

        // Override the initial directories after creation
        rootStore.allPanesStore.left.historyStore.addWithPath(RawPath(leftTempDir.path));
        rootStore.allPanesStore.right.historyStore.addWithPath(RawPath(rightTempDir.path));

        // Pump the app
        await tester.pumpWidget(App(rootStore));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Verify the app loaded successfully
        expect(find.byType(App), findsOneWidget);

        // Find the FileList widgets
        final fileLists = find.byType(FileList);
        expect(fileLists, findsNWidgets(2));

        // Wait a bit more for focus to settle
        await tester.pumpAndSettle(const Duration(milliseconds: 100));

        if (kDebugMode) {
          print('Left pane is source: ${rootStore.allPanesStore.left.isSource}');
          print('Right pane is source: ${rootStore.allPanesStore.right.isSource}');
        }

        // Verify that the left pane is marked as the source pane (this is the main requirement)
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

        // Test that keyboard navigation works by pressing arrow keys
        // If the FileList is properly focused, arrow keys should change the focused row
        final initialFocusedIndex = rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;

        if (kDebugMode) {
          print('Initial focused row index: $initialFocusedIndex');
        }

        // Press down arrow to move focus to next row (if there are multiple files)
        await tester.sendKeyEvent(LogicalKeyboardKey.arrowDown);
        await tester.pumpAndSettle(const Duration(milliseconds: 200));

        final newFocusedIndex = rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;

        if (kDebugMode) {
          print('New focused row index after arrow down: $newFocusedIndex');
        }

        // If there are multiple files, the focused index should have changed
        // If there's only one file, it should stay the same
        final fileCount = rootStore.allPanesStore.left.directoryViewStore.files.length;
        if (fileCount > 1) {
          expect(newFocusedIndex, isNot(equals(initialFocusedIndex)),
                 reason: 'Arrow key should change focused row when FileList has focus');
        } else {
          // With only one file, the index should stay the same
          expect(newFocusedIndex, equals(initialFocusedIndex),
                 reason: 'With only one file, focused index should remain the same');
        }

        // Also verify that the left pane is marked as the source pane
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));
      } finally {
        // Clean up temporary directories
        if (await tempBaseDir.exists()) {
          await tempBaseDir.delete(recursive: true);
        }
      }
    });

    testWidgets('Tab key should switch widget focus between FileList widgets', (WidgetTester tester) async {
      // Create temporary directories for testing
      final tempBaseDir = await io.Directory.systemTemp.createTemp('qfiler_tab_focus_test_');
      final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
      final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();

      try {
        // Create some test files in both directories
        await io.File(p.join(leftTempDir.path, 'test_file_left.txt')).writeAsString('Left pane test file');
        await io.File(p.join(rightTempDir.path, 'test_file_right.txt')).writeAsString('Right pane test file');

        // Use the default RootStore creation but override the initial directories
        final rootStore = await RootStore.create();

        // Override the initial directories after creation
        rootStore.allPanesStore.left.historyStore.addWithPath(RawPath(leftTempDir.path));
        rootStore.allPanesStore.right.historyStore.addWithPath(RawPath(rightTempDir.path));

        // Pump the app
        await tester.pumpWidget(App(rootStore));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Find the FileList widgets
        final fileLists = find.byType(FileList);
        expect(fileLists, findsNWidgets(2));

        // Wait for focus to settle
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        // Initially, left should be the source pane
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

        // Press Tab key
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        if (kDebugMode) {
          print('After Tab - Source pane: ${rootStore.allPanesStore.sourcePaneSide}');
          print('After Tab - Left pane is source: ${rootStore.allPanesStore.left.isSource}');
          print('After Tab - Right pane is source: ${rootStore.allPanesStore.right.isSource}');
        }

        // After Tab, right should be the source pane
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.right));

        // Test that the right pane now responds to keyboard input
        final rightInitialFocusedIndex = rootStore.allPanesStore.right.directoryViewStore.focusedRowIndex;
        await tester.sendKeyEvent(LogicalKeyboardKey.arrowDown);
        await tester.pumpAndSettle(const Duration(milliseconds: 200));

        // The right pane should respond to keyboard input now
        final rightFileCount = rootStore.allPanesStore.right.directoryViewStore.files.length;
        if (rightFileCount > 1) {
          final rightNewFocusedIndex = rootStore.allPanesStore.right.directoryViewStore.focusedRowIndex;
          expect(rightNewFocusedIndex, isNot(equals(rightInitialFocusedIndex)),
                 reason: 'Right pane should respond to keyboard input when it has focus');
        }

        // Press Tab again
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        // Should be back to left
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

        if (kDebugMode) {
          print('After second Tab - Source pane: ${rootStore.allPanesStore.sourcePaneSide}');
        }
      } finally {
        // Clean up temporary directories
        if (await tempBaseDir.exists()) {
          await tempBaseDir.delete(recursive: true);
        }
      }
    });
  });
}
