#[cfg(test)]
mod tests {
    use crate::api::domain::{CopyOptions, ProgressReport};
    use crate::api::error::FileSystemError; // Restored
    use crate::api::file_system;
    use std::sync::Arc;
    use chrono::{DateTime, TimeZone, Utc}; // Duration unused in this test, DateTime is used
    use filetime::FileTime; // set_file_handle_times unused in this test
    use flutter_rust_bridge::{DartFnFuture}; // Changed frb to SyncReturn
    use tokio::runtime::Runtime; // Needed for older tests
    // Removed StdFile, Write, PathBuf, Arc, Mutex as they are not used in test_copy_with_progress_preserves_timestamps
    // Add them back if other tests in this module require them in the common block.
    use tempfile::tempdir;
    use tokio::fs as tokio_fs;

    #[test]
    fn test_list_on_file_path_returns_not_a_directory_error() {
        let rt = tokio::runtime::Runtime::new().unwrap(); // Tokio runtime for async operations
        rt.block_on(async {
            let dir = tempdir().expect("Failed to create temporary directory");
            let file_path = dir.path().join("a_file.txt");

            // Create an empty file
            tokio_fs::write(&file_path, b"test content")
                .await
                .expect("Failed to create test file");

            // 2. Call the underlying operation that `list` uses, which is tokio::fs::read_dir.
            let path_str = file_path.to_str().unwrap().to_string();
            let result = tokio::fs::read_dir(&path_str).await;

            // 3. Assert the error.
            match result {
                Ok(_) => panic!("tokio::fs::read_dir should have failed for a file path, but it succeeded."),
                Err(io_err) => {
                    // Convert std::io::Error to FileSystemError as it happens in your `list` function's `?` operator
                    let fs_error: FileSystemError = io_err.into();
                    match fs_error {
                        FileSystemError::NotADirectory { message: _ } => {
                            // Test passed - we got the expected NotADirectory error
                        }
                        _ => panic!(
                            "Expected FileSystemError::NotADirectory, but got a different error type: {:?}",
                            fs_error
                        ),
                    }
                }
            }

            // Clean up: tempdir will be removed when `dir` goes out of scope automatically.
        });
    }

    // #[test]
    // fn test_copies_files_with_overwrite_control() {
    //     let rt = Runtime::new().unwrap();
    //     rt.block_on(async {
    //         let dir = tempdir().expect("Failed to create temporary directory");
    //         let source_path_buf = dir.path().join("source.txt");
    //         let dest_path_buf = dir.path().join("dest.txt");

    //         let source_path = source_path_buf.to_str().unwrap().to_string();
    //         let dest_path = dest_path_buf.to_str().unwrap().to_string();

    //         const CONTENT1: &str = "test content v1";
    //         tokio_fs::write(&source_path, CONTENT1)
    //             .await
    //             .expect("Failed to write initial source file");

    //         let copy_options_no_overwrite = CopyOptions {
    //             overwrite_if_exists: false,
    //             preserve_timestamps: true,
    //             preserve_metadata: false,
    //         };

    //         // 1. Initial copy, dest does not exist
    //         let result1 = file_system::copy_file(
    //             source_path.clone(),
    //             dest_path.clone(),
    //             copy_options_no_overwrite.clone(),
    //         )
    //         .await;
    //         assert!(result1.is_ok(), "Initial copy failed: {:?}", result1.err());
    //         let dest_content1 = tokio_fs::read_to_string(&dest_path)
    //             .await
    //             .expect("Failed to read dest file after initial copy");
    //         assert_eq!(
    //             dest_content1, CONTENT1,
    //             "Dest content mismatch after initial copy"
    //         );

    //         // 2. Attempt to copy again with overwrite_if_exists: false, dest exists
    //         const CONTENT2: &str = "test content v2";
    //         tokio_fs::write(&source_path, CONTENT2)
    //             .await
    //             .expect("Failed to write updated source file");

    //         let result2 = file_system::copy_file(
    //             source_path.clone(),
    //             dest_path.clone(),
    //             copy_options_no_overwrite.clone(),
    //         )
    //         .await;
    //         assert!(
    //             result2.is_err(),
    //             "Copy should have failed due to existing file"
    //         );
    //         match result2.err().unwrap() {
    //             FileSystemError::AlreadyExists { message: _ } => {
    //                 // Expected error
    //             }
    //             e => panic!("Expected FileSystemError::AlreadyExists, but got {:?}", e),
    //         }
    //         // Verify dest content hasn't changed
    //         let dest_content_after_failed_copy = tokio_fs::read_to_string(&dest_path)
    //             .await
    //             .expect("Failed to read dest file after failed copy attempt");
    //         assert_eq!(
    //             dest_content_after_failed_copy, CONTENT1,
    //             "Dest content should not have changed after failed copy"
    //         );

    //         // 3. Copy with overwrite_if_exists: true
    //         let copy_options_overwrite = CopyOptions {
    //             overwrite_if_exists: true,
    //             preserve_timestamps: true,
    //             preserve_metadata: false,
    //         };
    //         let result3 = file_system::copy_file(
    //             source_path.clone(),
    //             dest_path.clone(),
    //             copy_options_overwrite,
    //         )
    //         .await;
    //         assert!(
    //             result3.is_ok(),
    //             "Copy with overwrite failed: {:?}",
    //             result3.err()
    //         );
    //         let dest_content3 = tokio_fs::read_to_string(&dest_path)
    //             .await
    //             .expect("Failed to read dest file after overwrite copy");
    //         assert_eq!(
    //             dest_content3, CONTENT2,
    //             "Dest content mismatch after overwrite copy"
    //         );

    //         // Clean up: tempdir will be removed when `dir` goes out of scope automatically.
    //     });
    // }

    // #[tokio::test]
    // async fn test_copy_with_progress_preserves_timestamps() {
    //     // 1. Setup
    //     let dir = tempdir().expect("Failed to create temporary directory");
    //     let source_path_buf = dir.path().join("source_preserve_ts_prog.dat");
    //     let dest_path_buf = dir.path().join("dest_preserve_ts_prog.dat");

    //     let source_path_str = source_path_buf.to_str().unwrap().to_string();
    //     let dest_path_str = dest_path_buf.to_str().unwrap().to_string();

    //     const SOURCE_CONTENT: &str = "content for preserve timestamps test with progress rust";
    //     tokio_fs::write(&source_path_buf, SOURCE_CONTENT)
    //         .await
    //         .expect("Failed to write source file");

    //     // Define and set source timestamp (matches Dart: DateTime.utc(2023, 3, 3, 12, 0, 0, 0))
    //     let source_timestamp_dt = Utc.with_ymd_and_hms(2023, 3, 3, 12, 0, 0).unwrap();
    //     let source_timestamp_ft = FileTime::from_system_time(source_timestamp_dt.into());

    //     file_system::set_timestamps(
    //         source_path_str.clone(),
    //         source_timestamp_dt, // createTime
    //         source_timestamp_dt, // modifyTime
    //         source_timestamp_dt, // accessTime
    //     )
    //     .await
    //     .expect("Failed to set source timestamps");

    //     // Re-stat source to confirm setTimestamps worked (using tokio::fs::metadata for direct check)
    //     let source_meta_after_set = tokio_fs::metadata(&source_path_buf)
    //         .await
    //         .expect("Failed to get source metadata after set");

    //     assert_eq!(
    //         FileTime::from_last_modification_time(&source_meta_after_set),
    //         source_timestamp_ft,
    //         "Source modify time mismatch after set"
    //     );
    //     assert_eq!(
    //         FileTime::from_last_access_time(&source_meta_after_set),
    //         source_timestamp_ft,
    //         "Source access time mismatch after set"
    //     );

    //     // 2. Copy Operation
    //     const CHUNK_SIZE: u32 = 8;
    //     let copy_options = CopyOptions {
    //         overwrite_if_exists: false,
    //         preserve_timestamps: true,
    //         preserve_metadata: false,
    //     };

    //     let handle = file_system::prepare_copy_with_progress(
    //         source_path_str.clone(),
    //         dest_path_str.clone(),
    //         CHUNK_SIZE,
    //         copy_options.clone(),
    //     )
    //     .await
    //     .expect("Failed to prepare copy operation");

    //     let no_progress_callback: Box<dyn Fn(ProgressReport) -> DartFnFuture<()> + Send + Sync + 'static> = Box::new(|_| SyncReturn(()));
    //     file_system::execute_copy_with_progress(handle.clone(), no_progress_callback)
    //         .await
    //         .expect("execute_copy_with_progress failed");

    //     let dest_stats_result = file_system::stat(dest_path_str.clone()).await;
    //     assert!(
    //         dest_stats_result.error.is_none(),
    //         "Error in dest_stats: {:?}",
    //         dest_stats_result.error
    //     );

    //     assert_eq!(
    //         dest_stats_result.create_time, source_timestamp_dt,
    //         "Destination createTime mismatch (from our stat)"
    //     );
    //     assert_eq!(
    //         dest_stats_result.modify_time, source_timestamp_dt,
    //         "Destination modifyTime mismatch (from our stat)"
    //     );
    //     assert_eq!(
    //         dest_stats_result.access_time, source_timestamp_dt,
    //         "Destination accessTime mismatch (from our stat)"
    //     );
    // }
}
