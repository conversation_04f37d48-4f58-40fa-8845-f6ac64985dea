import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../util/context_extensions.dart';
import 'command.dart';
import 'command_context.dart';

// Global key to access the command palette state
final commandPaletteKey = GlobalKey<CommandPaletteState>();

/// Widget that displays a command palette for executing commands
class CommandPalette extends StatefulWidget {
  final Widget child;

  const CommandPalette({
    super.key,
    required this.child,
  });

  @override
  State<CommandPalette> createState() => CommandPaletteState();
}

/// Note: We're keeping this as a StatefulWidget instead of HookWidget
/// because we need to expose methods to the global key

class CommandPaletteState extends State<CommandPalette> {
  bool _isOpen = false;
  String _searchQuery = '';

  // Text controller for the search field
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final FocusNode _commandPaletteFocusNode = FocusNode();

  // List of filtered commands based on search query
  final List<Command> _filteredCommands = [];

  // Selected command index
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();

    // Listen for changes to the search query
    _searchController.addListener(_onSearchChanged);

    // Listen for focus changes
    _searchFocusNode.addListener(() {
      if (!_searchFocusNode.hasFocus && _isOpen) {
        _closeCommandPalette();
      }
    });

    // Listen for CommandPalette focus changes
    _commandPaletteFocusNode.addListener(() {
      if (kDebugMode) {
        print('CommandPalette Focus: hasFocus=${_commandPaletteFocusNode.hasFocus}');
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Update filtered commands
    _filterCommands();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    _commandPaletteFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterCommands();
    });
  }

  void _filterCommands() {
    _filteredCommands.clear();
    final results = context.commandRepository.searchCommands(_searchQuery);

    // Convert QuickScoreResult<Command> to Command
    _filteredCommands.addAll(results.map((result) => result.item));

    // Reset selection
    _selectedIndex = _filteredCommands.isNotEmpty ? 0 : -1;
  }

  // Public method to open the command palette
  void openCommandPalette() {
    // Update the command context
    context.commandContextRepository.pushContext(const CommandContext(inCommandPalette: true));

    setState(() {
      _isOpen = true;
      _searchQuery = '';
      _searchController.clear();
      _filterCommands();
    });

    // Focus the search field
    Future.microtask(() => _searchFocusNode.requestFocus());
  }

  // Private method for internal use
  void _openCommandPalette() {
    openCommandPalette();
  }

  void _closeCommandPalette() {
    context.commandContextRepository.popContext(const CommandContext(inCommandPalette: true));

    setState(() {
      _isOpen = false;
    });
  }

  void _selectNextCommand() {
    if (_filteredCommands.isEmpty) return;

    setState(() {
      _selectedIndex = (_selectedIndex + 1) % _filteredCommands.length;
    });
  }

  void _selectPreviousCommand() {
    if (_filteredCommands.isEmpty) return;

    setState(() {
      _selectedIndex = (_selectedIndex - 1 + _filteredCommands.length) % _filteredCommands.length;
    });
  }

  void _executeSelectedCommand() {
    if (_filteredCommands.isEmpty || _selectedIndex < 0 || _selectedIndex >= _filteredCommands.length) {
      return;
    }

    final command = _filteredCommands[_selectedIndex];
    _closeCommandPalette();

    // Dispatch the command
    context.commandDispatcher.dispatchCommand(command);
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('CommandPalette: Building with _isOpen=$_isOpen');
    }

    return Focus(
      autofocus: false,
      canRequestFocus: false, // Changed to false to prevent stealing focus
      focusNode: _commandPaletteFocusNode,
      onKeyEvent: (node, event) {
        if (kDebugMode && event is KeyDownEvent) {
          print('CommandPalette: Received key event: ${event.logicalKey.keyLabel}, hasFocus=${node.hasFocus}');
        }
        if (event is KeyDownEvent) {
          // Check for Ctrl+Shift+P to open the command palette
          if (event.logicalKey == LogicalKeyboardKey.keyP &&
              HardwareKeyboard.instance.isControlPressed &&
              HardwareKeyboard.instance.isShiftPressed) {
            _openCommandPalette();
            return KeyEventResult.handled;
          }

          // Handle keyboard navigation when the palette is open
          if (_isOpen) {
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              _closeCommandPalette();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
              _selectNextCommand();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
              _selectPreviousCommand();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.enter) {
              _executeSelectedCommand();
              return KeyEventResult.handled;
            }
          }
        }
        return KeyEventResult.ignored;
      },
      child: Stack(
        children: [
          widget.child,
          if (_isOpen)
            GestureDetector(
              onTap: _closeCommandPalette,
              behavior: HitTestBehavior.opaque,
              child: Container(
                color: Colors.black54,
                child: Center(
                  child: GestureDetector(
                    onTap: () {}, // Prevent closing when clicking on the dialog
                    child: Material(
                      elevation: 10,
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 600,
                        constraints: BoxConstraints(
                          maxHeight: MediaQuery.of(context).size.height * 0.8,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Search field
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: TextField(
                                controller: _searchController,
                                focusNode: _searchFocusNode,
                                decoration: InputDecoration(
                                  hintText: 'Search commands...',
                                  prefixIcon: const Icon(Icons.search),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                onSubmitted: (_) => _executeSelectedCommand(),
                              ),
                            ),
                            // Command list
                            Expanded(
                              child: ListView.builder(
                                itemCount: _filteredCommands.length,
                                itemBuilder: (context, index) {
                                  final command = _filteredCommands[index];

                                  // Get the keybind for this command
                                  final keybind = context.keyBindManager.getKeybindForCommand(command.id);
                                  final shortcut = keybind?.key ?? '';

                                  return ListTile(
                                    selected: index == _selectedIndex,
                                    selectedTileColor: Theme.of(context).hoverColor,
                                    title: Text(command.label),
                                    subtitle: Text(command.group),
                                    trailing: Text(
                                      shortcut,
                                      style: TextStyle(
                                        color: Theme.of(context).hintColor,
                                        fontFamily: 'monospace',
                                      ),
                                    ),
                                    onTap: () {
                                      setState(() {
                                        _selectedIndex = index;
                                      });
                                      _executeSelectedCommand();
                                    },
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
