<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JsonSchemaMappingsProjectConfiguration">
    <state>
      <map>
        <entry key="dart-build">
          <value>
            <SchemaInfo>
              <option name="generatedName" value="New Schema" />
              <option name="name" value="dart-build" />
              <option name="relativePathToSchema" value="https://json.schemastore.org/dart-build.json" />
              <option name="patterns">
                <list>
                  <Item>
                    <option name="path" value="build.yaml" />
                  </Item>
                </list>
              </option>
            </SchemaInfo>
          </value>
        </entry>
      </map>
    </state>
  </component>
</project>