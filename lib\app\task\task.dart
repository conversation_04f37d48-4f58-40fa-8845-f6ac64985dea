import 'package:mobx/mobx.dart';
import 'package:tsid_dart/tsid_dart.dart';

import '../domain/file.dart';
import '../domain/path.dart';
import '../domain/pausable.dart';
import '../domain/user_choice.dart';
import '../file_system/file_system.dart';
import '../util/log_utils.dart';
import 'progress.dart';

part '.gen/task.g.dart';

// Size threshold for pausable operations (4MB)
const int pausableThreshold = 4 * 1024 * 1024;

/// Represents the type of a file operation task
enum TaskType {
  copy,
  move,
  rename,
  delete,
  create,
  calcDirectorySize,
}

/// Represents the user's initial, unprocessed request.
/// This object captures what the user wants to achieve (e.g., "copy /src/folderA to /dest", "move fileX and fileY to /backup").
/// It contains all the necessary details provided by the user before any planning or decomposition occurs.
sealed class JobSpec {
  JobSpec._(this.type);

  /// The type of the job
  final TaskType type;

  /// Creates a new copy job specification
  factory JobSpec.copy({required List<File> files, required RawPath destDir}) = CopyJobSpec;

  /// Creates a new move job specification
  factory JobSpec.move({required List<File> files, required RawPath destDir}) = MoveJobSpec;

  /// Creates a new rename job specification
  factory JobSpec.rename({required File file, required String newName}) = RenameJobSpec;

  /// Creates a new delete job specification
  factory JobSpec.delete({required List<File> files}) = DeleteJobSpec;

  /// Creates a new create job specification
  factory JobSpec.create({required File file}) = CreateJobSpec;

  /// Creates a new directory size calculation job specification
  factory JobSpec.calcDirectorySize({required List<File> files}) = CalcDirectorySizeJobSpec;

  @override
  String toString() => "JobSpec($type)";
}

/// Abstract base class for JobSpecs that have source files
sealed class SourceFilesJobSpec extends JobSpec {
  /// The source files for this task
  final List<File> sources;

  SourceFilesJobSpec._(super.type, this.sources) : super._();
}

/// Abstract base class for JobSpecs that have source and destination
sealed class SourceDestJobSpec extends SourceFilesJobSpec {
  /// The destination directory for copy/move tasks
  final RawPath destinationDir;

  SourceDestJobSpec._(super.type, super.sources, this.destinationDir) : super._();
}

/// Abstract base class for JobSpecs that have a single source file
sealed class SourceFileJobSpec extends JobSpec {
  /// The source file for this task
  final File source;

  SourceFileJobSpec._(super.type, this.source) : super._();
}

/// Represents a copy job specification
final class CopyJobSpec extends SourceDestJobSpec {
  CopyJobSpec({required List<File> files, required RawPath destDir}) : super._(TaskType.copy, files, destDir);
}

/// Represents a move job specification
final class MoveJobSpec extends SourceDestJobSpec {
  MoveJobSpec({required List<File> files, required RawPath destDir}) : super._(TaskType.move, files, destDir);
}

/// Represents a rename job specification
final class RenameJobSpec extends SourceFileJobSpec {
  /// The new name for rename tasks
  final String newName;

  RenameJobSpec({required File file, required this.newName}) : super._(TaskType.rename, file);
}

/// Represents a delete job specification
final class DeleteJobSpec extends SourceFilesJobSpec {
  DeleteJobSpec({required List<File> files}) : super._(TaskType.delete, files);
}

/// Represents a create job specification
final class CreateJobSpec extends SourceFileJobSpec {
  CreateJobSpec({required File file}) : super._(TaskType.create, file);
}

/// Represents a directory size calculation job specification
final class CalcDirectorySizeJobSpec extends SourceFilesJobSpec {
  CalcDirectorySizeJobSpec({required List<File> files}) : super._(TaskType.calcDirectorySize, files);
}

/// Represents a Job that contains multiple Tasks
/// A Job is created based on a JobSpec during the planning phase
/// It acts as the primary container and manager for the work requested
class Job extends JobBase with _$Job {
  Job({required super.spec});

  @override
  String toString() => "[$id]Job($type)";
}

abstract class JobBase with Store implements Pausable {
  JobBase({required this.spec});

  /// The specification for this job
  final JobSpec spec;

  /// The ID of the job
  final Tsid id = Tsid.getTsid();

  /// Whether the job is paused
  @observable
  bool paused = false;

  /// Whether the job is cancelled
  @observable
  bool cancelled = false;

  /// Whether the job is completed
  @observable
  bool completed = false;

  /// The error that occurred during job execution, if any
  @observable
  Object? error;

  /// Whether the job is currently running
  @computed
  bool get isRunning => !completed && !cancelled && error == null;

  /// The tasks contained in this job
  ObservableList<Task> tasks = ObservableList<Task>();

  /// The overwrite mode for all operations in this job
  @observable
  OverwriteMode? overwriteModeForAll;

  /// The error mode for all operations in this job
  @observable
  OperationErrorMode? errorModeForAll;

  /// Whether the previous overwrite all option was selected
  bool get previousOverwriteAllSelected => overwriteModeForAll != null;

  /// Whether the previous error all option was selected
  bool get previousErrorAllSelected => errorModeForAll != null;

  /// The cumulative progress of all tasks in this job
  final Progress progress = Progress();

  /// Reports progress from a task to this job
  @action
  void reportProgress(Progress taskProgress) {
    progress.addTotals(taskProgress);
    progress.addBytesProcessed(taskProgress.bytesProcessed);
    progress.addFilesProcessed(taskProgress.filesProcessed);
  }

  /// Future that completes when the job is ready
  // final _readyCompleter = Completer<void>();
  // Future<void> get awaitUntilReady => _readyCompleter.future;

  /// Adds a task to this job
  @action
  void addTask(Task task) {
    tasks.add(task);
  }

  /// Gets the current type of this job
  TaskType get type => spec.type;

  /// Marks the job as ready
  // @action
  // void markReady() {
  //   if (!_readyCompleter.isCompleted) {
  //     _readyCompleter.complete();
  //   }
  // }

  /// Marks the job as complete
  @action
  void complete() {
    completed = true;
  }

  /// Cancels the job
  @override
  @action
  void cancel() {
    cancelled = true;
    for (final task in tasks) {
      task.cancel();
    }
  }

  /// Pauses the job
  @override
  @action
  void pause() {
    paused = true;
    for (final task in tasks) {
      task.pause();
    }
  }

  /// Resumes the job
  @override
  @action
  void resume() {
    paused = false;
    for (final task in tasks) {
      task.resume();
    }
  }

  /// Checks if all tasks are paused and updates job state
  @action
  void checkPaused() {
    if (tasks.isEmpty) return;
    paused = tasks.every((task) => task.paused);
  }

  @action
  void setError(Object? error) {
    error = error;
  }

  /// Sets the overwrite mode for all operations
  @action
  void setOverwriteModeForAll(OverwriteMode mode) {
    overwriteModeForAll = mode;
  }

  /// Sets the error mode for all operations
  @action
  void setErrorModeForAll(OperationErrorMode mode) {
    errorModeForAll = mode;
  }

  static final logger = loggerFor(Job);
}

/// Represents a single, logical unit of work within a Job
/// A Task is a node in the planned execution tree, containing Operations
class Task extends TaskBase with _$Task {
  Task({
    required super.job,
    required super.file,
    super.synthetic,
  });
}

abstract class TaskBase with Store implements Pausable {
  TaskBase({
    required this.job,
    required this.file,
    this.synthetic = false,
  });

  /// Unique identifier for the task.
  final Tsid id = Tsid.getTsid();

  /// The file associated with this task (directory or file)
  final File file;

  /// The parent Job this task belongs to
  final Job job;

  /// Whether this is a synthetic task (grouped top-level files)
  final bool synthetic;

  /// Source file for operations that act on a single source
  File? get sourceFile => null;

  /// Destination file for operations that have a destination
  File? get destinationFile => null;

  /// The progress of this task
  final Progress progress = Progress();

  /// Whether the task is paused
  @observable
  bool paused = false;

  /// Whether the task is cancelled
  bool get cancelled => job.cancelled;

  /// Whether the task is completed
  @observable
  bool completed = false;

  /// Track whether overwriteAll was previously selected
  bool previousOverwriteAllSelected = false;

  /// The error that occurred during task execution, if any
  @observable
  Object? error;

  /// Whether the task is currently running
  @computed
  bool get isRunning => !completed && !cancelled && job.isRunning;

  /// The operations contained in this task
  List<Operation> rootOps = [];

  /// Operations that are currently being executed
  ObservableList<Operation>? currentOps;

  /// The current pausable operation that can be paused/resumed
  CopyWithProgressOperation? _currentPausableOperation;

  /// Gets the type of this task from the parent job
  @computed
  TaskType get type => job.type;

  /// Reports progress from an operation to this task
  @action
  void reportProgress(Progress operationProgress) {
    progress.addTotals(operationProgress);
    progress.addBytesProcessed(operationProgress.bytesProcessed);
    progress.addFilesProcessed(operationProgress.filesProcessed);
    job.reportProgress(operationProgress); // Report to parent job
  }

  /// Set the current operations
  @action
  void setCurrentOps(ObservableList<Operation>? ops) {
    currentOps = ops;
  }

  /// Adds an operation to this task
  @action
  void addOperation(Operation operation) {
    rootOps.add(operation);
  }

  /// Sets the operations for this task
  @action
  void setOperations(List<Operation> ops) {
    rootOps.clear();
    rootOps.addAll(ops);
  }

  /// Sets the current pausable operation
  void setCurrentPausableOperation(CopyWithProgressOperation? operation) {
    _currentPausableOperation = operation;
  }

  /// Marks the task as complete
  @action
  void complete() {
    completed = true;
  }

  /// Pauses the task
  @override
  @action
  void pause() {
    if (!paused) {
      paused = true;
      // Pause the current pausable operation if it exists
      _currentPausableOperation?.pause();
    }
  }

  /// Resumes the task
  @override
  @action
  void resume() {
    if (paused) {
      paused = false;
      // Resume the current pausable operation if it exists
      _currentPausableOperation?.resume();
    }
  }

  /// Cancels the task
  @override
  @action
  void cancel() {
    if (!cancelled) {
      // Cancel the current pausable operation if it exists
      _currentPausableOperation?.cancel();
    }
  }

  /// Sets the overwrite mode for all operations
  void setOverwriteModeForAll(OverwriteMode mode) {
    job.setOverwriteModeForAll(mode);
  }

  OverwriteMode? get overwriteModeForAll => job.overwriteModeForAll;

  /// Sets the error mode for all operations
  void setErrorModeForAll(OperationErrorMode mode) {
    job.setErrorModeForAll(mode);
  }

  OperationErrorMode? get errorModeForAll => job.errorModeForAll;

  @override
  String toString() => "[$id]Task(${job.type} $file)";
}

/// Status of an operation
enum OperationStatus {
  /// Operation is waiting to be executed
  waiting,

  /// Operation is currently being executed
  executing,

  /// Operation has been completed successfully
  completed,

  /// Operation has been skipped
  skipped,

  /// Operation has been cancelled
  cancelled,

  /// Operation has failed with an error
  error,
}

/// Represents an atomic file operation
/// Operations are the most granular units of execution
sealed class Operation {
  Operation({
    required this.task,
    required this.source,
    this.destination,
    this.parent,
    this.children,
    this.size = 0,
  });

  final Tsid id = Tsid.getTsid();
  final Task task;

  final File source;
  final RawPath? destination;

  final Operation? parent;

  /// List of child operations (for directories)
  List<Operation>? children;

  /// Progress tracking for this operation
  final Progress progress = Progress();

  /// File or cumulative dir size
  int size = 0;

  Object? get error => progress.error;

  RawPath? get pathToAdd;
  RawPath? get pathToRemove;

  //////////////////////////////////////// Mobx Boilerplate ////////////////////////////////////////
  // Due to issues with sealed type exhaustive checking in dart, we cannot mix-in mobx observables to this class.
  // We must implement everything related to observables manually.
  OperationStatus _status = OperationStatus.waiting;
  late final _$statusAtom = Atom(name: 'Operation.status', context: mainContext);

  OperationStatus get status {
    _$statusAtom.reportRead();
    return _status;
  }

  set status(OperationStatus value) {
    _$statusAtom.reportWrite(value, _status, () {
      _status = value;
    });
  }

  late final _$OperationActionController = ActionController(name: 'Operation', context: mainContext);

  void setStatus(OperationStatus status, {Object? error}) {
    final $actionInfo = _$OperationActionController.startAction(name: 'Operation.setStatus');
    try {
      this.status = status;
      switch (status) {
        case OperationStatus.waiting:
          progress.setStatus(ProgressStatus.initialized, error);
        case OperationStatus.executing:
          progress.setStatus(ProgressStatus.running, error);
        case OperationStatus.completed || OperationStatus.skipped || OperationStatus.cancelled || OperationStatus.error:
          progress.setStatus(ProgressStatus.finished, error);
      }
    } finally {
      _$OperationActionController.endAction($actionInfo);
    }
  }

  //////////////////////////////////////// Mobx Boilerplate ////////////////////////////////////////

  void setChildren(List<Operation> ops) {
    if (children != null) {
      throw Exception('Cannot set children for operation that already has children');
    }
    children = ops;
  }

  // @action
  // void setProgress(int bytesProcessed, {int? bytesTotal}) {
  //   progress.setBytesTotal(bytesTotal)
  //   progress = progress.copyWith(
  //       bytesProcessed: bytesProcessed, totalBytes: bytesTotal);
  //   // Optionally update task progress directly if needed, but TaskProgress recalculates based on ops
  // }

  // Method to update the parent Task's progress based on this operation and its children
  // void _updateTaskProgress() {
  //   task.recalculateProgress();
  // }

  // @action
  // void setStatus(OperationStatus status, {Object? error}) {
  //   this.status = status;
  //   if (error != null) {
  //     setError(error);
  //   }
  // }

  // Clean up when the operation is fully disposed
  void dispose() {
    if (children != null) {
      for (final child in children!) {
        child.dispose();
      }
    }
  }

  @override
  String toString() {
    return "$runtimeType(${source.path}${destination != null ? ' -> ${destination!}' : ''})${children?.isEmpty ?? true ? '' : '(children: ${children!.length})'}";
  }
}

sealed class SourceDestOperation extends Operation {
  SourceDestOperation({
    required super.task,
    required super.source,
    required super.destination,
    super.parent,
    List<SourceDestOperation>? children,
    super.size,
  }) : super(children: children ?? []);

  @override
  RawPath get destination => super.destination!;
  @override
  List<SourceDestOperation>? get children => super.children?.cast<SourceDestOperation>();
}

class CopyOperation extends SourceDestOperation {
  CopyOperation({
    required super.task,
    required super.source,
    required super.destination,
    super.parent,
    super.size,
    List<CopyOperation>? children,
  }) : super(children: children ?? []);

  @override
  List<CopyOperation>? get children => super.children?.cast<CopyOperation>();
  @override
  RawPath? get pathToAdd => destination;
  @override
  RawPath? get pathToRemove => null;
}

class MoveOperation extends SourceDestOperation {
  MoveOperation({
    required super.task,
    required super.source,
    required super.destination,
    super.parent,
    List<MoveOperation>? children,
    super.size,
  }) : super(children: children ?? []);

  @override
  List<MoveOperation>? get children => super.children?.cast<MoveOperation>();
  @override
  RawPath? get pathToAdd => destination;
  @override
  RawPath? get pathToRemove => source.path;
}

class DeleteOperation extends Operation {
  DeleteOperation({
    required super.task,
    required super.source,
    super.parent,
    List<DeleteOperation>? children,
    super.size,
  }) : super(children: children ?? []);

  @override
  List<DeleteOperation>? get children => super.children?.cast<DeleteOperation>();
  @override
  RawPath? get pathToAdd => null;
  @override
  RawPath? get pathToRemove => source.path;
}

class RenameOperation extends SourceDestOperation {
  RenameOperation({
    required super.task,
    required super.source,
    required this.newName,
    super.parent,
    List<RenameOperation>? children,
  }) : super(destination: source.path.sibling(newName), children: children ?? []);

  final String newName;

  @override
  List<RenameOperation>? get children => super.children?.cast<RenameOperation>();
  @override
  RawPath? get pathToAdd => destination;
  @override
  RawPath? get pathToRemove => source.path;
}

class CreateFileOperation extends Operation {
  CreateFileOperation({
    required super.task,
    required super.source,
    super.parent,
    List<CreateFileOperation>? children,
    super.size,
  }) : super(children: children ?? []);

  @override
  List<CreateFileOperation>? get children => super.children?.cast<CreateFileOperation>();
  @override
  RawPath? get pathToAdd => source.path;
  @override
  RawPath? get pathToRemove => null;
}

class CalcDirectorySizeOperation extends Operation {
  CalcDirectorySizeOperation({
    required super.task,
    required super.source,
    super.parent,
    List<CalcDirectorySizeOperation>? children,
    super.size,
  }) : super(children: children ?? []);

  @override
  List<CalcDirectorySizeOperation>? get children => super.children?.cast<CalcDirectorySizeOperation>();
  @override
  RawPath? get pathToAdd => null;
  @override
  RawPath? get pathToRemove => null;
}

/// Error thrown when a task or job is cancelled
class CancellationError implements Exception {
  @override
  String toString() {
    return 'Operation was cancelled';
  }
}
