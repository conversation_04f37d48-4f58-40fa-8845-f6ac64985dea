<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AbstractClassExtendsConcreteClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractMethodCallInConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractMethodOverridesConcreteMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractValueInTrait" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AccessToNonThreadSafeStaticFieldFromInstance" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="nonThreadSafeClasses">
        <value />
      </option>
      <option name="nonThreadSafeTypes" value="" />
    </inspection_tool>
    <inspection_tool class="AccessToStaticFieldLockedOnInstance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AmbiguousFieldAccess" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AmbiguousMethodCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AnnotationNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
      <option name="m_minLength" value="8" />
      <option name="m_maxLength" value="64" />
    </inspection_tool>
    <inspection_tool class="AnonymousClassComplexity" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="3" />
    </inspection_tool>
    <inspection_tool class="AnonymousClassMethodCount" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="1" />
    </inspection_tool>
    <inspection_tool class="AnonymousClassVariableHidesContainingMethodVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ArchaicSystemPropertyAccess" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ArrayEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ArraySizeCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssertEqualsCalledOnArray" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssertEqualsMayBeAssertSame" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssertsWithoutMessages" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssignmentOrReturnOfFieldWithMutableType" enabled="true" level="INFO" enabled_by_default="true" />
    <inspection_tool class="AssignmentToCollectionFieldFromParameter" enabled="true" level="INFO" enabled_by_default="true">
      <option name="ignorePrivateMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="AssignmentToDateFieldFromParameter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignorePrivateMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="AssignmentToForLoopParameter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_checkForeachParameters" value="false" />
    </inspection_tool>
    <inspection_tool class="AssignmentToMethodParameter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreTransformationOfOriginalParameter" value="false" />
    </inspection_tool>
    <inspection_tool class="AssignmentToNull" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssignmentToStaticFieldFromInstanceMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AwaitNotInLoop" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AwaitWithoutCorrespondingSignal" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BadOddness" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BeforeClassOrAfterClassIsPublicStaticVoidNoArg" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BeforeOrAfterIsPublicVoidNoArg" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BigDecimalEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BooleanVariableAlwaysNegated" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CallToNativeMethodWhileLocked" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CallToStringConcatCanBeReplacedByOperator" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CastConflictsWithInstanceof" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CastThatLosesPrecision" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreIntegerCharCasts" value="false" />
      <option name="ignoreOverflowingByteCasts" value="false" />
    </inspection_tool>
    <inspection_tool class="CastToConcreteClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreAbstractClasses" value="true" />
    </inspection_tool>
    <inspection_tool class="CastToIncompatibleInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ChainedEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CharUsedInArithmeticContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassComplexity" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="80" />
    </inspection_tool>
    <inspection_tool class="ClassCoupling" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_includeJavaClasses" value="false" />
      <option name="m_includeLibraryClasses" value="false" />
      <option name="m_limit" value="15" />
    </inspection_tool>
    <inspection_tool class="ClassInTopLevelPackage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassInheritanceDepth" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="4" />
    </inspection_tool>
    <inspection_tool class="ClassMayBeInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassNameDiffersFromFileName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassNameSameAsAncestorName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
      <option name="m_minLength" value="3" />
      <option name="m_maxLength" value="64" />
    </inspection_tool>
    <inspection_tool class="ClassNestingDepth" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="1" />
    </inspection_tool>
    <inspection_tool class="ClassNewInstance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassReferencesSubclass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassWithMultipleLoggers" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="loggerNamesString" value="java.util.logging.Logger,org.slf4j.Logger,org.apache.commons.logging.Log,org.apache.log4j.Logger" />
    </inspection_tool>
    <inspection_tool class="CloneCallsConstructors" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CloneInNonCloneableClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CloneableImplementsClone" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreCloneableDueToInheritance" value="false" />
    </inspection_tool>
    <inspection_tool class="CollectionContainsUrl" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CollectionsFieldAccessReplaceableByMethodCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CollectionsMustHaveInitialCapacity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ComparableImplementedButEqualsNotOverridden" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CompareToUsesNonFinalVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConditionSignal" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConfusingElse" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="reportWhenNoStatementFollow" value="false" />
    </inspection_tool>
    <inspection_tool class="ConfusingFloatingPointLiteral" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConfusingOctalEscape" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantAssertCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantDeclaredInAbstractClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantDeclaredInInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantJUnitAssertArgument" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantMathCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="onlyCheckImmutables" value="false" />
      <option name="m_regex" value="[A-Z_\d]*" />
      <option name="m_minLength" value="3" />
      <option name="m_maxLength" value="32" />
    </inspection_tool>
    <inspection_tool class="ConstantOnLHSOfComparison" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantOnWrongSideOfComparison" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantValueVariableUse" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstructorCount" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreDeprecatedConstructors" value="false" />
      <option name="m_limit" value="5" />
    </inspection_tool>
    <inspection_tool class="ContinueStatementWithLabel" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ControlFlowStatementWithoutBraces" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CovariantCompareTo" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CovariantEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CyclomaticComplexity" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="10" />
    </inspection_tool>
    <inspection_tool class="DeclareCollectionAsInterface" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreLocalVariables" value="false" />
      <option name="ignorePrivateMethodsAndFields" value="false" />
    </inspection_tool>
    <inspection_tool class="DollarSignInName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DoubleLiteralMayBeFloatLiteral" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DynamicRegexReplaceableByCompiledPattern" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EnumSwitchStatementWhichMissesCases" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="ignoreSwitchStatementsWithDefault" value="false" />
    </inspection_tool>
    <inspection_tool class="EnumeratedClassNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
      <option name="m_minLength" value="5" />
      <option name="m_maxLength" value="64" />
    </inspection_tool>
    <inspection_tool class="EnumeratedConstantNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d_]*" />
      <option name="m_minLength" value="2" />
      <option name="m_maxLength" value="32" />
    </inspection_tool>
    <inspection_tool class="EnumerationCanBeIteration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EqualsAndHashcode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EqualsCalledOnEnumConstant" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EqualsHashCodeCalledOnUrl" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ErrorRethrown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExceptionNameDoesntEndWithException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExpectedExceptionNeverThrown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExtendsConcreteCollection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExtendsThread" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExtendsUtilityClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FallthruInSwitchStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FieldAccessedSynchronizedAndUnsynchronized" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="countGettersAndSetters" value="false" />
    </inspection_tool>
    <inspection_tool class="FieldCount" enabled="true" level="INFO" enabled_by_default="true">
      <option name="m_countConstantFields" value="false" />
      <option name="m_considerStaticFinalFieldsConstant" value="false" />
      <option name="myCountEnumConstants" value="false" />
      <option name="m_limit" value="15" />
    </inspection_tool>
    <inspection_tool class="FieldHidesSuperclassField" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInvisibleFields" value="true" />
    </inspection_tool>
    <inspection_tool class="FieldMayBeStatic" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="Finalize" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreTrivialFinalizers" value="true" />
    </inspection_tool>
    <inspection_tool class="FloatingPointEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ForLoopThatDoesntUseLoopVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IfStatementWithTooManyBranches" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="3" />
    </inspection_tool>
    <inspection_tool class="IgnoredJUnitTest" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ImplicitNumericConversion" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreWideningConversions" value="true" />
      <option name="ignoreCharConversions" value="false" />
      <option name="ignoreConstantConversions" value="false" />
    </inspection_tool>
    <inspection_tool class="InconsistentLanguageLevel" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IncrementDecrementUsedAsExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InnerClassVariableHidesOuterClassVariable" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInvisibleFields" value="true" />
    </inspection_tool>
    <inspection_tool class="InstanceMethodNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="3" />
      <option name="m_maxLength" value="32" />
    </inspection_tool>
    <inspection_tool class="InstanceVariableInitialization" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="true" />
    </inspection_tool>
    <inspection_tool class="InstanceVariableNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="2" />
      <option name="m_maxLength" value="32" />
    </inspection_tool>
    <inspection_tool class="InstanceVariableUninitializedUse" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="false" />
      <option name="annotationNamesString" value="" />
    </inspection_tool>
    <inspection_tool class="InstanceofChain" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInstanceofOnLibraryClasses" value="false" />
    </inspection_tool>
    <inspection_tool class="InstanceofIncompatibleInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InstanceofInterfaces" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InstanceofThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IntLiteralMayBeLongLiteral" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InterfaceNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
      <option name="m_minLength" value="5" />
      <option name="m_maxLength" value="64" />
    </inspection_tool>
    <inspection_tool class="IteratorNextDoesNotThrowNoSuchElementException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JUnitAbstractTestClassNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d]*TestCase" />
      <option name="m_minLength" value="12" />
      <option name="m_maxLength" value="64" />
    </inspection_tool>
    <inspection_tool class="JUnitTestClassNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d]*Test" />
      <option name="m_minLength" value="8" />
      <option name="m_maxLength" value="64" />
    </inspection_tool>
    <inspection_tool class="JavaLangReflect" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="KeySetIterationMayUseEntrySet" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LegacyStringFormatting" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LengthOneStringInIndexOf" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LengthOneStringsInConcatenation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LimitedScopeInnerClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ListenerMayUseAdapter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="checkForEmptyMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="LiteralAsArgToStringEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LocalCanBeFinal" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="REPORT_VARIABLES" value="true" />
      <option name="REPORT_PARAMETERS" value="false" />
    </inspection_tool>
    <inspection_tool class="LocalVariableHidingMemberVariable" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInvisibleFields" value="true" />
      <option name="m_ignoreStaticMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="LocalVariableNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreForLoopParameters" value="false" />
      <option name="m_ignoreCatchParameters" value="false" />
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="25" />
    </inspection_tool>
    <inspection_tool class="LoggerInitializedWithForeignClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="loggerClassName" value="org.apache.log4j.Logger,org.slf4j.LoggerFactory,org.apache.commons.logging.LogFactory,java.util.logging.Logger" />
      <option name="loggerFactoryMethodName" value="getLogger,getLogger,getLog,getLogger" />
    </inspection_tool>
    <inspection_tool class="LoggingConditionDisagreesWithLogStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LoopWithImplicitTerminationCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MagicNumber" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInTestCode" value="true" />
      <option name="ignoreInitialCapacity" value="true" />
    </inspection_tool>
    <inspection_tool class="MapReplaceableByEnumMap" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodCanBeVariableArityMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodCount" enabled="true" level="INFO" enabled_by_default="true">
      <option name="m_limit" value="20" />
      <option name="ignoreGettersAndSetters" value="true" />
      <option name="ignoreOverridingMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="MethodCoupling" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_includeJavaClasses" value="false" />
      <option name="m_includeLibraryClasses" value="false" />
      <option name="m_limit" value="10" />
    </inspection_tool>
    <inspection_tool class="MethodMayBeStatic" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_onlyPrivateOrFinal" value="false" />
      <option name="m_ignoreEmptyMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="MethodMayBeSynchronized" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodNameSameAsParentName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodNamesDifferOnlyByCase" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodOnlyUsedFromInnerClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreMethodsAccessedFromAnonymousClass" value="false" />
      <option name="ignoreStaticMethodsFromNonStaticInnerClass" value="false" />
      <option name="onlyReportStaticMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="MethodOverloadsParentMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodOverridesPackageLocalMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodOverridesPrivateMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodOverridesStaticMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisorderedAssertEqualsParameters" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MissingDeprecatedAnnotation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MissingOverrideAnnotation" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreObjectMethods" value="true" />
      <option name="ignoreAnonymousClassMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="MissortedModifiers" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_requireAnnotationsFirst" value="true" />
    </inspection_tool>
    <inspection_tool class="MisspelledCompareTo" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisspelledEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisspelledHashcode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisspelledSetUp" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisspelledTearDown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisspelledToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MultipleDeclaration" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreForLoopDeclarations" value="true" />
    </inspection_tool>
    <inspection_tool class="MultipleExceptionsDeclaredOnTestMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MultipleReturnPointsPerMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreGuardClauses" value="false" />
      <option name="ignoreEqualsMethod" value="true" />
      <option name="m_limit" value="3" />
    </inspection_tool>
    <inspection_tool class="MultipleTopLevelClassesInFile" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MultipleTypedDeclaration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MultipleVariablesInDeclaration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NakedNotify" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NegatedEqualityExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedAssignment" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedSynchronizedStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestingDepth" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="5" />
    </inspection_tool>
    <inspection_tool class="NewExceptionWithoutArguments" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonCommentSourceStatements" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="30" />
    </inspection_tool>
    <inspection_tool class="NonExceptionNameEndsWithException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalStaticVariableUsedInClassInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalUtilityClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonProtectedConstructorInAbstractClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreNonPublicClasses" value="false" />
    </inspection_tool>
    <inspection_tool class="NonReproducibleMathCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonShortCircuitBoolean" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonStaticFinalLogger" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="loggerClassName" value="" />
    </inspection_tool>
    <inspection_tool class="NonSynchronizedMethodOverridesSynchronizedMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonThreadSafeLazyInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NoopMethodInAbstractClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NotifyCalledOnCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NotifyNotInSynchronizedContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NotifyWithoutCorrespondingWait" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NullThrown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObjectEquality" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreEnums" value="true" />
      <option name="m_ignoreClassObjects" value="false" />
      <option name="m_ignorePrivateConstructors" value="false" />
    </inspection_tool>
    <inspection_tool class="ObjectNotify" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObjectToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObsoleteCollection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreRequiredObsoleteCollectionTypes" value="false" />
    </inspection_tool>
    <inspection_tool class="OctalAndDecimalIntegersMixed" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OverloadedMethodsWithSameNumberOfParameters" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInconvertibleTypes" value="true" />
    </inspection_tool>
    <inspection_tool class="OverloadedVarargsMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OverlyComplexArithmeticExpression" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="6" />
    </inspection_tool>
    <inspection_tool class="OverlyComplexBooleanExpression" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="3" />
      <option name="m_ignorePureConjunctionsDisjunctions" value="true" />
    </inspection_tool>
    <inspection_tool class="OverlyStrongTypeCast" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInMatchingInstanceof" value="false" />
    </inspection_tool>
    <inspection_tool class="OverridableMethodCallDuringObjectConstruction" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OverriddenMethodCallDuringObjectConstruction" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PackageNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[a-z]*" />
      <option name="m_minLength" value="3" />
      <option name="m_maxLength" value="16" />
    </inspection_tool>
    <inspection_tool class="PackageVisibleField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ParameterHidingMemberVariable" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="m_ignoreInvisibleFields" value="true" />
      <option name="m_ignoreStaticMethodParametersHidingInstanceFields" value="false" />
      <option name="m_ignoreForConstructors" value="true" />
      <option name="m_ignoreForPropertySetters" value="true" />
      <option name="m_ignoreForAbstractMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="ParameterNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="35" />
    </inspection_tool>
    <inspection_tool class="ParameterizedParametersStaticCollection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ParametersPerConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ParametersPerMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="5" />
    </inspection_tool>
    <inspection_tool class="PointlessIndexOfComparison" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ProtectedField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ProtectedInnerClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreEnums" value="false" />
      <option name="ignoreInterfaces" value="false" />
    </inspection_tool>
    <inspection_tool class="PublicConstructorInNonPublicClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PublicField" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreEnums" value="false" />
      <option name="ignorableAnnotations">
        <value />
      </option>
    </inspection_tool>
    <inspection_tool class="PublicFieldAccessedInSynchronizedContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="QuestionableName" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="nameString" value="aa,abc,bad,bar,bar2,baz,baz1,baz2,baz3,bb,blah,bogus,bool,cc,dd,defau1t,dummy,dummy2,ee,fa1se,ff,foo,foo1,foo2,foo3,foobar,four,fred,fred1,fred2,gg,hh,hello,hello1,hello2,hello3,ii,nu11,one,silly,silly2,string,two,that,then,three,whi1e,var" />
    </inspection_tool>
    <inspection_tool class="RandomDoubleForRandomInteger" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="RedundantFieldInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="RedundantImplements" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreSerializable" value="false" />
      <option name="ignoreCloneable" value="false" />
    </inspection_tool>
    <inspection_tool class="RelativeImport" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ReplaceAssignmentWithOperatorAssignment" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreLazyOperators" value="true" />
      <option name="ignoreObscureOperators" value="false" />
    </inspection_tool>
    <inspection_tool class="ResultOfObjectAllocationIgnored" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ResultSetIndexZero" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ReturnNull" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_reportObjectMethods" value="true" />
      <option name="m_reportArrayMethods" value="true" />
      <option name="m_reportCollectionMethods" value="true" />
      <option name="m_ignorePrivateMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="ReturnOfDateField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SafeLock" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SetReplaceableByEnumSet" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SetupCallsSuperSetup" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SetupIsPublicVoidNoArg" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SignalWithoutCorrespondingAwait" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SimplifiableAnnotation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SimplifiableEqualsExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SimplifiableJUnitAssertion" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SleepWhileHoldingLock" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StandardVariableNames" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticCallOnSubclass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticCollection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreWeakCollections" value="false" />
    </inspection_tool>
    <inspection_tool class="StaticFieldReferenceOnSubclass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticMethodNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="4" />
      <option name="m_maxLength" value="32" />
    </inspection_tool>
    <inspection_tool class="StaticMethodOnlyUsedInOneClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticNonFinalField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticSuite" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticVariableInitialization" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="false" />
    </inspection_tool>
    <inspection_tool class="StaticVariableNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="checkMutableFinals" value="false" />
      <option name="m_regex" value="s_[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="5" />
      <option name="m_maxLength" value="32" />
    </inspection_tool>
    <inspection_tool class="StaticVariableUninitializedUse" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="false" />
    </inspection_tool>
    <inspection_tool class="StringBufferField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringBufferMustHaveInitialCapacity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringBufferToStringInConcatenation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringConcatenationArgumentToLogCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringConcatenationInFormatCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringConcatenationInMessageFormatCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringConcatenationMissingWhitespace" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringReplaceableByStringBuffer" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="onlyWarnOnLoop" value="true" />
    </inspection_tool>
    <inspection_tool class="SubtractionInCompareTo" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SuppressionAnnotation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SuspiciousArrayCast" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SuspiciousInferredType" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SynchronizationOnStaticField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SynchronizeOnLock" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SynchronizeOnThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SynchronizedMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_includeNativeMethods" value="true" />
      <option name="ignoreSynchronizedSuperMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="SynchronizedOnLiteralObject" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SystemGC" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SystemOutErr" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TailRecursion" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TeardownCallsSuperTeardown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TeardownIsPublicVoidNoArg" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TestCaseInProductCode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TestCaseWithConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TestCaseWithNoTestMethods" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreSupers" value="false" />
    </inspection_tool>
    <inspection_tool class="TestMethodInProductCode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TestMethodIsPublicVoidNoArg" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TestMethodWithoutAssertion" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="assertionMethods" value="org.junit.Assert,assert.*|fail.*,junit.framework.Assert,assert.*|fail.*,org.mockito.Mockito,verify.*,org.junit.rules.ExpectedException,expect.*" />
    </inspection_tool>
    <inspection_tool class="TestOnlyProblems" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThisEscapedInConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadDumpStack" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadLocalNotStaticFinal" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadPriority" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadStartInConstruction" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadStopSuspendResume" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadYield" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TodoComment" enabled="true" level="INFO" enabled_by_default="true" />
    <inspection_tool class="TooBroadCatch" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TooBroadScope" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_allowConstructorAsInitializer" value="false" />
      <option name="m_onlyLookAtBlocks" value="false" />
    </inspection_tool>
    <inspection_tool class="TooBroadThrows" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TypeMayBeWeakened" enabled="false" level="INFO" enabled_by_default="false">
      <option name="useRighthandTypeAsWeakestTypeInAssignments" value="false" />
      <option name="useParameterizedTypeForCollectionMethods" value="true" />
      <option name="doNotWeakenToJavaLangObject" value="true" />
      <option name="onlyWeakentoInterface" value="true" />
    </inspection_tool>
    <inspection_tool class="TypeParameterNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="20" />
    </inspection_tool>
    <inspection_tool class="UnconditionalWait" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessarilyQualifiedInnerClassAccess" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreReferencesNeedingImport" value="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessarilyQualifiedStaticUsage" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreStaticFieldAccesses" value="false" />
      <option name="m_ignoreStaticMethodCalls" value="false" />
      <option name="m_ignoreStaticAccessFromStaticContext" value="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessarilyQualifiedStaticallyImportedElement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryBlockStatement" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreSwitchBranches" value="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryConstantArrayCreationExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryExplicitNumericCast" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryFullyQualifiedName" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreJavadoc" value="false" />
      <option name="ignoreInModuleStatements" value="true" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryModuleDependencyInspection" enabled="false" level="INFO" enabled_by_default="false" />
    <inspection_tool class="UnnecessaryQualifierForThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessarySuperConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessarySuperQualifier" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnqualifiedStaticUsage" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="m_ignoreStaticFieldAccesses" value="false" />
      <option name="m_ignoreStaticMethodCalls" value="false" />
      <option name="m_ignoreStaticAccessFromStaticContext" value="true" />
    </inspection_tool>
    <inspection_tool class="UnusedDeclaration" enabled="true" level="INFO" enabled_by_default="true">
      <option name="ADD_MAINS_TO_ENTRIES" value="true" />
      <option name="ADD_APPLET_TO_ENTRIES" value="true" />
      <option name="ADD_SERVLET_TO_ENTRIES" value="true" />
      <option name="ADD_NONJAVA_TO_ENTRIES" value="true" />
    </inspection_tool>
    <inspection_tool class="UpperCaseFieldNameNotConstant" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfAnotherObjectsPrivateField" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="ignoreSameClass" value="true" />
      <option name="ignoreEquals" value="true" />
    </inspection_tool>
    <inspection_tool class="UseOfObsoleteAssert" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfPropertiesAsHashtable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UtilityClassWithPublicConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UtilityClassWithoutPrivateConstructor" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignorableAnnotations">
        <value />
      </option>
      <option name="ignoreClassesWithOnlyMain" value="false" />
    </inspection_tool>
    <inspection_tool class="VariableNotUsedInsideIf" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="VolatileArrayField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="VolatileLongOrDoubleField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitCalledOnCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitNotInLoop" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitNotInSynchronizedContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitOrAwaitWithoutTimeout" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitWithoutCorrespondingNotify" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WeakerAccess" enabled="true" level="INFO" enabled_by_default="true">
      <option name="SUGGEST_PACKAGE_LOCAL_FOR_MEMBERS" value="true" />
      <option name="SUGGEST_PACKAGE_LOCAL_FOR_TOP_CLASSES" value="true" />
      <option name="SUGGEST_PRIVATE_FOR_INNERS" value="false" />
    </inspection_tool>
    <inspection_tool class="WhileLoopSpinsOnField" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreNonEmtpyLoops" value="false" />
    </inspection_tool>
    <inspection_tool class="ZeroLengthArrayInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="unusedMerged" />
  </profile>
</component>