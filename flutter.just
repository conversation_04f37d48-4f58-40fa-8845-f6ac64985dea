# List available recipes
default:
    @just --list flutter

# Run flutter pub get
pub-get:
    flutter pub get

# Run flutter pub upgrade
pub-upgrade:
    flutter pub upgrade

# Run build_runner once with --delete-conflicting-outputs
gen:
    dart run build_runner build --delete-conflicting-outputs

# Run build_runner in watch mode with --delete-conflicting-outputs
gen-watch:
    dart run build_runner watch --delete-conflicting-outputs

# Run Flutter tests
test file="":
    flutter test {{file}}

# Run Flutter tests with coverage
test-coverage file="":
    flutter test --coverage {{file}}

# Format Dart code with optional path (defaults to ".")
format path=".":
    dart format {{path}}

# Apply Dart fixes with optional path (defaults to ".")
lint path=".":
    dart fix --apply {{path}}

# Analyze Dart code
analyze:
    flutter analyze
