use anyhow::Result;
use chrono::{DateTime, Utc};
use filetime::FileTime;

use crate::api::domain::{
    CopyControlCommand, CopyOptions, CopyWithProgressOperation, PathStats, PathStatsType,
    ProgressReport,
};
use crate::api::error::FileSystemError;
use crate::frb_generated::StreamSink;
use crate::helpers::windows;
use cfg_if::cfg_if;
use flutter_rust_bridge::{frb, DartFnFuture};
#[cfg(not(target_os = "windows"))]
use futures::stream::{FuturesUnordered, StreamExt};
use std::env;
use std::path::Path as StdPath;
// use std::sync::atomic::{AtomicU64, Ordering};
use std::time::SystemTime;
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncWriteExt};

#[frb(init)]
pub fn init_app() {
    env::set_var("RUST_BACKTRACE", "1");
    // Default utilities - feel free to customize
    flutter_rust_bridge::setup_default_user_utils();
}

// static NEXT_OPERATION_ID: AtomicU64 = AtomicU64::new(1);

/// Get file or directory stats
pub async fn stat(path: String) -> PathStats {
    match fs::metadata(std::path::Path::new(&path)).await {
        Ok(meta) => meta.into(),
        Err(io_error) => {
            let fs_error: FileSystemError = io_error.into();
            PathStats {
                type_: PathStatsType::Unknown,
                create_time: DateTime::<Utc>::from(std::time::UNIX_EPOCH),
                modify_time: DateTime::<Utc>::from(std::time::UNIX_EPOCH),
                access_time: DateTime::<Utc>::from(std::time::UNIX_EPOCH),
                size: None,
                error: Some(fs_error),
            }
        }
    }
}

impl From<std::fs::Metadata> for PathStats {
    fn from(meta: std::fs::Metadata) -> Self {
        let file_type = if meta.is_dir() {
            PathStatsType::Directory
        } else if meta.is_file() {
            PathStatsType::File
        } else if meta.file_type().is_symlink() {
            PathStatsType::Link
        } else {
            PathStatsType::Unknown
        };

        // Helper to convert Result<SystemTime, io::Error> to DateTime<Utc>
        // Uses UNIX_EPOCH as a fallback if SystemTime is not available or errors.
        let system_time_to_datetime =
            |sys_time_res: std::io::Result<std::time::SystemTime>| -> DateTime<Utc> {
                match sys_time_res {
                    Ok(sys_time) => DateTime::<Utc>::from(sys_time),
                    Err(_) => DateTime::<Utc>::from(std::time::UNIX_EPOCH), // Fallback to Unix Epoch
                }
            };

        PathStats {
            type_: file_type,
            create_time: system_time_to_datetime(meta.created()),
            modify_time: system_time_to_datetime(meta.modified()),
            access_time: system_time_to_datetime(meta.accessed()),
            size: Some(meta.len()),
            error: None,
        }
    }
}

/// List directory contents
#[frb(stream_dart_await)]
pub async fn list(
    path: String,
    sink: StreamSink<(String, PathStats)>,
) -> Result<(), FileSystemError> {
    cfg_if! {
        if #[cfg(target_os = "windows")] {
            return windows::list(path, sink).await;
        } else {
            let mut entries = fs::read_dir(StdPath::new(&path)).await?;

            // Spawn the streaming work in background to not block the caller
            tokio::spawn(async move {
                // Populate the stream asynchronously with unordered parallel metadata fetching
                let mut pending_metadata = FuturesUnordered::new();
                let mut entries_done = false;

                loop {
                    tokio::select! {
                        // Try to get the next directory entry
                        entry_result = entries.next_entry(), if !entries_done => {
                            match entry_result {
                                Ok(Some(entry)) => {
                                    let entry_path = entry.path().to_string_lossy().to_string();
                                    // Spawn metadata fetch as a future without awaiting
                                    let metadata_future = async move {
                                        let stats: PathStats = entry.metadata().await.into();
                                        (entry_path, stats)
                                    };
                                    pending_metadata.push(metadata_future);
                                }
                                Ok(None) => {
                                    entries_done = true;
                                }
                                Err(e) => {
                                    let error: FileSystemError = e.into();
                                    if let Err(err) = sink.add_error(error) {
                                        log::debug!("Failed to add error to sink: {}", err);
                                    }
                                    return;
                                }
                            }
                        }

                        // Process completed metadata fetches
                        Some((entry_path, stats)) = pending_metadata.next(), if !pending_metadata.is_empty() => {
                            if let Err(e) = sink.add((entry_path, stats)) {
                                log::debug!("Stream sink closed, stopping directory listing: {}", e);
                                return;
                            }
                        }

                        // Exit when both entries are done and no pending metadata
                        else => return,
                    }
                }
            });

            // Return immediately to not block the caller
            Ok(())
        }
    }
}

/// Copy a file with options to preserve attributes and overwrite
pub async fn copy_file(
    source: String,
    dest: String,
    options: CopyOptions,
) -> Result<(), FileSystemError> {
    let source_path = StdPath::new(&source);

    let mut source_metadata: Option<std::fs::Metadata> = None;
    if options.preserve_timestamps {
        source_metadata = match fs::metadata(source_path).await {
            Ok(metadata) => Some(metadata),
            Err(e) => {
                log::warn!("Failed to get source metadata: {}", e);
                None
            }
        }
    }

    cfg_if! {
        if #[cfg(target_os = "windows")] {
            windows::copy_file(&source, &dest, &options).await?;
        } else {
            match fs::try_exists(&dest).await {
                Ok(true) => {
                    return Err(FileSystemError::AlreadyExists {
                        message: format!("File already exists: {}", dest),
                    })
                }
                Ok(false) => {}
                Err(e) => {
                    let fs_error: FileSystemError = e.into();
                    return Err(fs_error);
                }
            }

            fs::copy(source_path, dest).await?;
        }
    }

    // Preserving timestamps must be done as a separate step
    match source_metadata {
        Some(metadata) => {
            if let Err(e) = set_timestamps_from_metadata(metadata, dest).await {
                log::warn!("Failed to set timestamps: {}", e);
            }
        }
        None => {}
    }

    Ok(())
}

/// Prepare a copy operation with progress reporting
pub async fn copy_file_with_progress(
    source: String,
    dest: String,
    options: CopyOptions,
    chunk_size: u32,
    on_progress: impl Fn(ProgressReport) -> DartFnFuture<()> + Send + Sync + 'static,
    on_done: impl Fn(Option<FileSystemError>) -> DartFnFuture<()> + Send + Sync + 'static,
) -> CopyWithProgressOperation {
    debug_assert!(
        source != dest,
        "Source and destination paths cannot be the same."
    );
    debug_assert!(chunk_size > 0, "Chunk size must be greater than 0.");

    let (control_tx, mut control_rx) = tokio::sync::mpsc::channel(10);

    tokio::spawn(async move {
        let result = copy_file_with_progress_impl(
            source,
            dest,
            options,
            chunk_size as usize,
            &mut control_rx,
            on_progress,
        )
        .await;
        log::info!("Copy operation finished with result: {:#?}", result);
        on_done(result.err()).await;
    });

    CopyWithProgressOperation { control_tx }
}

/// Private helper method that contains the actual copy implementation.
async fn copy_file_with_progress_impl(
    source_path: String,
    dest_path: String,
    options: CopyOptions,
    chunk_size: usize,
    control_rx: &mut tokio::sync::mpsc::Receiver<CopyControlCommand>,
    on_progress: impl Fn(ProgressReport) -> DartFnFuture<()> + Send + Sync + 'static,
) -> Result<(), FileSystemError> {
    let mut source = fs::File::open(&source_path).await?;
    let mut dest = fs::OpenOptions::new()
        .write(true)
        .create(true)
        .truncate(options.overwrite_if_exists)
        .create_new(!options.overwrite_if_exists)
        .open(&dest_path)
        .await?;

    let mut source_metadata: Option<std::fs::Metadata> = None;
    if options.preserve_timestamps {
        source_metadata = match source.metadata().await {
            Ok(metadata) => Some(metadata),
            Err(e) => {
                log::warn!("Failed to get source metadata: {}", e);
                None
            }
        }
    }

    let mut bytes_copied: u64 = 0;
    let mut buffer = vec![0u8; chunk_size];

    loop {
        tokio::select! {
            biased; // Prioritize control commands

            maybe_command = control_rx.recv() => {
                match maybe_command {
                    Some(CopyControlCommand::Pause) => {
                        loop {
                            match control_rx.recv().await {
                                Some(CopyControlCommand::Resume) => break,
                                Some(CopyControlCommand::Cancel) => {
                                    return Err(FileSystemError::Cancelled);
                                }
                                Some(CopyControlCommand::Pause) => {
                                    debug_assert!(false, "Already paused");
                                    return Err(FileSystemError::OperationControlError { message: "Already paused".to_string() });
                                }
                                None => {
                                    return Err(FileSystemError::OperationControlError { message: "Control channel disconnected during pause".to_string() });
                                }
                            }
                        }
                    }
                    Some(CopyControlCommand::Resume) => {
                        debug_assert!(false, "Already running or just resumed, ignore");
                        log::warn!("Already running or just resumed, ignore");
                    }
                    Some(CopyControlCommand::Cancel) => {
                        return Err(FileSystemError::Cancelled);
                    }
                    None => {
                        return Err(FileSystemError::OperationControlError { message: "Control channel disconnected while running".to_string() });
                    }
                }
            }

            read_result = source.read(&mut buffer) => {
                let n = read_result?;
                log::info!("Read {} bytes", n);
                if n == 0 { // EOF
                    log::info!("EOF");
                    break;
                }
                let data_to_write = &buffer[..n];
                dest.write_all(data_to_write).await?;
                bytes_copied += n as u64;
                on_progress(ProgressReport { bytes_copied }).await;
            }
        }
    }

    // Handle timestamp preservation if requested
    if let Some(metadata) = source_metadata {
        if let Err(e) = set_timestamps_from_metadata(metadata, dest_path.clone()).await {
            log::warn!("Failed to set timestamps: {}", e);
        }
    }

    Ok(())
}

impl CopyWithProgressOperation {
    /// Pauses the file copy operation.
    pub async fn pause(&self) -> Result<(), FileSystemError> {
        self.control_tx.try_send(CopyControlCommand::Pause)?;
        Ok(())
    }

    /// Resumes a paused file copy operation.
    pub async fn resume(&self) -> Result<(), FileSystemError> {
        self.control_tx.try_send(CopyControlCommand::Resume)?;
        Ok(())
    }

    /// Cancels the file copy operation.
    pub async fn cancel(&self) -> Result<(), FileSystemError> {
        self.control_tx.try_send(CopyControlCommand::Cancel)?;
        Ok(())
    }
}

/// Rename or move a file or directory
pub async fn rename(source: String, dest: String) -> Result<(), FileSystemError> {
    let source_path = StdPath::new(&source);
    let dest_path = StdPath::new(&dest);

    fs::rename(source_path, dest_path).await?;
    Ok(())
}

/// Delete a file
pub async fn delete_file(path: String) -> Result<(), FileSystemError> {
    let std_path = StdPath::new(&path);

    fs::remove_file(std_path).await?;
    Ok(())
}

pub async fn read_file(path: String) -> Result<String, FileSystemError> {
    let content = fs::read_to_string(StdPath::new(&path)).await?;
    Ok(content)
}

/// Write a string to a file
pub async fn write_file(path: String, content: String) -> Result<(), FileSystemError> {
    let std_path = StdPath::new(&path);
    let mut file = fs::OpenOptions::new()
        .write(true)
        .create(true)
        .truncate(true)
        .open(std_path)
        .await?;

    file.write_all(content.as_bytes()).await?;
    Ok(())
}

/// Set file timestamps
pub async fn set_timestamps(
    path: String,
    create_time: DateTime<Utc>,
    modify_time: DateTime<Utc>,
    access_time: DateTime<Utc>,
) -> Result<(), FileSystemError> {
    let modify_time = FileTime::from(Into::<SystemTime>::into(modify_time));
    let access_time = FileTime::from(Into::<SystemTime>::into(access_time));
    cfg_if! {
        if #[cfg(target_os = "windows")] {
            let create_time = Some(FileTime::from(Into::<SystemTime>::into(create_time)));
        } else {
            let create_time = None;
        }
    }
    set_timestamps_from_filetime(path, create_time, modify_time, access_time).await
}

async fn set_timestamps_from_metadata(
    metadata: std::fs::Metadata,
    dest_path: impl AsRef<StdPath> + Send + 'static,
) -> Result<(), FileSystemError> {
    let modify_time = filetime::FileTime::from_last_modification_time(&metadata);
    let access_time = filetime::FileTime::from_last_access_time(&metadata);
    cfg_if! {
        if #[cfg(target_os = "windows")] {
            let create_time = filetime::FileTime::from_creation_time(&metadata);
        } else {
            let create_time = None;
        }
    }
    set_timestamps_from_filetime(dest_path, create_time, modify_time, access_time).await
}

async fn set_timestamps_from_filetime<P: AsRef<StdPath> + Send + 'static>(
    path: P,
    create_time: Option<FileTime>,
    modify_time: FileTime,
    access_time: FileTime,
) -> Result<(), FileSystemError> {
    tokio::task::spawn_blocking(move || -> Result<(), FileSystemError> {
        cfg_if! {
            if #[cfg(target_os = "windows")] {
                return windows::set_timestamps(path, create_time.unwrap(), modify_time, access_time);
            } else {
                filetime::set_file_times(path, access_time, modify_time)?;
            }
        }
    })
    .await??;
    Ok(())
}

/// Create a directory
pub async fn mkdir(path: String, recursive: bool) -> Result<(), FileSystemError> {
    let std_path = StdPath::new(&path);
    if recursive {
        fs::create_dir_all(std_path).await?
    } else {
        fs::create_dir(std_path).await?
    };
    Ok(())
}

/// Remove a directory
pub async fn rmdir(path: String, recursive: bool) -> Result<(), FileSystemError> {
    let std_path = StdPath::new(&path);
    if recursive {
        fs::remove_dir_all(std_path).await?
    } else {
        fs::remove_dir(std_path).await?
    };
    Ok(())
}
