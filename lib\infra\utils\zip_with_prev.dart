import 'package:mobx/mobx.dart';

/// A decorator that zips the current value with its previous value.
///
/// This utility is useful for tracking changes between successive values
/// of a computed property, such as for calculating deltas or detecting changes.
///
/// Returns a tuple containing [currentValue, previousValue].
/// If there is no previous value yet, both elements will be the same.
class ZipWithPrev {
  /// Creates a new [ZipWithPrev] annotation.
  const ZipWithPrev();
}

/// Implementation function for the ZipWithPrev annotation.
///
/// Takes a getter function and returns a computed property that produces
/// a tuple of [currentValue, previousValue].
Computed<List<T>> createZipWithPrev<T>(T Function() getter) {
  T? prev;

  return Computed(() {
    final value = getter();
    final prevToUse = prev ?? value;
    prev = value;
    return [value, prevToUse];
  }, name: 'ZipWithPrev');
}

/// Extension method for creating a zipped-with-previous value.
extension ZipWithPrevExtension<T> on T Function() {
  /// Creates a computed property that zips the current value with its previous value.
  ///
  /// Returns a tuple containing [currentValue, previousValue].
  /// If there is no previous value yet, both elements will be the same.
  Computed<List<T>> zipWithPrev() {
    return createZipWithPrev<T>(this);
  }
}
