<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ScalaProjectSettings">
    <option name="intInjectionMapping">
      <map>
        <entry key="css" value="CSS" />
        <entry key="html" value="HTML" />
        <entry key="java" value="JAVA" />
        <entry key="js" value="JavaScript" />
        <entry key="json" value="JSON" />
        <entry key="json5" value="JSON5" />
        <entry key="less" value="LESS" />
        <entry key="protobuf" value="protobuf" />
        <entry key="scala" value="Scala" />
        <entry key="sql" value="SQL" />
        <entry key="sqlu" value="SQL" />
        <entry key="svg" value="SVG" />
        <entry key="xhtml" value="XHTML" />
        <entry key="xml" value="XML" />
        <entry key="yaml" value="yaml" />
      </map>
    </option>
  </component>
</project>