import 'dart:math';

import 'package:json_annotation/json_annotation.dart';
import 'package:mek_data_class/mek_data_class.dart';
import 'package:mobx/mobx.dart';

import '../../app/domain/errors.dart';
import '../../app/domain/path.dart';
import '../../app/persistence/data_repository.dart';
import '../../app/util/json_serializer.dart';
import '../../app/util/observable_utils.dart';
import '../domain/side.dart';
import './directory_state.dart';
import './history.dart';

part '.gen/history_store.g.dart';

class HistoryStore extends HistoryStoreBase with _$HistoryStore {
  HistoryStore._();

  static Future<HistoryStore> create(DataRepository dataRepository, Side side) async {
    final historyStore = HistoryStore._();
    await dataRepository.register<_HistoryStoreStateSnapshot>(
      name: side.name,
      createDefault: historyStore._createDefault,
      snapshot: historyStore._snapshot,
      restore: historyStore._restore,
      serializer: _HistoryStoreStateSnapshot.serializer,
    );
    return historyStore;
  }

  _HistoryStoreStateSnapshot _createDefault() => _HistoryStoreStateSnapshot(histories: [
        History([DirectoryState(RawPath.currentWorkingDir)], 0),
      ], selectedIndex: 0);

  _HistoryStoreStateSnapshot _snapshot() => _HistoryStoreStateSnapshot(histories: histories, selectedIndex: _currentIndex);

  void _restore(_HistoryStoreStateSnapshot snapshot) {
    if (snapshot.histories.isEmpty) {
      throw AppException.invalidPersistedState("histories cannot be empty!");
    }
    if (snapshot.selectedIndex >= snapshot.histories.length) {
      throw AppException.invalidPersistedState("selectedIndex cannot be greater than histories length!");
    }

    histories.replace(snapshot.histories);
    setCurrentIndex(min(snapshot.selectedIndex, histories.length - 1));
  }
}

abstract class HistoryStoreBase with Store {
  final ObservableList<History> histories = ObservableList();

  @readonly
  int _currentIndex = 0;

  @computed
  History get current => histories[_currentIndex];

  @action
  void cloneCurrent() => _addAndSetCurrent(current.clone());

  @action
  void addWithPath(RawPath dir) => _addAndSetCurrent(History([DirectoryState(dir)], 0));

  @action
  void addWithFocusedPath(RawPath path) {
    addWithPath(path.parent!);
    current.setFocusedPath(path, 0);
  }

  @action
  bool setCurrentHistoryToAnyWhichContainsPathAndFocus(RawPath path) {
    final history = setCurrentHistoryToAnyWhichContainsPath(path.parent!);
    if (history != null) {
      history.setFocusedPath(path, 0);
    }
    return history != null;
  }

  @action
  History? setCurrentHistoryToAnyWhichContainsPath(RawPath dir) {
    final index = histories.indexWhere((element) => element.current.dir == dir);
    if (index != -1) {
      setCurrentIndex(index);
      return histories[index];
    }
    return null;
  }

  @action
  void _addAndSetCurrent(History history) {
    histories.add(history);
    setCurrentIndex(histories.length - 1);
  }

  @action
  void setCurrentIndex(int currentIndex) {
    if (currentIndex >= histories.length) {
      throw Exception("Invalid history index: $currentIndex, max=${histories.length}");
    }
    if (_currentIndex != currentIndex) {
      _currentIndex = currentIndex;
    }
  }

  @action
  void delete(int index) {
    if (histories.length <= 1) return;
    histories.removeAt(index);
    if (_currentIndex == histories.length) {
      setCurrentIndex(_currentIndex - 1);
    }
  }

  @action
  void deleteCurrent() => delete(_currentIndex);
}

@DataClass()
@JsonSerializable()
class _HistoryStoreStateSnapshot with _$_HistoryStoreStateSnapshot {
  @JsonKey(name: "tabs")
  final List<History> histories;

  @JsonKey(name: "selectedTab")
  final int selectedIndex;

  const _HistoryStoreStateSnapshot({required this.histories, required this.selectedIndex});

  static final serializer = JsonSerializer<_HistoryStoreStateSnapshot>(
    toJson: (instance) => _$HistoryStoreStateSnapshotToJson(instance),
    fromJson: (json) => _$HistoryStoreStateSnapshotFromJson(json),
  );
}
