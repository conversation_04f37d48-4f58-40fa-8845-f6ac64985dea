# List available recipes
default:
    @just --list release

# Build Rust library in release mode for current platform
rust:
    cd rust && cargo build --release

# Run Flutter app in release mode
run: rust
    flutter run --release

# Build for all platforms (requires appropriate setup)
all: rust
    flutter build windows --release
    flutter build macos --release
    flutter build linux --release
    flutter build apk --release
    flutter build ios --release

# Build for Windows with Rust
windows: rust
    flutter build windows --release

# Build for macOS with Rust
macos: rust
    flutter build macos --release

# Build for Linux with Rust
linux: rust
    flutter build linux --release

# Build for Android with Rust
android: rust
    flutter build apk --release

# Build for iOS with Rust
ios: rust
    flutter build ios --release
