import '../../../app/domain/file.dart';
import '../../../app/domain/path.dart';

abstract class DisplayFile {
  abstract final File file;

  String get displayName;

  String get absolutePath => file.absolutePath;
  String get root => file.root;
  String get dir => file.dir;
  String get name => file.name;
  String get nameWithoutExtension => file.nameWithoutExtension;
  String get extension => file.extension;
  bool get isRoot => file.isRoot;
  List<String> get elementsNames => file.elementNames;
  List<RawPath> get elements => file.elements;
  RawPath? get parent => file.parent;

  bool isParentOf(DisplayFile other) => file.isParentOf(other.file);
  bool isChildOf(DisplayFile other) => file.isChildOf(other.file);
  bool isAncestorOf(DisplayFile other) => file.isAncestorOf(other.file);
  bool isSiblingOf(DisplayFile other) => file.isSiblingOf(other.file);
  bool endsWith(String suffix) => file.endsWith(suffix);

  bool get isFile => file.isFile;
  bool get isDirectory => file.isDirectory;
  bool get isSymbolicLink => file.isSymbolicLink;
  bool get isSocket => file.isSocket;
  bool get isPipe => file.isPipe;
  bool get isError => file.isError;
  DateTime get createTime => file.createTime;
  DateTime get updateTime => file.updateTime;
  int? get size => file.size;
  Exception? get error => file.error;

  @override
  String toString() => file.toString();
}

class ParentDirDisplayFile extends DisplayFile {
  @override
  final File file;

  ParentDirDisplayFile(this.file);

  @override
  String get displayName => "..";

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is ParentDirDisplayFile && runtimeType == other.runtimeType && file == other.file;

  @override
  int get hashCode => file.hashCode;

  @override
  String toString() => "[.. $file]";
}

class FileDisplayFile extends DisplayFile {
  @override
  final File file;

  FileDisplayFile(this.file);

  @override
  String get displayName => file.name;

  @override
  bool operator ==(Object other) => identical(this, other) || other is FileDisplayFile && runtimeType == other.runtimeType && file == other.file;

  @override
  int get hashCode => file.hashCode;

  @override
  String toString() => file.toString();
}
