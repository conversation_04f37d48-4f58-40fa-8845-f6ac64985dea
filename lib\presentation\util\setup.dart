import 'package:flutter/foundation.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:logging/logging.dart';
import 'package:mobx/mobx.dart';

import '../../app/util/log_colorizer.dart';
import '../../app/util/log_utils.dart';
import '../../rust/.gen/api/logging.dart';
import '../../rust/.gen/frb_generated.dart';
import '../../rust/.gen/helpers/log_utils.dart';

final logger = Logger("app");

Future<void> setupApp({String? dylibPath}) async {
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    logger.shout("Unhandled Flutter error:", details.exception, details.stack);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    logger.shout("Unhandled Flutter Platform error:", error, stack);
    return true;
  };

  mainContext.config = mainContext.config.clone(disableErrorBoundaries: kDebugMode);

  // Configure colorized logging
  if (kDebugMode) {
    hierarchicalLoggingEnabled = true;
    Logger.root.level = Level.ALL;

    // Use LogColorizer to format log messages
    Logger.root.onRecord.listen((record) {
      String formattedMessage = LogColorizer.colorizeRecord(record);
      debugPrint(formattedMessage);
    });
  } else {
    Logger.root.level = Level.OFF;
  }

  await setupRust(dylibPath: dylibPath);
}

Future<void> setupRust({String? dylibPath}) async {
  await RustLib.init(externalLibrary: dylibPath != null ? ExternalLibrary.open(dylibPath) : null);

  if (kDebugMode) {
    setupLogging().listen((logEntry) {
      final record = LogRecord(
        switch (logEntry.logLevel) {
          LogLevel.trace => Level.FINEST,
          LogLevel.debug => Level.FINE,
          LogLevel.info => Level.INFO,
          LogLevel.warn => Level.WARNING,
          LogLevel.error => Level.SEVERE,
        },
        logEntry.msg,
        logEntry.lbl,
      );
      String formattedMessage = LogColorizer.colorizeRecord(record);
      debugPrint(formattedMessage);
    });
  }
}
