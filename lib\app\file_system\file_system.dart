import 'dart:async';

import '../../rust/.gen/api/domain.dart' as rs;
import '../domain/path.dart';

export '../../rust/.gen/api/domain.dart' show CopyOptions, ProgressReport;
export '../../rust/.gen/api/error.dart';

abstract class FileSystem {
  // Never throws
  Future<Path> stat(RawPath path);

  Future<Stream<Path>> list(RawPath path);

  Future<void> copyFile(RawPath source, RawPath dest, rs.CopyOptions options);
  Future<void> rename(RawPath source, RawPath dest);
  Future<void> deleteFile(RawPath path);

  Future<String> readFile(RawPath path);
  Future<void> writeFile(RawPath path, String content);

  Future<void> setTimestamps(RawPath path, {required DateTime createTime, required DateTime modifyTime, required DateTime accessTime});

  Future<void> mkdir(RawPath path, {bool recursive = false});
  Future<void> rmdir(RawPath path, {bool recursive = false});

  Future<CopyWithProgressOperation> copyFileWithProgress(
      RawPath source, RawPath dest, rs.CopyOptions options, FutureOr<void> Function(rs.ProgressReport) onProgress,
      {int chunkSize = defaultCopyChunkSize});

  static const int defaultCopyChunkSize = 1024 * 64;
}

abstract class CopyWithProgressOperation {
  /// Waits for the copy operation to complete.
  Future<void> finished();

  /// Pauses the file copy operation.
  Future<void> pause();

  /// Resumes a paused file copy operation.
  Future<void> resume();

  /// Cancels the file copy operation.
  Future<void> cancel();
}
