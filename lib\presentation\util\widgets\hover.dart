import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class Hover extends HookWidget {
  // If true, will only be hovered if none of the children is hovered.
  final bool exclusive;
  final MouseCursor cursor;
  final HitTestBehavior? hitTestBehavior;
  final Widget Function(bool isHovered) builder;

  const Hover({
    super.key,
    this.exclusive = false,
    this.cursor = MouseCursor.defer,
    this.hitTestBehavior,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final hovered = useState(false);
    final childIsHovered = exclusive ? useState(false) : null;

    return MouseRegion(
      cursor: cursor,
      hitTestBehavior: exclusive ? HitTestBehavior.deferToChild : hitTestBehavior,
      onEnter: (_) => (exclusive ? childIsHovered! : hovered).value = true,
      onExit: (_) => (exclusive ? childIsHovered! : hovered).value = false,
      opaque: false,
      // child: builder(hovered.value),
      child: exclusive
          ? MouseRegion(
              hitTestBehavior: HitTestBehavior.translucent,
              opaque: false,
              onEnter: (_) => hovered.value = true,
              onExit: (_) => hovered.value = false,
              child: builder(hovered.value && !(childIsHovered?.value ?? false)),
            )
          : builder(hovered.value),
    );
  }
}
