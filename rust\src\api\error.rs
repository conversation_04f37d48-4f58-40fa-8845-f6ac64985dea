use flutter_rust_bridge::Rust2DartSendError;
use log::SetLoggerError;
use thiserror::Error;
use flutter_rust_bridge::frb;
use tokio::sync::mpsc::error::TrySendError;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, Eq)]
#[frb(json_serializable)]
pub enum FileSystemError {
    #[error("Not found: {message}")]
    NotFound { message: String },

    #[error("Permission denied: {message}")]
    PermissionDenied { message: String },

    #[error("Connection refused: {message}")]
    ConnectionRefused { message: String },

    #[error("Connection reset: {message}")]
    ConnectionReset { message: String },

    #[error("Host unreachable: {message}")]
    HostUnreachable { message: String },

    #[error("Network unreachable: {message}")]
    NetworkUnreachable { message: String },

    #[error("Connection aborted: {message}")]
    ConnectionAborted { message: String },

    #[error("Not connected: {message}")]
    NotConnected { message: String },

    #[error("Address in use: {message}")]
    AddrInUse { message: String },

    #[error("Address not available: {message}")]
    AddrNotAvailable { message: String },

    #[error("Network down: {message}")]
    NetworkDown { message: String },

    #[error("Broken pipe: {message}")]
    BrokenPipe { message: String },

    #[error("File already exists: {message}")]
    AlreadyExists { message: String },

    #[error("Operation would block: {message}")]
    WouldBlock { message: String },

    #[error("Not a directory: {message}")]
    NotADirectory { message: String },

    #[error("Is a directory: {message}")]
    IsADirectory { message: String },

    #[error("Directory not empty: {message}")]
    DirectoryNotEmpty { message: String },

    #[error("Read-only filesystem: {message}")]
    ReadOnlyFilesystem { message: String },

    #[error("Filesystem loop: {message}")]
    FilesystemLoop { message: String },

    #[error("Stale network file handle: {message}")]
    StaleNetworkFileHandle { message: String },

    #[error("Invalid input: {message}")]
    InvalidInput { message: String },

    #[error("Invalid data: {message}")]
    InvalidData { message: String },

    #[error("Timed out: {message}")]
    TimedOut { message: String },

    #[error("Write zero: {message}")]
    WriteZero { message: String },

    #[error("Storage full: {message}")]
    StorageFull { message: String },

    #[error("Not seekable: {message}")]
    NotSeekable { message: String },

    #[error("File too large: {message}")]
    FileTooLarge { message: String },

    #[error("Resource busy: {message}")]
    ResourceBusy { message: String },

    #[error("Executable file busy: {message}")]
    ExecutableFileBusy { message: String },

    #[error("Deadlock: {message}")]
    Deadlock { message: String },

    #[error("Crosses devices: {message}")]
    CrossesDevices { message: String },

    #[error("Too many links: {message}")]
    TooManyLinks { message: String },

    #[error("Invalid filename: {message}")]
    InvalidFilename { message: String },

    #[error("Argument list too long: {message}")]
    ArgumentListTooLong { message: String },

    #[error("Interrupted: {message}")]
    Interrupted { message: String },

    #[error("Unsupported: {message}")]
    Unsupported { message: String },

    #[error("Unexpected end of file: {message}")]
    UnexpectedEof { message: String },

    #[error("Out of memory: {message}")]
    OutOfMemory { message: String },

    #[error("In progress: {message}")]
    InProgress { message: String },

    #[error("Quota exceeded: {message}")]
    QuotaExceeded { message: String },

    #[error("Other error: {message}")]
    Other { message: String },

    #[error("Uncategorized error: {message}")]
    Uncategorized { message: String },

    #[error("Operation control error: {message}")]
    OperationControlError { message: String },

    #[error("Cancelled")]
    Cancelled,

    #[error("Unknown error: {0}")]
    Unknown(String),
}

impl From<std::io::Error> for FileSystemError {
    fn from(e: std::io::Error) -> Self {
        let message = e.to_string();
        match e.kind() {
            std::io::ErrorKind::NotFound => FileSystemError::NotFound { message },
            std::io::ErrorKind::PermissionDenied => FileSystemError::PermissionDenied { message },
            std::io::ErrorKind::ConnectionRefused => FileSystemError::ConnectionRefused { message },
            std::io::ErrorKind::ConnectionReset => FileSystemError::ConnectionReset { message },
            std::io::ErrorKind::HostUnreachable => FileSystemError::HostUnreachable { message },
            std::io::ErrorKind::NetworkUnreachable => FileSystemError::NetworkUnreachable { message },
            std::io::ErrorKind::ConnectionAborted => FileSystemError::ConnectionAborted { message },
            std::io::ErrorKind::NotConnected => FileSystemError::NotConnected { message },
            std::io::ErrorKind::AddrInUse => FileSystemError::AddrInUse { message },
            std::io::ErrorKind::AddrNotAvailable => FileSystemError::AddrNotAvailable { message },
            std::io::ErrorKind::NetworkDown => FileSystemError::NetworkDown { message },
            std::io::ErrorKind::BrokenPipe => FileSystemError::BrokenPipe { message },
            std::io::ErrorKind::AlreadyExists => FileSystemError::AlreadyExists { message },
            std::io::ErrorKind::WouldBlock => FileSystemError::WouldBlock { message },
            std::io::ErrorKind::NotADirectory => FileSystemError::NotADirectory { message },
            std::io::ErrorKind::IsADirectory => FileSystemError::IsADirectory { message },
            std::io::ErrorKind::DirectoryNotEmpty => FileSystemError::DirectoryNotEmpty { message },
            std::io::ErrorKind::ReadOnlyFilesystem => FileSystemError::ReadOnlyFilesystem { message },
            // std::io::ErrorKind::FilesystemLoop => FileSystemError::FilesystemLoop { message },
            std::io::ErrorKind::StaleNetworkFileHandle => FileSystemError::StaleNetworkFileHandle { message },
            std::io::ErrorKind::InvalidInput => FileSystemError::InvalidInput { message },
            std::io::ErrorKind::InvalidData => FileSystemError::InvalidData { message },
            std::io::ErrorKind::TimedOut => FileSystemError::TimedOut { message },
            std::io::ErrorKind::WriteZero => FileSystemError::WriteZero { message },
            std::io::ErrorKind::StorageFull => FileSystemError::StorageFull { message },
            std::io::ErrorKind::NotSeekable => FileSystemError::NotSeekable { message },
            std::io::ErrorKind::QuotaExceeded => FileSystemError::QuotaExceeded { message },
            std::io::ErrorKind::FileTooLarge => FileSystemError::FileTooLarge { message },
            std::io::ErrorKind::ResourceBusy => FileSystemError::ResourceBusy { message },
            std::io::ErrorKind::ExecutableFileBusy => FileSystemError::ExecutableFileBusy { message },
            std::io::ErrorKind::Deadlock => FileSystemError::Deadlock { message },
            std::io::ErrorKind::CrossesDevices => FileSystemError::CrossesDevices { message },
            std::io::ErrorKind::TooManyLinks => FileSystemError::TooManyLinks { message },
            std::io::ErrorKind::InvalidFilename => FileSystemError::InvalidFilename { message },
            std::io::ErrorKind::ArgumentListTooLong => FileSystemError::ArgumentListTooLong { message },
            std::io::ErrorKind::Interrupted => FileSystemError::Interrupted { message },
            std::io::ErrorKind::Unsupported => FileSystemError::Unsupported { message },
            std::io::ErrorKind::UnexpectedEof => FileSystemError::UnexpectedEof { message },
            std::io::ErrorKind::OutOfMemory => FileSystemError::OutOfMemory { message },
            // std::io::ErrorKind::InProgress => FileSystemError::InProgress { message },
            std::io::ErrorKind::Other => FileSystemError::Other { message },
            _ => FileSystemError::Uncategorized { message },
        }
    }
}

impl From<Rust2DartSendError> for FileSystemError {
    fn from(value: Rust2DartSendError) -> Self {
        FileSystemError::Unknown(value.to_string())
    }
}

impl<T> From<TrySendError<T>> for FileSystemError {
    fn from(value: TrySendError<T>) -> Self {
        FileSystemError::Unknown(value.to_string())
    }
}

impl From<tokio::task::JoinError> for FileSystemError {
    fn from(value: tokio::task::JoinError) -> Self {
        FileSystemError::Unknown(value.to_string())
    }
}

impl From<SetLoggerError> for FileSystemError {
    fn from(value: SetLoggerError) -> Self {
        FileSystemError::Unknown(value.to_string())
    }
}