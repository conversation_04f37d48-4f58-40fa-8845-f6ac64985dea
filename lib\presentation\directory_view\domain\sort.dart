import 'package:json_annotation/json_annotation.dart';
import 'package:mek_data_class/mek_data_class.dart';

import '../../../app/util/collection_utils.dart';
import './display_file.dart';

part '.gen/sort.g.dart';

enum SortType {
  name,
  size,
  updateDate,
  createDate;

  int compare(DisplayFile a, DisplayFile b) => _comparator(a, b);

  Comparator<DisplayFile> get _comparator {
    switch (this) {
      case SortType.name:
        return _composedNameComparator;
      case SortType.size:
        return _composedSizeComparator;
      case SortType.updateDate:
        return _composedUpdateDateComparator;
      case SortType.createDate:
        return _composedCrateDateComparator;
    }
  }

  static final Comparator<DisplayFile> _nameComparator = (a, b) => a.name.compareTo(b.name);
  static final Comparator<DisplayFile> _sizeComparator = (a, b) => (a.size ?? 0) - (b.size ?? 0);
  static final Comparator<DisplayFile> _updateDateComparator = (a, b) => a.updateTime.compareTo(b.updateTime);
  static final Comparator<DisplayFile> _createDateComparator = (a, b) => a.createTime.compareTo(b.createTime);

  // TODO: Fallbacks from sorting by name are probably redundant, as file names are unique.
  static final Comparator<DisplayFile> _composedNameComparator = _nameComparator.compose([_createDateComparator, _updateDateComparator]);
  static final Comparator<DisplayFile> _composedSizeComparator = _sizeComparator.compose([_composedNameComparator]);
  static final Comparator<DisplayFile> _composedUpdateDateComparator = _updateDateComparator.compose([_nameComparator, _createDateComparator]);
  static final Comparator<DisplayFile> _composedCrateDateComparator = _createDateComparator.compose([_nameComparator, _updateDateComparator]);
}

enum SortDirection { ascending, descending }

@DataClass()
@JsonSerializable()
class Sort with _$Sort {
  final SortType type;
  final SortDirection direction;

  // TODO: Instead of directoryFirst, this should be a generic "groupBy" param, and probably not a part of 'sort'.
  final bool directoryFirst;

  const Sort(this.type, this.direction, {this.directoryFirst = true});

  int compare(DisplayFile a, DisplayFile b) {
    final result = type.compare(a, b);
    return direction == SortDirection.ascending ? result : -result;
  }

  static const defaultSort = Sort(SortType.name, SortDirection.ascending, directoryFirst: true);

  factory Sort.fromJson(Map<String, dynamic> json) => _$SortFromJson(json);
  Map<String, dynamic> toJson() => _$SortToJson(this);
}
