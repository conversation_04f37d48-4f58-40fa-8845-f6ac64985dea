import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/root_store.dart';
import 'package:qfiler/rust/.gen/frb_generated.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  setUpAll(() async => await RustLib.init());

  group('QFiler Focus Tests', () {
    testWidgets('Tab key switches focus between panes', (WidgetTester tester) async {
      // Create temporary directories for testing
      final tempBaseDir = await io.Directory.systemTemp.createTemp('qfiler_focus_test_');
      final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
      final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();

      try {
        // Create some test files in both directories
        await io.File(p.join(leftTempDir.path, 'test_file_left.txt')).writeAsString('Left pane test file');
        await io.File(p.join(rightTempDir.path, 'test_file_right.txt')).writeAsString('Right pane test file');

        // Use the default RootStore creation but override the initial directories
        final rootStore = await RootStore.create();
        
        // Override the initial directories after creation
        rootStore.allPanesStore.left.historyStore.addWithPath(RawPath(leftTempDir.path));
        rootStore.allPanesStore.right.historyStore.addWithPath(RawPath(rightTempDir.path));

        // Pump the app
        await tester.pumpWidget(App(rootStore));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Verify the app loaded successfully
        expect(find.byType(App), findsOneWidget);

        // Ensure left pane is the source pane
        rootStore.allPanesStore.setSourcePane(Side.left);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        // Initially, left pane should be the source pane
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

        if (kDebugMode) {
          print('Initial state: Left pane is source: ${rootStore.allPanesStore.left.isSource}');
          print('Initial state: Right pane is source: ${rootStore.allPanesStore.right.isSource}');
        }

        // Press Tab key to switch focus to right pane
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        if (kDebugMode) {
          print('After Tab: Left pane is source: ${rootStore.allPanesStore.left.isSource}');
          print('After Tab: Right pane is source: ${rootStore.allPanesStore.right.isSource}');
          print('After Tab: Source pane side: ${rootStore.allPanesStore.sourcePaneSide}');
        }

        // Verify that the source pane switched to right
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.right));

        // Press Tab key again to switch back to left pane
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        if (kDebugMode) {
          print('After second Tab: Left pane is source: ${rootStore.allPanesStore.left.isSource}');
          print('After second Tab: Right pane is source: ${rootStore.allPanesStore.right.isSource}');
          print('After second Tab: Source pane side: ${rootStore.allPanesStore.sourcePaneSide}');
        }

        // Verify that the source pane switched back to left
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

      } finally {
        // Clean up temporary directories
        if (await tempBaseDir.exists()) {
          await tempBaseDir.delete(recursive: true);
        }
      }
    });
  });
}
