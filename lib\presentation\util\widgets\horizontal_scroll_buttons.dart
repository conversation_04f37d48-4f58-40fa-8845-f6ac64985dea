import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

class HorizontalScrollButtons extends StatefulWidget {
  final Widget child;
  final double scrollIconSize;
  final double scrollAmount;
  final Duration animationDuration;

  const HorizontalScrollButtons({
    super.key,
    required this.child,
    this.scrollIconSize = 21,
    this.scrollAmount = 150,
    this.animationDuration = const Duration(milliseconds: 100),
  });

  @override
  _HorizontalScrollButtonsState createState() => _HorizontalScrollButtonsState();
}

class _HorizontalScrollButtonsState extends State<HorizontalScrollButtons> {
  late ScrollController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ScrollController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        NotificationListener<ScrollEndNotification>(
          onNotification: (notification) {
            setState(() {
              // Force a rebuild to show/hide the scroll buttons.
            });
            return false;
          },
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _controller,
            physics: const ClampingScrollPhysics(),
            child: widget.child,
          ),
        ),
        !_controller.hasClients || _controller.position.extentBefore == 0
            ? const SizedBox()
            : Positioned(
                left: 0,
                child: GFIconButton(
                  icon: const Icon(Icons.chevron_left),
                  iconSize: widget.scrollIconSize,
                  padding: const EdgeInsets.all(0),
                  onPressed: () => _scrollTo(_controller.offset - widget.scrollAmount),
                ),
              ),
        !_controller.hasClients || _controller.position.extentAfter == 0
            ? const SizedBox()
            : Positioned(
                right: 0,
                child: GFIconButton(
                  iconSize: widget.scrollIconSize,
                  padding: const EdgeInsets.all(0),
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () => _scrollTo(_controller.offset + widget.scrollAmount),
                ),
              ),
      ],
    );
  }

  void _scrollTo(double position) {
    if (widget.animationDuration == const Duration(milliseconds: 0)) {
      _controller.jumpTo(position);
    } else {
      _controller.animateTo(position, duration: widget.animationDuration, curve: Curves.easeInOut);
    }
  }
}
