// This file contains mocks for the file_op_executor_test.dart file
import 'dart:async';

import 'package:mockito/mockito.dart';
import 'package:qfiler/app/task/file_op_executor.dart';
import 'package:qfiler/app/task/pending_file_store.dart';
import 'package:qfiler/app/task/task.dart';
import 'package:qfiler/rust/.gen/api/domain.dart';

// Mock classes for testing
class MockPendingOperationRepository extends Mock implements PendingOperationRepository {
  final List<Operation> removedOps = [];

  @override
  void removeOp(Operation op) {
    removedOps.add(op);
  }
}

class MockShowConfirmOverwriteDialog extends Mock implements ShowConfirmOverwriteDialog {}

class MockShowFileOpErrorDialog extends Mock implements ShowFileOpErrorDialog {}

class MockPausableStreamCopy implements CopyWithProgressOperation {
  final bool shouldFail;
  bool isPaused = false;
  bool isCancelled = false;
  @override
  final void Function(int bytesInChunk)? onChunkProcessed;

  // Callback functions
  // onChunkProcessed is a final field, not an overridable getter/setter.
  // No @override needed here if it's correctly defined as a field in PausableStreamCopy.
  // Removed onDataChunk and onProgress as they are not in PausableStreamCopy

  MockPausableStreamCopy({this.shouldFail = false, this.onChunkProcessed, required this.id});

  @override
  Future<void> pause() async {
    isPaused = true;
  }

  @override
  Future<void> resume() async {
    isPaused = false;
  }

  @override
  Future<void> cancel() async {
    isCancelled = true;
  }

  @override
  Future<void> execute({required FutureOr<void> Function(ProgressReport) progressCallback}) async {
    if (isCancelled) {
      throw CancellationError();
    }
    if (shouldFail) {
      throw Exception('Simulated error in stream copy');
    }

    // Simulate some data chunks being processed
    if (onChunkProcessed != null) {
      for (int i = 0; i < 5 && !isCancelled; i++) {
        if (isPaused) {
          // Wait until resumed or cancelled
          while (isPaused && !isCancelled) {
            await Future.delayed(Duration(milliseconds: 10));
          }
          if (isCancelled) break;
        }
        onChunkProcessed!(1024); // Simulate 1KB chunk
        await Future.delayed(Duration(milliseconds: 1));
      }
    }

    if (isCancelled) {
      throw CancellationError();
    }
  }

  @override
  BigInt id;

  @override
  void dispose() {
    // TODO: implement dispose
  }

  @override
  // TODO: implement isDisposed
  bool get isDisposed => throw UnimplementedError();
}
