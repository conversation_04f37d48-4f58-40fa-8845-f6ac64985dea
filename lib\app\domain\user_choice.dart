/// Represents the overwrite mode for file operations
enum OverwriteMode {
  overwrite,
  skip,
  cancel,
}

/// Represents the choice made in the overwrite confirmation dialog
class OverwriteChoice {
  const OverwriteChoice({required this.overwriteMode, required this.all});

  /// The overwrite mode chosen
  final OverwriteMode overwriteMode;

  /// Whether to apply this choice to all files
  final bool all;
}

/// Represents the choice made in the file operation error dialog
enum OperationErrorMode {
  retry,
  skip,
  cancel,
}

class OperationErrorChoice {
  const OperationErrorChoice({required this.mode, required this.all});

  final OperationErrorMode mode;
  final bool all;
}
