<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/lib" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/lib/app/domain/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/app/file_system/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/app/file_system/real/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/app/persistence/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/app/settings/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/app/util/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/presentation/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/presentation/directory_view/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/presentation/directory_view/domain/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/presentation/history/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/presentation/pane/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/presentation/tab_view/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/lib/presentation/util/widgets/gen" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/test/app/util/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/rust/src" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/build" />
      <excludeFolder url="file://$MODULE_DIR$/.idea/dictionaries" />
      <excludeFolder url="file://$MODULE_DIR$/linux/flutter/ephemeral" />
      <excludeFolder url="file://$MODULE_DIR$/windows/flutter/ephemeral/.plugin_symlinks" />
      <excludeFolder url="file://$MODULE_DIR$/data" />
      <excludeFolder url="file://$MODULE_DIR$/qfile-commander" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm" />
      <excludeFolder url="file://$MODULE_DIR$/rust_builder/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/rust_builder/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/rust_builder/build" />
      <excludeFolder url="file://$MODULE_DIR$/rust_builder/cargokit/build_tool/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/rust_builder/cargokit/build_tool/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/rust_builder/cargokit/build_tool/build" />
      <excludeFolder url="file://$MODULE_DIR$/rust/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Flutter Plugins" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>