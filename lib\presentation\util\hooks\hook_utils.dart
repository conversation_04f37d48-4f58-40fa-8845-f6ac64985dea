import 'package:flutter/widgets.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../scroll_utils.dart';

void useListener(Listenable listenable, VoidCallback listener, [List<Object?>? keys]) {
  useEffect(() {
    listenable.addListener(listener);
    return () => listenable.removeListener(listener);
  }, keys ?? [listenable]);
}

ValueNotifier<bool> useHasFocus(FocusNode focusNode, [List<Object?>? keys]) {
  final hasFocus = useState(focusNode.hasFocus);
  useListener(focusNode, () => hasFocus.value = focusNode.hasFocus, keys);
  return hasFocus;
}

void useExtraSpeedScrollController(ScrollController controller, {required double extraScrollSpeed}) {
  return useListener(controller, () => addExtraScrollSpeed(controller, extraScrollSpeed: extraScrollSpeed), [controller]);
}
