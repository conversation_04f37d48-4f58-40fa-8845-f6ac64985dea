[package]
name = "rust_lib_qfiler"
version = "0.1.0"
edition = "2021"

[features]
default = []

[lib]
crate-type = ["cdylib", "staticlib"]

[dependencies]
anyhow = "1.0.98"
chrono = { version = "0.4.41", features = ["serde"] }
flutter_rust_bridge = { version = "=2.10.0", features = ["chrono"] }
thiserror = "2.0.12"
futures = "0.3.31"
async-stream = "0.3.6"
tokio = { version = "1.45", features = ["rt", "rt-multi-thread", "io-util", "fs", "time", "macros"] }
log = "0.4"
filetime = "0.2.25"
fs_extra = "1.3.0"
cfg-if = "1.0"
once_cell = "1.21.3"

[dev-dependencies]
tempfile = "3.10.1"

[target.'cfg(target_os = "windows")'.dependencies]
windows = { version = "0.54.0", features = [
    "Win32_Foundation",
    "Win32_Storage_FileSystem",
    "Win32_System_Time",
    "Win32_Security",
    "Win32_System_IO",
    "Win32_Networking_WinSock",
] }

[target.'cfg(target_family = "unix")'.dependencies]
libc = "0.2.172"

[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = ['cfg(frb_expand)'] }
