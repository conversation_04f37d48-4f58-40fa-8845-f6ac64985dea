import 'package:petitparser/petitparser.dart';

/// Grammar for QFiler log colorization, designed for flexible parsing of logs and function calls.
class LogGrammarDefinition extends GrammarDefinition {
  @override
  Parser start() => ref0(fragment).star();

  // Top-level: sequence of atoms (function calls, bracket groups, arrows, etc.)
  Parser expression() => ref0(atom).separatedBy(ref0(ws), includeSeparators: false);

  // Atom: function call, bracket/brace group, arrow, or literal (not identifier)
  Parser atom() => [
        ref0(functionCall),
        ref0(bracketGroup),
        ref0(braceGroup),
        ref0(arrow),
        ref0(literal), // literal excludes identifiers
      ].toChoiceParser();

  // Fragment: either a structured atom or plain text
  Parser fragment() => ref0(plainText) | ref0(atom);

  // Plain text: any run of characters not starting a special token
  // Plain text: any run of characters that cannot possibly start a structured fragment
  Parser plainText() => (ref0(atom).not().and() & any()).plus().flatten();

  // Function call: identifier '(' paramList ')'
  Parser functionCall() => seq3(ref0(identifier), ref0(ws).optional(), seq3(char('('), ref0(paramList).optional(), char(')')));

  // Parameter list: param (',' param)*
  Parser paramList() => ref0(param).separatedBy(ref0(paramSep), includeSeparators: true);

  Parser paramSep() => seq2(ref0(ws).optional(), char(',')).map((v) => v.$2);

  // Parameter: key = value
  Parser param() => seq5(ref0(identifier), ref0(ws).optional(), char('='), ref0(ws).optional(), ref0(paramValue));

  // Parameter value: function call, bracket/brace group, literal, etc.
  Parser paramValue() => [
        ref0(resultAssignment),
        ref0(functionCall),
        ref0(bracketGroup),
        ref0(braceGroup),
        ref0(arrow),
        ref0(literal),
      ].toChoiceParser();

  // Bracket group: '[' ... ']'
  Parser bracketGroup() => seq3(
        char('['),
        pattern('^]').star().flatten(), // Any characters except ']'
        char(']'),
      );

  // Brace group: '{' ... '}'
  Parser braceGroup() => seq3(char('{'), ref0(groupContent).optional(), char('}'));

  // Group content: sequence of atoms separated by commas or whitespace
  Parser groupContent() => ref0(paramValue).separatedBy(ref0(paramSep), includeSeparators: true);

  // Arrow: '->'
  Parser arrow() => seq2(ref0(ws).optional(), string('->'));

  // Identifier: matches [a-zA-Z0-9_.$'/\-]+
  Parser identifier() => pattern(r"[a-zA-Z0-9_.$'/\\-]").plus().flatten();

  // Literal: numbers, strings, null, or any non-special token
  Parser literal() => [
        ref0(number),
        ref0(quotedString),
        ref0(nullValue),
        ref0(simpleLiteral),
      ].toChoiceParser();

  Parser number() => digit().plus().flatten();

  Parser quotedString() => (char("'") & any().starLazy(char("'")) & char("'")).flatten();

  Parser nullValue() => string('null');

  // Result assignment: result = value or result : value
  Parser resultAssignment() => seq3(string('result'), char(':').or(char('=')), pattern('^,[](){}').star().flatten());

  // Simple literal: any run of characters that cannot start an identifier or special token
  Parser simpleLiteral() => ((pattern(r"[a-zA-Z0-9_.$'/\\(\\[\\{\-]").or(string('->'))).neg().plus().flatten());

  // Whitespace
  Parser ws() => whitespace().plus();
}

// Usage example:
// final parser = GrammarParser(LogGrammarDefinition());
// final result = parser.parse(yourLogString);
// Walk result.value to build your colorized output.
