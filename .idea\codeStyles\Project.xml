<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="GENERATE_FINAL_LOCALS" value="true" />
    <option name="INSERT_INNER_CLASS_IMPORTS" value="true" />
    <option name="IMPORT_LAYOUT_TABLE">
      <value>
        <package name="" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="java" withSubpackages="true" static="false" />
        <package name="javax" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="true" />
      </value>
    </option>
    <CssCodeStyleSettings>
      <option name="HEX_COLOR_LOWER_CASE" value="true" />
      <option name="BLANK_LINES_AROUND_NESTED_SELECTOR" value="0" />
    </CssCodeStyleSettings>
    <HTMLCodeStyleSettings>
      <option name="HTML_SPACE_INSIDE_EMPTY_TAG" value="true" />
    </HTMLCodeStyleSettings>
    <JSCodeStyleSettings version="0">
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </JSCodeStyleSettings>
    <JavaCodeStyleSettings>
      <option name="GENERATE_FINAL_LOCALS" value="true" />
      <option name="INSERT_INNER_CLASS_IMPORTS" value="true" />
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="java" withSubpackages="true" static="false" />
          <package name="javax" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="true" />
        </value>
      </option>
    </JavaCodeStyleSettings>
    <JetCodeStyleSettings>
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </JetCodeStyleSettings>
    <LessCodeStyleSettings>
      <option name="HEX_COLOR_LOWER_CASE" value="true" />
      <option name="BLANK_LINES_AROUND_NESTED_SELECTOR" value="0" />
    </LessCodeStyleSettings>
    <MarkdownNavigatorCodeStyleSettings>
      <option name="RIGHT_MARGIN" value="72" />
    </MarkdownNavigatorCodeStyleSettings>
    <SassCodeStyleSettings>
      <option name="BLANK_LINES_AROUND_NESTED_SELECTOR" value="0" />
    </SassCodeStyleSettings>
    <ScalaCodeStyleSettings>
      <option name="PLACE_SELF_TYPE_ON_NEW_LINE" value="false" />
      <option name="NOT_CONTINUATION_INDENT_FOR_PARAMS" value="true" />
    </ScalaCodeStyleSettings>
    <ScssCodeStyleSettings>
      <option name="HEX_COLOR_LOWER_CASE" value="true" />
      <option name="BLANK_LINES_AROUND_NESTED_SELECTOR" value="0" />
    </ScssCodeStyleSettings>
    <TypeScriptCodeStyleSettings version="0">
      <option name="USE_SEMICOLON_AFTER_STATEMENT" value="false" />
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="PREFER_EXPLICIT_TYPES_VARS_FIELDS" value="true" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </TypeScriptCodeStyleSettings>
    <XML>
      <option name="XML_SPACE_INSIDE_EMPTY_TAG" value="true" />
    </XML>
    <codeStyleSettings language="CSS">
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="Dart">
      <option name="RIGHT_MARGIN" value="100" />
    </codeStyleSettings>
    <codeStyleSettings language="Groovy">
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="HTML">
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JAVA">
      <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="0" />
      <option name="BLANK_LINES_AROUND_CLASS" value="0" />
      <option name="BLANK_LINES_AROUND_METHOD_IN_INTERFACE" value="0" />
      <option name="METHOD_PARAMETERS_WRAP" value="5" />
      <option name="IF_BRACE_FORCE" value="3" />
      <option name="DOWHILE_BRACE_FORCE" value="3" />
      <option name="WHILE_BRACE_FORCE" value="3" />
      <option name="FOR_BRACE_FORCE" value="3" />
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
      </indentOptions>
      <arrangement>
        <groups>
          <group>
            <type>GETTERS_AND_SETTERS</type>
            <order>KEEP</order>
          </group>
          <group>
            <type>OVERRIDDEN_METHODS</type>
            <order>KEEP</order>
          </group>
          <group>
            <type>DEPENDENT_METHODS</type>
            <order>BREADTH_FIRST</order>
          </group>
        </groups>
        <rules>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PRIVATE />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PROTECTED />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PACKAGE_PRIVATE />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PUBLIC />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PRIVATE />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PROTECTED />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PACKAGE_PRIVATE />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PUBLIC />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PRIVATE />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PROTECTED />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PACKAGE_PRIVATE />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <FINAL />
                  <PUBLIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PRIVATE />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PROTECTED />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PACKAGE_PRIVATE />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <FIELD />
                  <PUBLIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <FIELD />
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <CONSTRUCTOR />
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <METHOD />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <METHOD />
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <ENUM />
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <INTERFACE />
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <AND>
                  <CLASS />
                  <STATIC />
                </AND>
              </match>
            </rule>
          </section>
          <section>
            <rule>
              <match>
                <CLASS />
              </match>
            </rule>
          </section>
        </rules>
      </arrangement>
    </codeStyleSettings>
    <codeStyleSettings language="JSON">
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="LESS">
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="SASS">
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="SCSS">
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="Scala">
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="true" />
      <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="0" />
      <option name="BLANK_LINES_AROUND_CLASS" value="0" />
      <option name="BLANK_LINES_AROUND_METHOD" value="0" />
      <option name="BLANK_LINES_AROUND_METHOD_IN_INTERFACE" value="0" />
      <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
      <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
      <option name="IF_BRACE_FORCE" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="TypeScript">
      <option name="RIGHT_MARGIN" value="160" />
      <option name="BLANK_LINES_AROUND_METHOD_IN_INTERFACE" value="0" />
      <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
      <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="kotlin">
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </codeStyleSettings>
  </code_scheme>
</component>