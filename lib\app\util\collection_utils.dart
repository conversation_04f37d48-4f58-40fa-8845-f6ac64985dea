import 'dart:collection';

extension IterableMapEntryExtensions<K, V> on Iterable<MapEntry<K, V>> {
  Map<K, V> toMap({
    bool Function(K, K)? equals,
    int Function(K)? hashCode,
    bool Function(dynamic)? isValidKey,
  }) =>
      LinkedHashMap(equals: equals, hashCode: hashCode, isValidKey: isValidKey)..addEntries(this);
}

extension ListExtensions<T> on List<T> {
  T? lastWhereFromIndex(bool Function(T) predicate, int? fromIndex) {
    final index = lastIndexWhere(predicate, fromIndex);
    return index != -1 ? this[index] : null;
  }
}

extension IterableExtensions<T> on Iterable<T> {
  Iterable<T> add(T element) sync* {
    yield* this;
    yield element;
  }

  Iterable<T> addFirst(T element) sync* {
    yield element;
    yield* this;
  }
}

extension ComparatorExtensions<T> on Comparator<T> {
  Comparator<T> compose(List<Comparator<T>> rest) {
    assert(rest.isNotEmpty, "compose(): rest must not be empty!");
    return (a, b) {
      final first = this(a, b);
      if (first != 0) {
        return first;
      }
      for (final then in rest) {
        final result = then(a, b);
        if (result != 0) {
          return result;
        }
      }
      return 0;
    };
  }
}
