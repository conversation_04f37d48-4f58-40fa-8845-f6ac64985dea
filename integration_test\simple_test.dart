import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:logging/logging.dart';
import 'package:mobx/mobx.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/app/domain/user_choice.dart';
import 'package:qfiler/app/file_system/file_repository.dart';
import 'package:qfiler/app/file_system/real/path_repository.dart';
import 'package:qfiler/app/file_system/rust/rust_file_system_adapter.dart';
import 'package:qfiler/app/notification/notification_repository.dart';
import 'package:qfiler/app/notification/notification_store.dart';
import 'package:qfiler/app/persistence/data_repository.dart';
import 'package:qfiler/app/settings/settings_repository.dart';
import 'package:qfiler/app/task/file_op_executor.dart';
import 'package:qfiler/app/task/pending_file_store.dart';
import 'package:qfiler/app/task/task.dart';
import 'package:qfiler/app/task/task_planner.dart';
import 'package:qfiler/app/task/task_store.dart';
import 'package:qfiler/app/util/log_colorizer.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/command/command_context_repository.dart';
import 'package:qfiler/presentation/command/command_dispatcher.dart';
import 'package:qfiler/presentation/command/command_repository.dart';
import 'package:qfiler/presentation/command/keybind_manager.dart';
import 'package:qfiler/presentation/command/keybind_repository.dart';
import 'package:qfiler/presentation/directory_view/directory_view_store.dart';
import 'package:qfiler/presentation/directory_view/widgets/file_list.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/history/directory_state.dart';
import 'package:qfiler/presentation/history/history.dart';
import 'package:qfiler/presentation/history/history_store.dart';
import 'package:qfiler/presentation/pane/all_panes_store.dart';
import 'package:qfiler/presentation/pane/pane_store.dart';
import 'package:qfiler/presentation/rename/rename_store.dart';
import 'package:qfiler/presentation/root_store.dart';
import 'package:qfiler/rust/.gen/frb_generated.dart';

final logger = Logger("integration_test");

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    hierarchicalLoggingEnabled = true;
    FlutterError.onError = (details) {
      FlutterError.presentError(details);
      logger.shout("Unhandled Flutter error:", details.exception, details.stack);
    };
    PlatformDispatcher.instance.onError = (error, stack) {
      logger.shout("Unhandled Flutter Platform error:", error, stack);
      return true;
    };

    mainContext.config = mainContext.config.clone(disableErrorBoundaries: kDebugMode);

    // Configure colorized logging
    if (kDebugMode) {
      hierarchicalLoggingEnabled = true;
      Logger.root.level = Level.ALL;

      // Use LogColorizer to format log messages
      Logger.root.onRecord.listen((record) {
        String formattedMessage = LogColorizer.colorizeRecord(record);
        debugPrint(formattedMessage);
      });
    } else {
      Logger.root.level = Level.OFF;
    }

    await RustLib.init();
  });

  group('QFiler Integration Tests', () {
    testWidgets('Press tab to switch focus between panes', (WidgetTester tester) async {
      // Create temporary directories for testing
      final tempBaseDir = await io.Directory.systemTemp.createTemp('qfiler_integration_test_');
      final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
      final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();
      final dataTempDir = await io.Directory(p.join(tempBaseDir.path, 'data')).create();

      try {
        // Create some test files in both directories
        await io.File(p.join(leftTempDir.path, 'test_file_left.txt')).writeAsString('Left pane test file');
        await io.File(p.join(rightTempDir.path, 'test_file_right.txt')).writeAsString('Right pane test file');

        // Create a custom RootStore with temporary directories
        final rootStore = await createTestRootStore(
          leftInitialDir: RawPath(leftTempDir.path),
          rightInitialDir: RawPath(rightTempDir.path),
          dataDir: RawPath(dataTempDir.path),
        );

        // Pump the app
        await tester.pumpWidget(App(rootStore));
        await tester.pumpAndSettle();

        // Verify the app loaded successfully
        expect(find.byType(App), findsOneWidget);

        // Wait for the app to fully initialize
        await tester.pumpAndSettle(const Duration(seconds: 1));

        // Ensure left pane is the source pane and has focus
        rootStore.allPanesStore.setSourcePane(Side.left);

        // Initially, left pane should be the source pane
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

        // Debug: Print the focused row indices
        if (kDebugMode) {
          print('Left pane focused row index: ${rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex}');
          print('Right pane focused row index: ${rootStore.allPanesStore.right.directoryViewStore.focusedRowIndex}');
          print('Left pane files count: ${rootStore.allPanesStore.left.directoryViewStore.files.length}');
          print('Right pane files count: ${rootStore.allPanesStore.right.directoryViewStore.files.length}');
        }

        // Verify that the left pane has files and a focused row
        expect(rootStore.allPanesStore.left.directoryViewStore.files.length, greaterThan(0));
        expect(rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex, greaterThanOrEqualTo(0));

        // Find the file list widgets (there should be two - one for each pane)
        final fileLists = find.byType(FileList);
        expect(fileLists, findsNWidgets(2));

        // Press Tab key to switch focus to right pane
        if (kDebugMode) {
          print('Before Tab: Source pane is ${rootStore.allPanesStore.sourcePaneSide}');
        }
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        // Debug: Check what happened after Tab
        if (kDebugMode) {
          print('After first Tab: Source pane is ${rootStore.allPanesStore.sourcePaneSide}');
        }

        // Verify that the source pane switched to right
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.right));

        // Verify visual state: right pane should now have the source pane color
        await _verifyFocusedRowColor(tester, rootStore, Side.right, Colors.grey.shade300);

        // Press Tab key again to switch back to left pane
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle(const Duration(milliseconds: 500));

        // Debug: Check what happened after second Tab
        if (kDebugMode) {
          print('After second Tab: Source pane is ${rootStore.allPanesStore.sourcePaneSide}');
        }

        // Verify that the source pane switched back to left
        expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

        // Verify visual state: left pane should again have the source pane color
        await _verifyFocusedRowColor(tester, rootStore, Side.left, Colors.grey.shade300);
      } finally {
        // Clean up temporary directories
        if (await tempBaseDir.exists()) {
          await tempBaseDir.delete(recursive: true);
        }
      }
    });
  });
}

/// Creates a test RootStore with custom temporary directories
Future<RootStore> createTestRootStore({
  required RawPath leftInitialDir,
  required RawPath rightInitialDir,
  required RawPath dataDir,
}) async {
  // Use the same setup as the main RootStore.create() but with custom directories
  final fileSystem = RustFileSystemAdapter();
  final pendingOperationRepository = PendingOperationRepository();
  final fileRepository = FileRepository(PathRepository(fileSystem), pendingOperationRepository);

  final notificationRepository = NotificationRepository();
  final notificationStore = NotificationStore();
  final executor = FileOpExecutor(
    fileSystem,
    pendingOperationRepository,
    _TestShowConfirmOverwriteDialog(),
    _TestShowFileOpErrorDialog(),
  );

  final jobPlanner = JobPlanner(fileRepository: fileRepository, pendingOperationRepository: pendingOperationRepository);
  final jobStore = JobStore(executor, notificationStore, jobPlanner);

  // Create a custom data repository with the test data directory
  final dataRepository = DataRepository(
    DataRepositoryConfig(userDataDir: dataDir),
    fileSystem,
    notificationRepository,
  );

  final settingsRepository = await SettingsRepository.create(dataRepository);
  final commandRepository = CommandRepository.create();
  final keyBindRepository = await KeyBindRepository.create(dataRepository);
  final commandContextRepository = CommandContextRepository();

  final renameStore = RenameStore(jobStore, notificationStore, commandContextRepository);
  renameStore.setupRenameSyncListener();

  // Create pane stores with custom initial directories
  final left =
      await createTestPaneStore(settingsRepository, fileRepository, dataRepository, notificationStore, renameStore, Side.left, leftInitialDir);
  final right =
      await createTestPaneStore(settingsRepository, fileRepository, dataRepository, notificationStore, renameStore, Side.right, rightInitialDir);

  // Create AllPanesStore
  final allPanesStore = AllPanesStore(left: left, right: right);

  // Create CommandDispatcher
  final commandDispatcher = CommandDispatcher(
    allPanesStore: allPanesStore,
    commandContextRepository: commandContextRepository,
    jobStore: jobStore,
    notificationStore: notificationStore,
  );

  // Create KeyBindManager
  final keyBindManager = KeyBindManager(
    keyBindRepository,
    commandRepository,
    commandContextRepository,
    commandDispatcher,
  );

  // Set the AllPanesStore reference in the pane stores
  left.setAllPanesStore(allPanesStore);
  right.setAllPanesStore(allPanesStore);

  return RootStore(
    fileRepository: fileRepository,
    allPanesStore: allPanesStore,
    settingsRepository: settingsRepository,
    commandRepository: commandRepository,
    keyBindRepository: keyBindRepository,
    commandContextRepository: commandContextRepository,
    keyBindManager: keyBindManager,
    commandDispatcher: commandDispatcher,
    renameStore: renameStore,
  );
}

/// Creates a test pane store with a custom initial directory
Future<PaneStore> createTestPaneStore(
  SettingsRepository settingsRepository,
  FileRepository fileRepository,
  DataRepository dataRepository,
  NotificationStore notificationStore,
  RenameStore renameStore,
  Side side,
  RawPath initialDir,
) async {
  final historyStore = await createTestHistoryStore(dataRepository, side, initialDir);
  final directoryViewStore = DirectoryViewStore(historyStore, fileRepository, renameStore, side.name);
  final paneStore = PaneStore(historyStore, directoryViewStore, side);
  return paneStore;
}

/// Creates a test history store with a custom initial directory
Future<HistoryStore> createTestHistoryStore(DataRepository dataRepository, Side side, RawPath initialDir) async {
  // Create the history store using the standard method
  final historyStore = await HistoryStore.create(dataRepository, side);

  // Replace the default history with our custom initial directory
  historyStore.histories.clear();
  historyStore.histories.add(History([DirectoryState(initialDir)], 0));
  historyStore.setCurrentIndex(0);

  return historyStore;
}

/// Test implementation of ShowConfirmOverwriteDialog
class _TestShowConfirmOverwriteDialog implements ShowConfirmOverwriteDialog {
  @override
  Future<OverwriteChoice> call({required Operation op, required bool selectOverwriteAll}) async {
    // For tests, always return overwrite
    return OverwriteChoice(overwriteMode: OverwriteMode.overwrite, all: selectOverwriteAll);
  }
}

/// Test implementation of ShowFileOpErrorDialog
class _TestShowFileOpErrorDialog implements ShowFileOpErrorDialog {
  @override
  Future<OperationErrorChoice> call({required Operation op, required Object error}) async {
    // For tests, always return retry
    return OperationErrorChoice(mode: OperationErrorMode.retry, all: false);
  }
}

/// Helper function to verify the focused row color in a specific pane
Future<void> _verifyFocusedRowColor(WidgetTester tester, RootStore rootStore, Side side, Color expectedColor) async {
  // Get the pane store for the specified side
  final paneStore = side == Side.left ? rootStore.allPanesStore.left : rootStore.allPanesStore.right;
  final focusedRowIndex = paneStore.directoryViewStore.focusedRowIndex;

  // Verify that there is a focused row
  expect(focusedRowIndex, greaterThanOrEqualTo(0));

  // Note: In a real integration test, we would need to find the specific Davi widget
  // and verify its row colors. However, this is complex because Davi is a third-party
  // widget and we'd need to access its internal structure.
  // For now, we'll verify the logical state and add a comment about visual verification.

  // Verify logical state: the pane should be marked as source if it has the expected source color
  if (expectedColor == Colors.grey.shade300) {
    expect(paneStore.isSource, isTrue, reason: 'Pane with source color should be the source pane');
  } else {
    expect(paneStore.isSource, isFalse, reason: 'Pane with target color should not be the source pane');
  }

  // TODO: Add visual verification by finding the Davi widget and checking row colors
  // This would require accessing the Davi widget's internal structure or using
  // a custom test widget that exposes the row colors for testing.
}
