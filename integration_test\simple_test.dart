import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/rust/.gen/frb_generated.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  setUpAll(() async => await RustLib.init());
  testWidgets('Can call rust function', (WidgetTester tester) async {
    // await tester.pumpWidget(App(RootStore()));
    expect(find.textContaining('Result: `Hello, <PERSON>!`'), findsOneWidget);
  });
}
