import 'package:mobx/mobx.dart';

extension ObservableFutureExtensions<T> on ObservableFuture<T> {
  bool get isPending => status == FutureStatus.pending;
  bool get isFulfilled => status == FutureStatus.fulfilled;
  bool get isError => status == FutureStatus.rejected;
}

extension IterableExtension<T> on Iterable<T> {
  ObservableList<T> asObservable({ReactiveContext? context, String? name}) => ObservableList<T>.of(this, context: context, name: name);
}

extension ObservableListExtension<T> on ObservableList<T> {
  // TODO: Unit test this!
  // Detect all ranges in this list that have a different value in replacement list and replace only them.
  void replace(List<T> replacement) {
    replaceRange(0, length, replacement);

    // TODO: This requires implementing that nice 'replace' method.
    // TODO: The below is an attempt to optimize the replaceRange call above by using granular ranges.
    // TODO: There's a bug in there which causes it to not work when replacement.length > length,
    // TODO: and I'm not even sure what the benefit of the optimization is, so its disabled for now.
    // if (isEmpty) {
    //   addAll(replacement);
    //   return;
    // }
    // if (replacement.isEmpty) {
    //   clear();
    //   return;
    // }
    //
    // // Find all ranges in this list that have different items in replacement list.
    // final List<List<int>> ranges = [];
    // int? start;
    // int? end;
    // for (int i = 0; i < min(length, replacement.length); i++) {
    //   if (i >= replacement.length || this[i] != replacement[i]) {
    //     // Diff found.
    //     if (start != null) {
    //       end = i + 1;
    //     } else {
    //       start = i;
    //     }
    //   } else {
    //     // Diff ended.
    //     if (start != null) {
    //       ranges.add([start, i]);
    //       start = null;
    //       end = null;
    //     }
    //   }
    // }
    // if (start != null) {
    //   end = max(length, replacement.length);
    //   ranges.add([start, end]);
    // }
    //
    // // Replace all ranges.
    // for (final range in ranges) {
    //   final sublist = replacement.sublist(range[0], min(range[1], replacement.length));
    //   replaceRange(range[0], min(range[1], length), sublist);
    // }
  }

  // Adds the element as the first element of the list and watches the list for updates.
  ObservableList<T> addingFirst(T element) {
    final list = ObservableList<T>.of([element, ...this]);
    // FIXME: Observe this list for changes and apply them to target list.
    return list;
  }
}

extension ObservableMapExtension<K, V> on ObservableMap<K, V> {
  void replace(Iterable<MapEntry<K, V>> entries) {
    clear();
    addEntries(entries);
  }
}

ReactionDisposer reactionWithPrev<T>(
  T Function(Reaction) fn,
  void Function(T? prev, T curr, bool firstCall) effect, {
  String? name,
  int? delay,
  bool? fireImmediately,
  EqualityComparer<T>? equals,
  ReactiveContext? context,
  void Function(Object, Reaction)? onError,
}) {
  bool firstCall = true;
  T? prev;
  return reaction(
    fn,
    (curr) {
      effect(prev, curr, firstCall);
      prev = curr;
      firstCall = false;
    },
    name: name,
    delay: delay,
    fireImmediately: fireImmediately,
    equals: equals,
    context: context,
    onError: onError,
  );
}

mixin Disposable {
  void dispose();
}
