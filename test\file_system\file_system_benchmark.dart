import 'dart:async';
import 'dart:io' as io;
import 'dart:math';

import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart' show Path, RawPath;
import 'package:qfiler/app/file_system/file_system.dart';
import 'package:qfiler/app/file_system/rust/rust_file_system_adapter.dart';
import 'package:qfiler/rust/.gen/frb_generated.dart';

const int kWarmupRuns = 5;
const int kMeasurementRuns = 100;

final FileSystem _fs = RustFileSystemAdapter();
final String _tempBaseDir = p.join(io.Directory.systemTemp.path, 'qfiler_benchmark');

String _humanReadableDuration(Duration duration) {
  final totalMicroseconds = duration.inMicroseconds;
  if (totalMicroseconds < 0) return 'N/A';
  if (totalMicroseconds == 0) return '0 µs';

  final s = totalMicroseconds ~/ 1000000;
  final ms = (totalMicroseconds % 1000000) ~/ 1000;
  final us = totalMicroseconds % 1000;

  List<String> parts = [];
  if (s > 0) parts.add('$s s');
  if (ms > 0) parts.add('$ms ms');
  if (us > 0) {
    // Add µs if it's the only non-zero part, or if other parts (s or ms) exist.
    if (s > 0 || ms > 0 || parts.isEmpty) {
      parts.add('$us µs');
    }
  }
  
  // Fallback if all parts somehow ended up being zero but totalMicroseconds was not (e.g. very small duration that rounds components to 0)
  // This should ideally be caught by `totalMicroseconds == 0` check, but as a safeguard:
  if (parts.isEmpty) return '$totalMicroseconds µs';

  return parts.join(' ');
}

class InterEventStat {
  final Duration duration;
  final String entryPath;
  final int entryIndex;

  InterEventStat(this.duration, this.entryPath, this.entryIndex);

  @override
  String toString() {
    return '${_humanReadableDuration(duration)} (entry #${entryIndex}: ${p.basename(entryPath)})';
  }
}

class BenchmarkResult {
  final int itemCount;
  final List<Duration> totalTimes;
  final List<InterEventStat> allInterEventTimes;

  Duration minTotalTime = Duration.zero;
  Duration maxTotalTime = Duration.zero;
  Duration avgTotalTime = Duration.zero;
  Duration stdDevTotalTime = Duration.zero;
  Duration p95TotalTime = Duration.zero;
  Duration p99TotalTime = Duration.zero;
  Duration p999TotalTime = Duration.zero;

  InterEventStat? minInterEventTime;
  InterEventStat? maxInterEventTime;
  Duration avgInterEventTime = Duration.zero;

  BenchmarkResult(this.itemCount, this.totalTimes, this.allInterEventTimes) {
    if (totalTimes.isNotEmpty) {
      totalTimes.sort();
      minTotalTime = totalTimes.first;
      maxTotalTime = totalTimes.last;
      final totalMicroseconds = totalTimes.map((d) => d.inMicroseconds).reduce((a, b) => a + b);
      avgTotalTime = Duration(microseconds: totalMicroseconds ~/ totalTimes.length);

      final double mean = avgTotalTime.inMicroseconds.toDouble();
      if (totalTimes.length > 1) {
        final double variance = totalTimes
            .map((d) => pow(d.inMicroseconds - mean, 2))
            .reduce((a, b) => a + b) / (totalTimes.length - 1);
        stdDevTotalTime = Duration(microseconds: sqrt(variance).round());
      } else {
        stdDevTotalTime = Duration.zero;
      }

      p95TotalTime = totalTimes[min((totalTimes.length * 0.95).ceil() - 1, totalTimes.length - 1).clamp(0, totalTimes.length - 1)];
      p99TotalTime = totalTimes[min((totalTimes.length * 0.99).ceil() - 1, totalTimes.length - 1).clamp(0, totalTimes.length - 1)];
      p999TotalTime = totalTimes[min((totalTimes.length * 0.999).ceil() - 1, totalTimes.length - 1).clamp(0, totalTimes.length - 1)];
    }

    if (allInterEventTimes.isNotEmpty) {
      allInterEventTimes.sort((a, b) => a.duration.compareTo(b.duration));
      minInterEventTime = allInterEventTimes.first;
      maxInterEventTime = allInterEventTimes.last;
      final totalInterEventMicroseconds = allInterEventTimes.map((s) => s.duration.inMicroseconds).reduce((a, b) => a + b);
      avgInterEventTime = Duration(microseconds: totalInterEventMicroseconds ~/ allInterEventTimes.length);
    }
  }

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('Benchmark for $itemCount items:');
    buffer.writeln('  Total Operation Time (list):');
    buffer.writeln('    Min:    ${_humanReadableDuration(minTotalTime)}');
    buffer.writeln('    Max:    ${_humanReadableDuration(maxTotalTime)}');
    buffer.writeln('    Avg:    ${_humanReadableDuration(avgTotalTime)}');
    buffer.writeln('    StdDev: ${_humanReadableDuration(stdDevTotalTime)}');
    buffer.writeln('    P95:    ${_humanReadableDuration(p95TotalTime)}');
    buffer.writeln('    P99:    ${_humanReadableDuration(p99TotalTime)}');
    buffer.writeln('    P99.9:  ${_humanReadableDuration(p999TotalTime)}');
    buffer.writeln('  Inter-Event Time (time between stream entries):');
    if (minInterEventTime != null) buffer.writeln('    Min: $minInterEventTime'); // InterEventStat.toString() already formats its duration
    if (maxInterEventTime != null) buffer.writeln('    Max: $maxInterEventTime');
    buffer.writeln('    Avg: ${_humanReadableDuration(avgInterEventTime)}');
    return buffer.toString();
  }
}

Future<void> _createTestDirectory(String dirPath, int numItems) async {
  final ioDir = io.Directory(dirPath);
  if (await ioDir.exists()) {
    await ioDir.delete(recursive: true);
  }
  await ioDir.create(recursive: true);

  for (int i = 0; i < numItems; i++) {
    final name = 'item_$i';
    if (i % 2 == 0) {
      await _fs.writeFile(RawPath(p.join(dirPath, '$name.txt')), '');
    } else {
      await _fs.mkdir(RawPath(p.join(dirPath, name))); // recursive defaults to false
    }
  }
}

Future<void> _deleteTestDirectory(String dirPath) async {
  final ioDir = io.Directory(dirPath);
  if (await ioDir.exists()) {
    await ioDir.delete(recursive: true);
  }
}

Future<BenchmarkResult> _runBenchmarkForSize(int numItems, String testDirName) async {
  final testDirPath = p.join(_tempBaseDir, testDirName);
  final rawTestDirPath = RawPath(testDirPath);
  print('Running benchmark for $numItems items in $testDirPath...');

  // Warmup
  print('  Warmup ($kWarmupRuns runs)...');
  await _createTestDirectory(testDirPath, numItems);
  for (int i = 0; i < kWarmupRuns; i++) {
    await (await _fs.list(rawTestDirPath)).drain();
  }
  await _deleteTestDirectory(testDirPath);

  final List<Duration> totalTimes = [];
  final List<InterEventStat> allInterEventTimes = [];

  print('  Measurement ($kMeasurementRuns runs)...');
  for (int i = 0; i < kMeasurementRuns; i++) {
    if ((i + 1) % 10 == 0 || i == 0 || i == kMeasurementRuns -1) {
      print('    Run ${i + 1}/$kMeasurementRuns...');
    }
    await _createTestDirectory(testDirPath, numItems);

    final stopwatch = Stopwatch()..start();
    Stopwatch? interEventStopwatch;
    int entryCount = 0;

    final stream = await _fs.list(rawTestDirPath);
    await for (final Path entry in stream) {
      if (interEventStopwatch != null) {
        allInterEventTimes.add(InterEventStat(interEventStopwatch.elapsed, entry.absolutePath, entryCount));
      }
      interEventStopwatch = Stopwatch()..start();
      entryCount++;
    }
    stopwatch.stop();
    totalTimes.add(stopwatch.elapsed);

    await _deleteTestDirectory(testDirPath);
  }

  return BenchmarkResult(numItems, totalTimes, allInterEventTimes);
}

Future<void> main() async {
  print('Initializing Rust library...');
  final rustCrateName = 'rust_lib_qfiler';
  final dylibPath = p.join(
    io.Directory.current.path,
    'rust',
    'target',
    'debug',
    '$rustCrateName${io.Platform.isWindows ? '.dll' : io.Platform.isMacOS ? '.dylib' : '.so'}',
  );

  try {
    final externalLibrary = ExternalLibrary.open(dylibPath);
    await RustLib.init(externalLibrary: externalLibrary);
    print('Rust library initialized successfully from: $dylibPath');
  } catch (e) {
    print('Error initializing Rust library: $e');
    print('Ensure the Rust library is built (e.g., via `cargo build` in the `rust` directory).');
    print('Expected dylib path: $dylibPath');
    io.exit(1);
  }

  print('Initializing FileSystem (Rust implementation)...');

  final tempDir = io.Directory(_tempBaseDir);
  if (await tempDir.exists()) {
    await tempDir.delete(recursive: true);
  }
  await tempDir.create(recursive: true);
  print('Created temporary base directory: $_tempBaseDir');

  final List<int> itemCounts = [100, 1000, 5000]; // Changed 10000 to 5000
  final List<BenchmarkResult> results = [];

  try {
    for (int count in itemCounts) {
      final result = await _runBenchmarkForSize(count, 'dir_$count');
      results.add(result);
      print(result);
    }
  } catch (e, s) {
    print('An error occurred during benchmarking: $e');
    print('Stack trace:\n$s');
  } finally {
    print('\n--- Overall Benchmark Summary ---');
    for (final result in results) {
      print(result);
    }

    print('Cleaning up temporary base directory: $_tempBaseDir');
    await _deleteTestDirectory(_tempBaseDir);
    print('Benchmark finished.');
    io.exit(0);
  }
}
