import 'package:analyzer/dart/element/element.dart';
import 'package:build/build.dart';
import 'package:mobx_codegen/src/template/method_override.dart';
import 'package:mobx_codegen/src/template/store.dart';

import 'debounced_computed_annotation.dart';

/// A generator for MobX [DebouncedComputed] annotations.
///
/// This generator processes methods annotated with [DebouncedComputed]
/// and generates code that creates debounced computed properties.
class DebouncedComputedGenerator {
  /// Generates code for a method annotated with [DebouncedComputed].
  ///
  /// The [method] is the annotated method element.
  /// The [annotation] is the [DebouncedComputed] annotation instance.
  /// The [storeTemplate] is the template for the store class.
  ///
  /// Returns the generated code for the debounced computed property.
  String generate(
    MethodElement method,
    DebouncedComputed annotation,
    StoreTemplate storeTemplate,
  ) {
    final methodName = method.name;
    final returnType = method.returnType.getDisplayString(withNullability: true);
    final cacheFieldName = '_${methodName}DebouncedCache';

    // Add a field to store the debounced computed instance
    final field = '''
  Computed<$returnType>? $cacheFieldName;
''';

    // Generate the getter method
    final methodOverride = MethodOverrideTemplate();
    methodOverride.name = methodName;
    methodOverride.returnType = returnType;

    final body = '''
    $cacheFieldName ??= getDebouncedComputed(
      '$methodName',
      () => super.$methodName,
      ${annotation.debounceMillis},
    );
    return $cacheFieldName!.value;
''';

    return '''
@override
$returnType get $methodName {
$body
}
$field''';
  }
}

/// A builder for generating code for [DebouncedComputed] annotations.
class DebouncedComputedBuilder implements Builder {
  @override
  Map<String, List<String>> get buildExtensions => {
        '.dart': ['.debounced_computed.g.part'],
      };

  @override
  Future<void> build(BuildStep buildStep) async {
    // Implementation would go here
    // This would be integrated with the mobx_codegen package
  }
}
