targets:
  $default:
    sources:
      include:
        - "$package$"
        - "lib/$lib$"
        - "lib/**.dart"
      exclude:
        # Exclude the Dart source files that will be handled by the 'rust_gen' target.
        - "lib/rust/**.dart"
        # Exclude generated files from being considered as sources for new generation
        - "lib/**/.gen/*.dart"
    builders:
      source_gen:combining_builder:
        options:
          build_extensions:
            '{{dir}}/{{file}}.dart': '{{dir}}/./.gen/{{file}}.g.dart'
      # json_serializable:
      #   options:
      #     field_rename: snake
      mek_data_class_generator:data_class:
        enabled: true
        options:
          comparable: true
          stringify: true
          copyable: true
          changeable: false
          changes_visible: false
          create_fields_class: false
      mobx_codegen:mobx_generator:
        options:
          debouncedComputed: DebouncedComputed
      mockito|mockBuilder:
        options:
          build_extensions:
            '{{dir}}/{{file}}.dart': '{{dir}}/.gen/{{file}}.mocks.dart'

  rust_gen:
    sources:
      - "lib/rust/.gen/**.dart"
    builders:
      source_gen:combining_builder:
        options:
          build_extensions:
            '{{dir}}/{{file}}.dart': '{{dir}}/{{file}}.g.dart'