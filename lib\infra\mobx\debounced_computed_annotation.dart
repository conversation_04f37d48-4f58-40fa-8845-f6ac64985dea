/// Annotation for creating debounced computed properties in MobX.
///
/// This annotation can be used on getter methods to create a debounced
/// computed property. The property will only be recalculated after the
/// specified debounce period has elapsed since the last change.
///
/// Example:
/// ```dart
/// @DebouncedComputed(debounceMillis: 300)
/// int get debouncedValue => expensiveCalculation();
/// ```
class DebouncedComputed {
  /// The debounce period in milliseconds.
  final int debounceMillis;

  /// Creates a new [DebouncedComputed] annotation.
  ///
  /// The [debounceMillis] parameter specifies how long to wait after changes
  /// before recalculating the computed value.
  const DebouncedComputed({required this.debounceMillis});
}
