import 'dart:async';
import 'dart:collection';

import 'package:dartx/dartx.dart';
import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';

import '../domain/file.dart';
import '../domain/path.dart';
import '../task/pending_file_store.dart';
import '../task/task.dart';
import '../util/async_value.dart';
import '../util/log_utils.dart';
import 'real/path_repository.dart';

part '.gen/file_repository.g.dart';

class WatchFileSession {
  final FileRepositoryBase _fileRepository;
  final String _clientId;
  final int _maxWatchedDirs;

  final LinkedHashSet<RawPath> _watchedDirs = LinkedHashSet();

  WatchFileSession(this._fileRepository, this._clientId, {int maxWatchedDirs = 0}) : _maxWatchedDirs = maxWatchedDirs;

  // This is an idempotent operation, it will only watch a directory once per client.
  @action
  Files watchDir(RawPath dir) {
    if (_watchedDirs.add(dir) && _maxWatchedDirs > 0) {
      if (kDebugMode) {
        logger.finer("$dir watch($_clientId): Watched dirs $_watchedDirs");
      }
      if (_watchedDirs.length > _maxWatchedDirs) {
        if (kDebugMode) {
          logger.fine("$dir watch($_clientId): Max watched dirs ($_maxWatchedDirs) exceeded. Un-watching oldest dir ${_watchedDirs.first}");
        }
        unwatchDir(_watchedDirs.first);
      }
    }
    return _fileRepository._watch(dir, _clientId);
  }

  @action
  void unwatchDir(RawPath dir) {
    _watchedDirs.remove(dir);
    _fileRepository._unwatch(dir, _clientId);
  }

  // When un-watching, unwatch the children before the parents - this is done by sorting.
  // This prevents situations where children notice their parents are gone and reload themselves.
  @action
  void unwatchAll() => _watchedDirs.sortedDescending().forEach(unwatchDir);

  static final logger = loggerFor(WatchFileSession);
}

class FileRepository extends FileRepositoryBase with _$FileRepository {
  FileRepository(super.pathRepository, super.pendingFileStore);

  @override
  String toString() => "FileRepository{watchedDirectories: ${_watchedDirectories.keys}}";
}

abstract class FileRepositoryBase with Store {
  FileRepositoryBase(this._pathRepository, this._pendingFileStore);

  final PathRepository _pathRepository;
  final PendingOperationRepository _pendingFileStore;

  final ObservableMap<RawPath, Files> _watchedDirectories = ObservableMap();

  late final emptyFiles = _createFiles(RawPath.Null, _pathRepository.emptyPaths, watch: false);

  WatchFileSession createWatchFileClient(String clientId, {int maxWatchedDirs = 0}) =>
      WatchFileSession(this, clientId, maxWatchedDirs: maxWatchedDirs);

  Files _watch(RawPath dir, String clientId) {
    var files = _watchedDirectories[dir];
    if (files == null) {
      if (kDebugMode) {
        logger.finer("$dir _watch($clientId): First watcher");
      }
      files = _createWatchedDir(dir);
      _watchedDirectories[dir] = files;
    } else {
      if (kDebugMode) {
        logger.finer("$dir _watch($clientId): Joining existing watchers ${files._watchers.toList()}");
      }
    }
    files._addWatcher(clientId);
    return files;
  }

  Files _createWatchedDir(RawPath dir) {
    final files = _createFiles(dir, _pathRepository.watchDir(dir), watch: true);

    // Link ourselves with our parent.
    final parent = dir.parent;
    final parentFiles = parent != null ? _get(parent) : null;
    if (parentFiles != null) {
      files._setWatchedParent(parentFiles);
      parentFiles._addWatchedChild(files);
    }

    // Link ourselves with all already watched children.
    for (final watchedChild in _watchedDirectories.values) {
      if (dir.isParentOf(watchedChild.dir.path)) {
        watchedChild._setWatchedParent(files);
        files._addWatchedChild(watchedChild);
      }
    }

    return files;
  }

  void _unwatch(RawPath dir, String clientId) {
    final files = _watchedDirectories[dir];
    if (kDebugMode) {
      if (files == null || !files._watchers.contains(clientId)) {
        logger.finer("$dir _unwatch($clientId): Not watched by this client");
      }
    }
    if (files == null) {
      return;
    }

    files._removeWatcher(clientId);
    if (files._watchers.isEmpty) {
      if (kDebugMode) {
        logger.finer("$dir _unwatch($clientId): No more watchers, disposing...");
      }
      _disposeWatchedDir(dir, files);
    } else {
      if (kDebugMode) {
        logger.finer("$dir _unwatch($clientId): Still watched by ${files._watchers.toList()}");
      }
    }
  }

  @action
  void _disposeWatchedDir(RawPath dir, Files files) {
    _watchedDirectories.remove(dir);
    files._dispose();
  }

  Files fetchDir(RawPath dir) => _get(dir) ?? _createFiles(dir, _pathRepository.fetchDir(dir), watch: false);

  Files? _get(RawPath dir) => _watchedDirectories[dir];

  Files _createFiles(RawPath dir, Paths paths, {required bool watch}) {
    final pendingFiles = _pendingFileStore.fetchDir(dir, watch);
    return Files._(ParentDir(this, dir, watch: watch), paths, pendingFiles);
  }

  // File? _getDirectoryFromParent(Path dir) {
  //   final parent = dir.parent;
  //   if (parent == null) {
  //     throwIfDebug("No parent for $dir");
  //     return null;
  //   }
  //   final parentFiles = _watchedDirectories[parent];
  //   if (parentFiles == null) {
  //     return null;
  //   }
  //   return parentFiles.(dir);
  // }

// @action
// void setDirPathRenamedToTarget(Path? dirPathRenamedToTarget) {
//   this.dirPathRenamedToTarget = dirPathRenamedToTarget
// }

  static final logger = loggerFor(FileRepository, Level.INFO);
}

class Files = FilesBase with _$Files;

abstract class FilesBase extends AsyncValue<List<File>> with Store {
  FilesBase._(this._dir, this._paths, this._pendingOperations) {
    _disposers.addAll([
      _watchDirInParent().call,
      _observePaths().call,
      _observePendingOperations().call,
      // _files.dispose,
    ]);
  }

  final ParentDir _dir;
  final Paths _paths;
  final PendingOperations _pendingOperations;

  final _filesByName = ObservableMap<String, File>();

  // TODO: Split into liveFiles & filesSnapshot?
  // late final DisposableAsyncValue<List<File>> _files;

  // Watching is idempotent - each clientId may only watch a directory once.
  final Set<String> _watchers = {};

  @observable
  FilesBase? _watchedParent;

  final Set<FilesBase> _watchedChildren = {};

  final List<void Function()> _disposers = [];

  // TODO: Maybe don't use snapshots here? Why not just use a mapped observable?
  @computed
  AsyncValueSnapshot<File?> get _dirInParent {
    final parent = _watchedParent;
    return parent == null ? nullDirInParent : parent.snapshot.map((_) => parent.get(dir.name));
  }

  // Reload ourselves if we previously didn't exist but now we do or vice versa.
  ReactionDisposer _watchDirInParent() {
    _DirInParent? prevDirInParent;
    // 'reaction' and 'reactionWithPrev' were missing notifications for some reason.
    return autorun((_) {
      final dirInParent = _dirInParent;
      // Ignore the initial change of dirInParent from null to non-null,
      // it will always happen at least once if we're watching a directory that actually exists.
      if (prevDirInParent != null) {
        if (prevDirInParent!.value == null && !prevDirInParent!.isLoading && dirInParent.value != null) {
          if (kDebugMode) {
            logger.info("$dir _watchDirInParent(): Previously didn't exist in parent ${dirInParent.value!.parent} but now does, reloading...");
          }
          reload();
        } else if (prevDirInParent!.value != null && dirInParent.value == null && !dirInParent.isLoading) {
          if (kDebugMode) {
            logger.info("$dir _watchDirInParent(): Previously existed but now doesn't, reloading...");
          }
          reload();
        }
      }
      prevDirInParent = dirInParent;
    }, name: 'Files.watchDirInParent $dir');
  }

  Dispose _observePaths() {
    return _paths.observe((change) {
      final fileName = change.key!;
      switch (change.type) {
        case OperationType.add || OperationType.update:
          if (kDebugMode) {
            logger.finest("$dir _observePaths($fileName) Path added/updated");
          }
        case OperationType.remove:
          if (kDebugMode) {
            logger.finest("$dir _observePaths($fileName) Path removed");
          }
          break;
        case null:
          throw Exception('Unknown operation type');
      }

      _calcFile(fileName);
    }, fireImmediately: true);
  }

  Dispose _observePendingOperations() {
    return _pendingOperations.observe((change) {
      final fileName = change.key!;

      switch (change.type) {
        case OperationType.add:
          if (kDebugMode) {
            logger.finest("$dir _observePendingOperations($fileName) Op added: ${change.newValue!}");
          }

        case OperationType.update:
          final newOperations = change.newValue!;
          final oldOperations = change.oldValue!;
          if (kDebugMode) {
            logger.finest("$dir _observePendingOperations($fileName) Op updated: $oldOperations -> $newOperations");
          }

          // Find operations that were in the old list but not in the new list
          // These are operations that have completed - force apply their effects
          for (final oldOp in oldOperations) {
            if (!newOperations.contains(oldOp)) {
              _applyFinishedOperation(oldOp, fileName);
            }
          }

        case OperationType.remove:
          // When all operations are removed, it means they've all completed
          // Force apply the final state of the last operation
          if (kDebugMode) {
            logger.finest("$dir _observePendingOperations($fileName) Op removed: ${change.oldValue}");
          }
          if (change.oldValue != null && change.oldValue!.isNotEmpty) {
            _applyFinishedOperation(change.oldValue!.last, fileName);
          }
        case null:
          throw Exception('Unknown operation type');
      }

      _calcFile(fileName);
    }, fireImmediately: true);
  }

  /// Applies the effects of a completed operation immediately without waiting for file system notifications
  /// @param op The operation that was completed
  /// @param notificationFileName The file name for which we received the notification (can be source or destination)
  void _applyFinishedOperation(Operation op, String notificationFileName) {
    // Only apply successful operations
    if (op.status != OperationStatus.completed) {
      return;
    }
    if (kDebugMode) {
      logger.finest("$_dir _applyFinishedOperation($notificationFileName) $op");
    }

    switch (op) {
      case CopyOperation(source: var source, destination: var destination):
        if (notificationFileName == destination.name) {
          final destFile = source.copyWith(path: destination, lifecycle: FileLifecycle.exists);
          _forceSetFile(destFile);
        }

      case MoveOperation(source: var source, destination: var destination):
        if (notificationFileName == source.name) {
          _forceRemoveFile(source);
        } else if (notificationFileName == destination.name) {
          final destFile = source.copyWith(path: destination, lifecycle: FileLifecycle.exists);
          _forceSetFile(destFile);
        }

      case RenameOperation(source: var source, destination: var destination):
        if (notificationFileName == source.name) {
          _forceRemoveFile(source);
        } else if (notificationFileName == destination.name) {
          final destFile = source.copyWith(path: destination, lifecycle: FileLifecycle.exists);
          _forceSetFile(destFile);
        }

      case DeleteOperation(source: var source):
        if (notificationFileName == source.name) {
          _forceRemoveFile(source);
        }

      case CreateFileOperation(source: var source):
        if (notificationFileName == source.name) {
          final newFile = source.copyWith(lifecycle: FileLifecycle.exists);
          _forceSetFile(newFile);
        }

      case CalcDirectorySizeOperation():
        // Size calculation doesn't affect file system state
        break;

      case SourceDestOperation():
        // Should never happen.
        throw UnimplementedError();
    }
  }

  /// This is used to immediately update the UI when an operation completes
  @action
  void _forceSetFile(File file) {
    if (kDebugMode) {
      logger.finest("$_dir _forceSetFile($file)");
    }
    assert(file.parent == _dir.file.path, 'File ${file.path} does not belong to this directory ${_dir.file.path}');
    _filesByName[file.name] = file;
    _paths.forceSet(file.path.withStats(file.stats));
  }

  /// This is used to immediately update the UI when an operation completes
  @action
  void _forceRemoveFile(File file) {
    if (kDebugMode) {
      logger.finest("$_dir _forceRemoveFile($file)");
    }
    assert(file.parent == _dir.file.path, 'File ${file.path} does not belong to this directory ${_dir.file.path}');
    _filesByName.remove(file.name);
    _paths.forceRemove(file.path.withStats(file.stats));
  }

  /// Specialized method for rename operations where both source and destination are in the same directory
  @action
  void _forceRenameFile(File source, File destination) {
    if (kDebugMode) {
      logger.finest("$_dir _forceRenameFile($source, $destination)");
    }
    assert(source.parent == _dir.file.path, 'Source file ${source.path} does not belong to this directory ${_dir.file.path}');
    assert(destination.parent == _dir.file.path, 'Destination path ${destination.path} does not belong to this directory ${_dir.file.path}');

    // Remove the source file and add the destination file
    _filesByName.remove(source.name);
    _filesByName[destination.name] = destination;

    // Update the paths
    _paths.forceRemove(source.path.withStats(source.stats));
    _paths.forceSet(destination.path.withStats(destination.stats));
  }

  @action
  void _calcFile(String fileName) {
    final realPath = _paths.get(fileName);
    final operations = _pendingOperations.get(fileName);

    String? realPathExists;
    if (kDebugMode) {
      realPathExists = realPath != null ? "exists" : "doesn't exist";
      RawPath currentPath = _dir.file.path.child(fileName);
      assert(realPath == null || realPath.path == currentPath, 'Path $currentPath does not match realPath $realPath');
    }

    File? file;
    FileLifecycle? lifecycle = realPath != null ? FileLifecycle.exists : null;

    if (operations != null) {
      // Apply each operation to determine the file and lifecycle state
      for (final op in operations) {
        final isSource = op.source.name == fileName;
        final isDest = op.destination?.name == fileName;
        if (kDebugMode) {
          RawPath currentPath = _dir.file.path.child(fileName);
          assert(op.source.path == currentPath || op.destination == currentPath, 'Operation $op does not affect $currentPath');
          assert(isSource && !isDest || !isSource && isDest, 'Operation $op affects both source and destination');
        }
        switch (op) {
          case CopyOperation():
            if (isDest) {
              // This file is being written or overwritten
              lifecycle = lifecycle == FileLifecycle.exists || lifecycle == FileLifecycle.pendingRemove
                  ? FileLifecycle.pendingUpdate
                  : FileLifecycle.pendingAdd;
              file = op.source.withPath(op.destination);
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Copy destination $operations");
              }
            } else {
              // isSource
              // Copying this file somewhere else, nothing to do.
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Copy source $operations");
              }
            }

          case MoveOperation():
            if (isSource) {
              // Moving this file somewhere else.
              // If it did not previously exist, it's state is now as though it never did.
              lifecycle = lifecycle == FileLifecycle.exists || lifecycle == FileLifecycle.pendingUpdate ? FileLifecycle.pendingRemove : null;
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Move source $operations");
              }
            } else {
              // isDest
              // This file is being written or overwritten
              lifecycle = lifecycle == FileLifecycle.exists || lifecycle == FileLifecycle.pendingRemove
                  ? FileLifecycle.pendingUpdate
                  : FileLifecycle.pendingAdd;
              file = op.source.withPath(op.destination);
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Move destination $operations");
              }
            }

          case RenameOperation():
            if (isSource) {
              // Renaming this file, will be removed from source.
              // If it did not previously exist, it's state is now as though it never did.
              lifecycle = lifecycle == FileLifecycle.exists || lifecycle == FileLifecycle.pendingUpdate ? FileLifecycle.pendingRemove : null;
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Rename source $operations");
              }
            } else {
              // isDest
              // This is the new name for a renamed file
              file = op.source.withPath(op.source.path.sibling(fileName));
              lifecycle = FileLifecycle.pendingAdd;
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Rename destination $operations");
              }
            }

          case DeleteOperation():
            if (isSource) {
              // Deleting this file.
              // If it did not previously exist, it's state is now as though it never did.
              lifecycle = lifecycle == FileLifecycle.exists || lifecycle == FileLifecycle.pendingUpdate ? FileLifecycle.pendingRemove : null;
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Delete $operations");
              }
            } else {
              assert(false, 'Delete operation with wrong path: $op');
            }

          case CreateFileOperation():
            if (isSource) {
              // Creating this file
              file = op.source;
              lifecycle = FileLifecycle.pendingAdd;
              if (kDebugMode) {
                logger.finest("$_dir _calcFile($fileName, $realPathExists) Create $operations");
              }
            } else {
              assert(false, 'Create operation with wrong path: $op');
            }

          case CalcDirectorySizeOperation():
            // Size calculation doesn't affect file lifecycle
            if (kDebugMode) {
              logger.finest("$_dir _calcFile($fileName, $realPathExists) Size calculation $operations");
            }
            break;
        }
      }
    }

    // Create the final file with the calculated lifecycle and operations
    if (lifecycle != null) {
      RawPath path;
      PathStats stats;
      if (file != null) {
        path = file.path;
        stats = file.stats;
      } else if (realPath != null) {
        path = realPath.path;
        stats = realPath.stats;
      } else {
        assert(false, 'File $fileName does not exist and is not being created');
        return;
      }
      file = File(path, stats, lifecycle, operations?.toList());
    } else {
      file = null;
    }

    if (kDebugMode) {
      logger.finest("$_dir _calcFile($fileName, $realPathExists) Result: ${lifecycle ?? "removed"}");
    }
    if (file != null) {
      _filesByName[fileName] = file;
    } else {
      _filesByName.remove(fileName);
    }
  }

  @computed
  File get dir => _dir.file;

  File? get(String name) => _filesByName[name];

  Future<void> reload() => _paths.reload();

  List<File> get files => _filesByName.values.toList();

  @override
  List<File> get value => files;

  @override
  bool get isLoading => _paths.isLoading;

  @override
  Exception? get error => _paths.error;

  // FIXME: This needs a custom future.
  @override
  Future<List<File>> get future => Future.value(_filesByName.values.toList());

  /// Allows external components to observe changes to files in this directory
  /// Returns a disposer function that should be called when the observer is no longer needed
  Dispose observeFiles(void Function(MapChange<String, File>) listener, {bool fireImmediately = true}) {
    return _filesByName.observe(listener, fireImmediately: fireImmediately);
  }

  /// Refreshes a file in the file list
  // @action
  // void refreshFile(File file) {
  //   if (kDebugMode) {
  //     logger.fine('${_dir.file} refreshFile: $file');
  //   }

  //   // Update the file in the file list
  //   // This is a temporary solution until we implement proper file watching
  //   // In the future, this should trigger a refresh from the file system
  //   final currentFiles = _files.value.toList();
  //   final index = currentFiles.indexWhere((f) => f.name == file.name);

  //   if (index >= 0) {
  //     // Replace the existing file
  //     currentFiles[index] = file;
  //   } else {
  //     // Add the new file
  //     currentFiles.add(file);
  //   }

  //   // Force the paths to update with the new file
  //   _paths.forceSet(file.path.withStats(file.stats));
  // }

  // /// Removes a file from the file list
  // @action
  // void removeFile(File file) {
  //   if (kDebugMode) {
  //     logger.fine('removeFile: $file');
  //   }

  //   // Remove the file from the file list
  //   final currentFiles = _files.value.toList();
  //   currentFiles.removeWhere((f) => f.name == file.name);

  //   // Force the paths to update by removing the file
  //   _paths.forceRemove(file.path.withStats(file.stats));
  // }

  void _addWatcher(String clientId) => _watchers.add(clientId);

  void _removeWatcher(String clientId) {
    final removed = _watchers.remove(clientId);
    assert(removed, "_removeWatcher(${dir.path}): '$clientId' wasn't watching!");
  }

  @action
  void _setWatchedParent(FilesBase? parent) {
    if (parent != null) {
      assert(_watchedParent == null || _watchedParent == parent, "_setWatchedParent($dir): Already watching parent: ${_watchedParent!.dir}");
      if (_watchedParent != parent) {
        _watchedParent = parent;
      }
    } else {
      assert(_watchedParent != null, "_setWatchedParent(null): Not watching any parent!");
      _watchedParent = null;
    }
  }

  @action
  void _addWatchedChild(FilesBase child) => _watchedChildren.add(child);

  @action
  void _removeWatchedChild(FilesBase child) {
    final removed = _watchedChildren.remove(child);
    assert(removed, "$dir _removeWatchedChild($child): Not a watched child!");
  }

  // @computed
  // PathWithStats? get _dirPath => _parent?._paths.get(dir.path.name);

  // @computed
  // PendingFileDisplay? get _dirPendingFile => _parent?._pendingFiles.get(dir.path.name);

  void _dispose() {
    for (final disposer in _disposers) {
      disposer();
    }

    _paths.dispose();

    _watchedParent?._removeWatchedChild(this);
    _watchedParent = null;

    for (final child in _watchedChildren) {
      child._setWatchedParent(null);
    }
    _watchedChildren.clear();

    _watchers.clear();
  }

  static final logger = loggerFor(Files, Level.INFO);
}

class ParentDir extends ParentDirBase with _$ParentDir {
  ParentDir(super.fileRepository, super.path, {required super.watch});

  @override
  String toString() => file.toString();
}

// Monitors the "parent dir" file for changes by watching its parent.
abstract class ParentDirBase with Store {
  final FileRepositoryBase _fileRepository;
  final bool _watch;
  final File _bestEffortFile;

  ParentDirBase(this._fileRepository, RawPath path, {required bool watch})
      : _watch = watch,
        // We initially set the file to contains fake stats. This is ok because:
        //   - If we're watching the file, its stats will be replaced with real stats as soon as they are available.
        //   - All the use cases that don't watch the file also don't need the stats.
        _bestEffortFile = File(path, PathStats.fakeDir, FileLifecycle.exists);

  @computed
  File get file {
    final bestEffortFile = _bestEffortFile;
    if (!_watch) {
      return bestEffortFile;
    }

    final path = bestEffortFile.path;
    final parentPath = path.parent;
    if (parentPath == null) {
      return bestEffortFile;
    }

    final parentFiles = _fileRepository._get(parentPath);
    if (parentFiles == null || parentFiles.isLoading) {
      return bestEffortFile;
    }
    final error = parentFiles.error;
    if (error != null) {
      return bestEffortFile.withStats(bestEffortFile.stats.withError(error));
    }

    var fileInParent = parentFiles.get(path.name);
    if (fileInParent == null) {
      if (kDebugMode) {
        logger.warning("ParentDir($path): File not found in parent!");
      }
      fileInParent = bestEffortFile.withStats(bestEffortFile.stats.withError(Exception("Not found: '$path'")));
    }
    return fileInParent;
  }

  static final logger = loggerFor(ParentDir);
}

typedef _DirInParent = AsyncValueSnapshot<File?>;
final nullDirInParent = _DirInParent(value: null);
