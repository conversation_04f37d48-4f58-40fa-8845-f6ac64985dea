import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:qfiler/app/domain/file.dart';
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/app/domain/pausable.dart';
import 'package:qfiler/app/domain/user_choice.dart';
import 'package:qfiler/app/task/file_op_executor.dart';
import 'package:qfiler/app/task/pending_file_store.dart';
import 'package:qfiler/app/task/progress.dart';
import 'package:qfiler/app/task/task.dart';
import 'package:qfiler/rust/.gen/api/domain.dart' as rs;

import '../../mocks/mock_file_system.dart';

void main() {
  late InMemoryFileSystem fakeFs;
  late MockPendingOperationRepository mockPendingOpRepo;
  late MockShowConfirmOverwriteDialog mockOverwriteDialog;
  late MockShowFileOpErrorDialog mockErrorDialog;
  late FileOpExecutor executor;

  setUp(() {
    fakeFs = InMemoryFileSystem();
    mockPendingOpRepo = MockPendingOperationRepository();
    mockOverwriteDialog = MockShowConfirmOverwriteDialog();
    mockErrorDialog = MockShowFileOpErrorDialog();

    // Set default behaviors to throw errors for unexpected calls
    when(
      mockOverwriteDialog.call(
        op: anyNamed('op'),
        selectOverwriteAll: anyNamed('selectOverwriteAll'),
      ),
    ).thenAnswer((_) async => Future.error('Unexpected call to overwrite dialog'));

    when(
      mockErrorDialog.call(
        op: anyNamed('op'),
        error: anyNamed('error'),
      ),
    ).thenAnswer((_) async => Future.error('Unexpected call to error dialog'));

    executor = FileOpExecutor(
      fakeFs,
      mockPendingOpRepo,
      mockOverwriteDialog,
      mockErrorDialog,
    );
  });

  group('FileOpExecutor', () {
    group('File Operations', () {
      late TestTask mockTask;
      late File sourceFile;
      late RawPath destPath;
      late CopyOperation copyOp;

      setUp(() {
        mockTask = TestTask();
        sourceFile = File(
          RawPath('source/file.txt'),
          PathStats.fakeFile.copyWith(size: 1000, createTime: DateTime(2023, 1, 1), updateTime: DateTime(2023, 1, 2)),
        );
        destPath = RawPath('dest/file.txt');

        copyOp = CopyOperation(task: mockTask, source: sourceFile, destination: destPath);
      });

      test('should execute copy operation successfully', () async {
        // Set up the fake file system with a source file
        fakeFs.addEntry(sourceFile.path, content: 'Test content');

        // Execute the operation
        await executor.execute(copyOp);

        // Verify the operation was processed correctly
        final destStats = await fakeFs.stat(destPath);
        expect(destStats.stats.type, equals(rs.PathStatsType.file));

        final destContent = await fakeFs.readFile(destPath);
        expect(destContent, equals('Test content'));

        expect(mockPendingOpRepo.removedOps, contains(copyOp));
      });

      test('should track progress during copy operation', () async {
        // Set up the fake file system with a source file
        final fileSize = 1000;
        fakeFs.addEntry(sourceFile.path, content: 'X' * fileSize);

        // Create a copy operation with the real progress object
        final copyOpWithProgress = CopyOperation(task: mockTask, source: sourceFile, destination: destPath);

        // Set the progress object manually since the constructor doesn't accept it
        copyOpWithProgress.progress.setBytesTotal(fileSize);
        copyOpWithProgress.progress.setFilesTotal(1);

        // Execute the operation
        await executor.execute(copyOpWithProgress);

        // Verify progress was tracked correctly
        expect(copyOpWithProgress.progress.bytesProcessed, equals(fileSize));
        expect(copyOpWithProgress.progress.filesProcessed, equals(1));
        expect(copyOpWithProgress.progress.isFinished, isTrue);
      });

      test('should handle generic errors gracefully', () async {
        // Create a large file to force using _copyFileWithProgress instead of _copyFileDirectly
        final largeFileSize = 1000000; // 1MB
        final largeFile = File(
          RawPath('source/large_file.txt'),
          PathStats.fakeFile.copyWith(size: largeFileSize, createTime: DateTime(2023, 1, 1), updateTime: DateTime(2023, 1, 2)),
        );
        final largeFileDest = RawPath('dest/large_file.txt');

        // Set up the fake file system with a source file that will cause an error
        fakeFs.addEntry(largeFile.path, content: 'X' * 1000, errorOnCopy: true); // Using smaller content for test speed

        // Create a new task for this test to ensure it doesn't have errorModeForAll set
        final testTask = TestTask();
        final testCopyOp = CopyOperation(task: testTask, source: largeFile, destination: largeFileDest);

        // Make sure the progress is set up correctly to force using _copyFileWithProgress
        testCopyOp.progress.setBytesTotal(largeFileSize);
        testCopyOp.progress.setFilesTotal(1);

        // Set up the error dialog to return 'skip' for this specific operation
        when(mockErrorDialog.call(op: testCopyOp, error: any))
            .thenAnswer((_) async => Future.value(OperationErrorChoice(mode: OperationErrorMode.skip, all: false)));

        // Execute the operation - should not throw
        await executor.execute(testCopyOp);

        // Verify error dialog was called at least once
        verify(mockErrorDialog.call(op: testCopyOp, error: captureAny)).called(greaterThan(0));

        // Verify the operation was removed from pending operations
        expect(mockPendingOpRepo.removedOps, contains(testCopyOp), reason: 'Operation should have been removed from pending operations');
      });

      test('should handle file already exists with overwrite confirmation', () async {
        // Set up the fake file system with source and destination files
        fakeFs.addEntry(sourceFile.path, content: 'Source content');
        fakeFs.addEntry(destPath, content: 'Destination content'); // This will cause FileAlreadyExistsError

        // Create a new task for this test to ensure it doesn't have overwriteModeForAll set
        final testTask = TestTask();
        final testCopyOp = CopyOperation(task: testTask, source: sourceFile, destination: destPath);

        // Set up the overwrite dialog to return 'overwrite' for this specific operation
        when(mockOverwriteDialog.call(op: testCopyOp, selectOverwriteAll: false))
            .thenAnswer((_) async => Future.value(OverwriteChoice(overwriteMode: OverwriteMode.overwrite, all: false)));

        // Execute the operation
        await executor.execute(testCopyOp);

        // Verify the operation was processed correctly
        final destStats = await fakeFs.stat(destPath);
        expect(destStats.stats.type, equals(rs.PathStatsType.file));

        // Now that we've fixed the MockFileSystem, the content should be from the source file
        expect(await fakeFs.readFile(destPath), equals('Source content'), reason: 'Destination file should have source content');

        // Verify that the overwrite dialog was shown
        verify(mockOverwriteDialog.call(op: testCopyOp, selectOverwriteAll: false)).called(1);

        // Verify the operation was removed from pending operations
        expect(mockPendingOpRepo.removedOps, contains(testCopyOp), reason: 'Operation should have been removed from pending operations');
      });

      test('should skip file when overwrite confirmation returns skip', () async {
        // Set up the fake file system with source and destination files
        fakeFs.addEntry(sourceFile.path, content: 'Source content');
        fakeFs.addEntry(destPath, content: 'Destination content'); // This will cause FileAlreadyExistsError

        // Create a new task for this test to ensure it doesn't have overwriteModeForAll set
        final testTask = TestTask();
        final testCopyOp = CopyOperation(task: testTask, source: sourceFile, destination: destPath);

        // Set up the overwrite dialog to return 'skip' for this specific operation
        when(mockOverwriteDialog.call(op: testCopyOp, selectOverwriteAll: false))
            .thenAnswer((_) async => Future.value(OverwriteChoice(overwriteMode: OverwriteMode.skip, all: false)));

        // Execute the operation
        await executor.execute(testCopyOp);

        // Verify the operation was processed correctly
        final destStats = await fakeFs.stat(destPath);
        expect(destStats.stats.type, equals(rs.PathStatsType.file));
        expect(await fakeFs.readFile(destPath), equals('Destination content'), reason: 'Content should remain unchanged');

        // Verify that the overwrite dialog was shown
        verify(mockOverwriteDialog.call(op: testCopyOp, selectOverwriteAll: false)).called(1);

        // Verify the operation was removed from pending operations
        expect(mockPendingOpRepo.removedOps, contains(testCopyOp), reason: 'Operation should have been removed from pending operations');
      });

      test('should use overwriteModeForAll when set', () async {
        // Set up the fake file system with source and destination files
        fakeFs.addEntry(sourceFile.path, content: 'Source content');
        fakeFs.addEntry(destPath, content: 'Destination content'); // This will cause FileAlreadyExistsError

        // Set up the task to return overwriteModeForAll
        mockTask.setOverwriteModeForAll(OverwriteMode.overwrite);

        // Execute the operation
        await executor.execute(copyOp);

        // Verify the operation was processed correctly
        final destStats = await fakeFs.stat(destPath);
        expect(destStats.stats.type, equals(rs.PathStatsType.file));
        expect(await fakeFs.readFile(destPath), equals('Source content')); // Content should be from source

        // Verify that the overwrite dialog was NOT shown
        verifyNever(mockOverwriteDialog.call(op: captureAny, selectOverwriteAll: captureAny));

        // Verify the operation was removed from pending operations
        expect(mockPendingOpRepo.removedOps, contains(copyOp));
      });

      test('should skip file when overwrite confirmation returns skip with shared task', () async {
        // Set up the fake file system with source and destination files
        fakeFs.addEntry(sourceFile.path, content: 'Source content');
        fakeFs.addEntry(destPath, content: 'Destination content'); // This will cause FileAlreadyExistsError

        // Make sure the task doesn't have an overwriteModeForAll set
        mockTask.setOverwriteModeForAll(null);

        // Set up the overwrite dialog to return 'skip' for this specific operation
        when(mockOverwriteDialog.call(op: copyOp, selectOverwriteAll: false))
            .thenAnswer((_) async => Future.value(OverwriteChoice(overwriteMode: OverwriteMode.skip, all: false)));

        // Execute the operation
        await executor.execute(copyOp);

        // Verify the operation was processed correctly
        final destStats = await fakeFs.stat(destPath);
        expect(destStats.stats.type, equals(rs.PathStatsType.file));
        expect(await fakeFs.readFile(destPath), equals('Destination content'), reason: 'Content should remain unchanged');

        // Verify that the overwrite dialog was shown
        verify(mockOverwriteDialog.call(op: copyOp, selectOverwriteAll: false)).called(1);

        // Verify the operation was removed from pending operations
        expect(mockPendingOpRepo.removedOps, contains(copyOp), reason: 'Operation should have been removed from pending operations');
      });
    });

    // Test directory operations
    group('Directory Operations', () {
      late TestTask mockTask;
      late File sourceDir;
      late RawPath destDir;
      late CopyOperation copyDirOp;
      late DeleteOperation deleteDirOp;

      setUp(() {
        mockTask = TestTask();
        sourceDir = File(RawPath('source/dir'), PathStats.fakeDir.copyWith(createTime: DateTime(2023, 1, 1), updateTime: DateTime(2023, 1, 2)));
        destDir = RawPath('dest/dir');

        // Create operations
        copyDirOp = CopyOperation(task: mockTask, source: sourceDir, destination: destDir);

        deleteDirOp = DeleteOperation(task: mockTask, source: sourceDir);
      });

      test('should copy directory successfully', () async {
        // Set up the fake file system with a source directory
        fakeFs.addEntry(sourceDir.path, isDirectory: true);

        // Execute the operation
        await executor.execute(copyDirOp);

        // Verify the operation was processed correctly
        final destDirStats = await fakeFs.stat(destDir);
        expect(destDirStats.stats.type, equals(rs.PathStatsType.directory));
        expect(mockPendingOpRepo.removedOps, contains(copyDirOp));
      });

      test('should delete directory successfully', () async {
        // Set up the fake file system with a source directory
        fakeFs.addEntry(sourceDir.path, isDirectory: true);

        // Execute the operation
        await executor.execute(deleteDirOp);

        // Verify the operation was processed correctly
        final sourceDirStats = await fakeFs.stat(sourceDir.path);
        expect(sourceDirStats.stats.type, equals(rs.PathStatsType.unknown)); // Directory should be gone
        expect(mockPendingOpRepo.removedOps, contains(deleteDirOp));
      });

      test('should copy nested directory structure successfully', () async {
        // Create a nested directory structure
        final nestedSourceDir = RawPath('source/dir');
        final nestedFile1 = RawPath('source/dir/file1.txt');
        final nestedSubDir = RawPath('source/dir/subdir');
        final nestedFile2 = RawPath('source/dir/subdir/file2.txt');

        // Set up the fake file system with the nested structure
        fakeFs.addEntry(nestedSourceDir, isDirectory: true);
        fakeFs.addEntry(nestedFile1, content: 'File 1 content');
        fakeFs.addEntry(nestedSubDir, isDirectory: true);
        fakeFs.addEntry(nestedFile2, content: 'File 2 content');

        // Create child operations
        final file1 = File(nestedFile1, PathStats.fakeFile);
        final subDir = File(nestedSubDir, PathStats.fakeDir);
        final file2 = File(nestedFile2, PathStats.fakeFile);

        final destFile1 = RawPath('dest/dir/file1.txt');
        final destSubDir = RawPath('dest/dir/subdir');
        final destFile2 = RawPath('dest/dir/subdir/file2.txt');

        final copyFile1Op = CopyOperation(task: mockTask, source: file1, destination: destFile1);

        final copySubDirOp = CopyOperation(task: mockTask, source: subDir, destination: destSubDir);

        final copyFile2Op = CopyOperation(task: mockTask, source: file2, destination: destFile2);

        // Set up the nested structure in the copy operation
        final nestedCopyDirOp = CopyOperation(task: mockTask, source: sourceDir, destination: destDir, children: [copyFile1Op, copySubDirOp]);

        // Set up the nested structure in the subdirectory operation
        copySubDirOp.setChildren([copyFile2Op]);

        // Execute the operation
        await executor.execute(nestedCopyDirOp);

        // Verify the operation was processed correctly
        final destDirStats = await fakeFs.stat(destDir);
        final destFile1Stats = await fakeFs.stat(destFile1);
        final destSubDirStats = await fakeFs.stat(destSubDir);
        final destFile2Stats = await fakeFs.stat(destFile2);

        expect(destDirStats.stats.type, equals(rs.PathStatsType.directory));
        expect(destFile1Stats.stats.type, equals(rs.PathStatsType.file));
        expect(destSubDirStats.stats.type, equals(rs.PathStatsType.directory));
        expect(destFile2Stats.stats.type, equals(rs.PathStatsType.file));

        expect(await fakeFs.readFile(destFile1), equals('File 1 content'));
        expect(await fakeFs.readFile(destFile2), equals('File 2 content'));
        expect(mockPendingOpRepo.removedOps, contains(nestedCopyDirOp));
      });
    });

    group('Move Operations', () {
      late TestTask mockTask;
      late File sourceFile;
      late RawPath destPath;
      late MoveOperation moveOp;

      setUp(() {
        mockTask = TestTask();
        sourceFile = File(
          RawPath('source/file.txt'),
          PathStats.fakeFile.copyWith(size: 1000, createTime: DateTime(2023, 1, 1), updateTime: DateTime(2023, 1, 2)),
        );
        destPath = RawPath('dest/file.txt');

        moveOp = MoveOperation(task: mockTask, source: sourceFile, destination: destPath);
      });

      test('should execute move operation using rename', () async {
        // Set up the fake file system with a source file
        fakeFs.addEntry(sourceFile.path, content: 'Test content');

        // Execute the operation
        await executor.execute(moveOp);

        // Verify the operation was processed correctly
        final sourceStats = await fakeFs.stat(sourceFile.path);
        final destStats = await fakeFs.stat(destPath);
        expect(sourceStats.stats.type, equals(rs.PathStatsType.unknown)); // Source should be gone
        expect(destStats.stats.type, equals(rs.PathStatsType.file)); // Destination should exist
        expect(await fakeFs.readFile(destPath), equals('Test content')); // Content should be preserved
        expect(mockPendingOpRepo.removedOps, contains(moveOp));
      });

      test('should fall back to copy-and-delete if rename fails with file exists error', () async {
        // Set up the fake file system with a source file and a destination file (to cause rename to fail)
        fakeFs.addEntry(sourceFile.path, content: 'Test content');
        fakeFs.addEntry(destPath, content: 'Existing content'); // This will cause rename to fail

        // Execute the operation
        await executor.execute(moveOp);

        // Verify the operation was processed correctly
        final sourceStats = await fakeFs.stat(sourceFile.path);
        final destStats = await fakeFs.stat(destPath);
        expect(sourceStats.stats.type, equals(rs.PathStatsType.unknown)); // Source should be gone
        expect(destStats.stats.type, equals(rs.PathStatsType.file)); // Destination should exist
        expect(await fakeFs.readFile(destPath), equals('Test content')); // Content should be from source
        expect(mockPendingOpRepo.removedOps, contains(moveOp));
      });
    });

    group('Rename Operations', () {
      late TestTask mockTask;
      late File sourceFile;
      late String newName;
      late RenameOperation renameOp;

      setUp(() {
        mockTask = TestTask();
        sourceFile = File(RawPath('source/oldname.txt'), PathStats.fakeFile);
        newName = 'newname.txt';

        renameOp = RenameOperation(task: mockTask, source: sourceFile, newName: newName);
      });

      test('should execute rename operation successfully', () async {
        // Set up the fake file system with a source file
        fakeFs.addEntry(sourceFile.path, content: 'Test content');

        // Execute the operation
        await executor.execute(renameOp);

        // Verify the operation was processed correctly
        final sourceStats = await fakeFs.stat(sourceFile.path);
        final destStats = await fakeFs.stat(renameOp.destination);
        expect(sourceStats.stats.type, equals(rs.PathStatsType.unknown)); // Source should be gone
        expect(destStats.stats.type, equals(rs.PathStatsType.file)); // Destination should exist
        expect(await fakeFs.readFile(renameOp.destination), equals('Test content')); // Content should be preserved
        expect(mockPendingOpRepo.removedOps, contains(renameOp));
      });
    });

    group('Delete Operations', () {
      late TestTask mockTask;
      late File sourceFile;
      late DeleteOperation deleteOp;

      setUp(() {
        mockTask = TestTask();
        sourceFile = File(RawPath('source/file_to_delete.txt'), PathStats.fakeFile.copyWith(size: 500));

        deleteOp = DeleteOperation(task: mockTask, source: sourceFile);
      });

      test('should execute delete operation successfully', () async {
        // Set up the fake file system with a source file
        fakeFs.addEntry(sourceFile.path, content: 'Test content');

        // Execute the operation
        await executor.execute(deleteOp);

        // Verify the operation was processed correctly
        final sourceStats = await fakeFs.stat(sourceFile.path);
        expect(sourceStats.stats.type, equals(rs.PathStatsType.unknown)); // File should be gone
        expect(mockPendingOpRepo.removedOps, contains(deleteOp));
      });
    });
  });
}

class TestTask implements Task {
  OverwriteMode? _overwriteModeForAll;
  OperationErrorMode? _errorModeForAll;
  bool _cancelled = false;
  final bool _previousOverwriteAllSelected = false;
  @override
  final Progress progress = Progress();

  @override
  OverwriteMode? get overwriteModeForAll => _overwriteModeForAll;

  @override
  OperationErrorMode? get errorModeForAll => _errorModeForAll;

  @override
  bool get previousOverwriteAllSelected => _previousOverwriteAllSelected;

  @override
  void setOverwriteModeForAll(OverwriteMode? mode) {
    _overwriteModeForAll = mode;
  }

  @override
  void setErrorModeForAll(OperationErrorMode? mode) {
    _errorModeForAll = mode;
  }

  @override
  void setCurrentPausableOperation(Pausable? operation) {
    // Implementation not needed for tests
  }

  @override
  void cancel() {
    _cancelled = true;
  }

  @override
  void pause() {
    // Implementation not needed for tests
  }

  @override
  void resume() {
    // Implementation not needed for test
  }

  // This is used in our tests but not part of the Task interface
  bool get isCancelled => _cancelled;

  // Implement other required methods with minimal functionality
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

class MockPendingOperationRepository implements PendingOperationRepository {
  final List<Operation> removedOps = [];

  @override
  void removeOp(Operation op) {
    // Make this synchronous to avoid potential deadlocks
    removedOps.add(op);
  }

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

// Use the generated mocks instead of these manual mocks
class MockShowConfirmOverwriteDialog extends Mock implements ShowConfirmOverwriteDialog {}

class MockShowFileOpErrorDialog extends Mock implements ShowFileOpErrorDialog {}

class MockPausableStreamCopy implements PausableStreamCopy {
  bool isPaused = false;
  bool isCancelled = false;
  final bool shouldFail;
  @override
  final void Function(int bytesInChunk)? onChunkProcessed;

  MockPausableStreamCopy({this.shouldFail = false, this.onChunkProcessed});

  // onChunkProcessed is a final field, not an overridable getter/setter.
  // No @override needed here if it's correctly defined as a field in PausableStreamCopy.

  @override
  Future<void> pause() async {
    isPaused = true;
  }

  @override
  Future<void> resume() async {
    isPaused = false;
  }

  @override
  Future<void> cancel() async {
    isCancelled = true;
  }

  @override
  Future<void> execute() async {
    if (isCancelled) {
      throw CancellationError();
    }
    if (shouldFail) {
      throw Exception('Simulated error in stream copy');
    }
    // Call onChunkProcessed for simple simulation
    if (onChunkProcessed != null) {
      for (int i = 0; i < 5 && !isCancelled; i++) {
        onChunkProcessed!(1024); // Simulate 1KB chunk
        await Future.delayed(Duration(milliseconds: 1));
        if (isPaused) {
          // Wait until resumed or cancelled
          while (isPaused && !isCancelled) {
            await Future.delayed(Duration(milliseconds: 1));
          }
          if (isCancelled) break;
        }
      }
    }
    return;
  }
}
