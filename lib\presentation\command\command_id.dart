/// Enum representing all available command IDs
enum CommandId {
  // Command palette
  showCommandPalette('command.showPalette'),

  // File operations
  renameFile('file.rename'),
  copyFiles('file.copy'),
  moveFiles('file.move'),
  deleteFiles('file.delete'),
  executeFile('file.execute'),
  toggleSelection('file.toggleSelection'),

  // Navigation
  navigateToParent('navigation.parent'),
  navigateUp('navigation.up'),
  navigateDown('navigation.down'),
  navigateBack('navigation.back'),
  navigateForward('navigation.forward'),

  // Pane operations
  switchPane('pane.switch'),

  // Directory operations
  refreshDirectory('directory.refresh');

  final String value;

  const CommandId(this.value);

  /// Get the scope part of the command ID (before the dot)
  String get scope => value.split('.').first;

  /// Get the command part of the command ID (after the dot)
  String get command => value.split('.').last;

  /// Parse a string into a CommandId
  static CommandId fromString(String value) {
    return CommandId.values.firstWhere(
      (cmd) => cmd.value == value,
      orElse: () => throw ArgumentError('Unknown command ID: $value'),
    );
  }

  @override
  String toString() => value;
}
