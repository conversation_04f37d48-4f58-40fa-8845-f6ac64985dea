import 'dart:io' show FileSystemException;

import 'package:dartx/dartx.dart';

class AppException implements Exception {
  final String message;
  final int errorCode;
  final Exception? cause;

  AppException(this.message, {this.errorCode = ErrorCodes.unknown, this.cause});

  @override
  String toString() => message;

  factory AppException.from(Exception e) {
    if (e is AppException) {
      return e;
    }

    if (e is FileSystemException) {
      final osError = e.osError;
      if (osError != null) {
        switch (osError.errorCode) {
          case 3:
            return AppException("Not found: '${e.path!.removeSuffix("*")}'", errorCode: osError.errorCode, cause: e);
        }
      }
      return AppException(osError?.message ?? e.message, errorCode: osError?.errorCode ?? -1, cause: e);
    }

    return AppException(e.toString(), errorCode: ErrorCodes.unknown, cause: e);
  }

  factory AppException.internalError(String message, [Exception? cause]) =>
      AppException(message, errorCode: ErrorCodes.internalError, cause: cause);

  factory AppException.invalidArgument(String message, [Exception? cause]) =>
      AppException(message, errorCode: ErrorCodes.invalidArgument, cause: cause);

  factory AppException.notFound(String message, [Exception? cause]) => AppException(message, errorCode: ErrorCodes.notFound, cause: cause);

  factory AppException.invalidPersistedState(String message, [Exception? cause]) =>
      AppException(message, errorCode: ErrorCodes.invalidPersistedState, cause: cause);
}

class ErrorCodes {
  static const unknown = 1;
  static const internalError = 2;
  static const invalidArgument = 3;
  static const notFound = 4;
  static const invalidPersistedState = 5;
}
