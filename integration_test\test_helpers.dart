import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/app/domain/user_choice.dart';
import 'package:qfiler/app/file_system/file_repository.dart';
import 'package:qfiler/app/file_system/real/path_repository.dart';
import 'package:qfiler/app/file_system/rust/rust_file_system_adapter.dart';
import 'package:qfiler/app/notification/notification_repository.dart';
import 'package:qfiler/app/notification/notification_store.dart';
import 'package:qfiler/app/persistence/data_repository.dart';
import 'package:qfiler/app/settings/settings_repository.dart';
import 'package:qfiler/app/task/file_op_executor.dart';
import 'package:qfiler/app/task/pending_file_store.dart';
import 'package:qfiler/app/task/task.dart';
import 'package:qfiler/app/task/task_planner.dart';
import 'package:qfiler/app/task/task_store.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/command/command_context_repository.dart';
import 'package:qfiler/presentation/command/command_dispatcher.dart';
import 'package:qfiler/presentation/command/command_repository.dart';
import 'package:qfiler/presentation/command/keybind_manager.dart';
import 'package:qfiler/presentation/command/keybind_repository.dart';
import 'package:qfiler/presentation/directory_view/directory_view_store.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/history/directory_state.dart';
import 'package:qfiler/presentation/history/history.dart';
import 'package:qfiler/presentation/history/history_store.dart';
import 'package:qfiler/presentation/pane/all_panes_store.dart';
import 'package:qfiler/presentation/pane/pane_store.dart';
import 'package:qfiler/presentation/rename/rename_store.dart';
import 'package:qfiler/presentation/root_store.dart';

/// Test setup data
class TestSetup {
  final io.Directory tempBaseDir;
  final io.Directory leftTempDir;
  final io.Directory rightTempDir;
  final io.Directory dataTempDir;
  final RootStore rootStore;

  TestSetup({
    required this.tempBaseDir,
    required this.leftTempDir,
    required this.rightTempDir,
    required this.dataTempDir,
    required this.rootStore,
  });
}

/// Create test setup with temporary directories and RootStore
Future<TestSetup> createTestSetup({
  String testName = 'qfiler_test',
  List<String>? leftFiles,
  List<String>? rightFiles,
}) async {
  // Create temporary directories
  final tempBaseDir = await io.Directory.systemTemp.createTemp('${testName}_');
  final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
  final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();
  final dataTempDir = await io.Directory(p.join(tempBaseDir.path, 'data')).create();

  // Create test files
  await _createTestFiles(leftTempDir, leftFiles ?? ['file1.txt', 'file2.txt']);
  await _createTestFiles(rightTempDir, rightFiles ?? ['file3.txt']);

  // Create RootStore
  final rootStore = await createTestRootStore(
    leftInitialDir: RawPath(leftTempDir.path),
    rightInitialDir: RawPath(rightTempDir.path),
    dataDir: RawPath(dataTempDir.path),
  );

  return TestSetup(
    tempBaseDir: tempBaseDir,
    leftTempDir: leftTempDir,
    rightTempDir: rightTempDir,
    dataTempDir: dataTempDir,
    rootStore: rootStore,
  );
}

/// Clean up test setup
Future<void> cleanupTestSetup(TestSetup setup) async {
  if (await setup.tempBaseDir.exists()) {
    await setup.tempBaseDir.delete(recursive: true);
  }
}

/// Pump app and wait for it to settle
Future<void> pumpApp(WidgetTester tester, RootStore rootStore, {Duration? settleTimeout}) async {
  await tester.pumpWidget(App(rootStore));
  await tester.pumpAndSettle(settleTimeout ?? const Duration(seconds: 3));
}

/// Send key and wait for processing
Future<void> sendKeyAndWait(WidgetTester tester, LogicalKeyboardKey key, {Duration? waitTime}) async {
  await tester.sendKeyEvent(key);
  await tester.pumpAndSettle(waitTime ?? const Duration(milliseconds: 300));
}

/// Debug current state
void debugCurrentState(RootStore rootStore, {String? label}) {
  if (kDebugMode) {
    final prefix = label != null ? '[$label] ' : '';
    print('${prefix}=== DEBUG STATE ===');
    print('${prefix}Left pane is source: ${rootStore.allPanesStore.left.isSource}');
    print('${prefix}Right pane is source: ${rootStore.allPanesStore.right.isSource}');
    print('${prefix}Source pane side: ${rootStore.allPanesStore.sourcePaneSide}');
    print('${prefix}Left pane files: ${rootStore.allPanesStore.left.directoryViewStore.files.length}');
    print('${prefix}Left pane focused index: ${rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex}');
    print('${prefix}Right pane files: ${rootStore.allPanesStore.right.directoryViewStore.files.length}');
    print('${prefix}Right pane focused index: ${rootStore.allPanesStore.right.directoryViewStore.focusedRowIndex}');
  }
}

/// Verify arrow keys work in the specified pane
Future<void> verifyArrowKeysWork(WidgetTester tester, RootStore rootStore, {required bool isLeftPane, String? debugLabel}) async {
  final paneStore = isLeftPane ? rootStore.allPanesStore.left : rootStore.allPanesStore.right;
  final initialIndex = paneStore.directoryViewStore.focusedRowIndex;
  final fileCount = paneStore.directoryViewStore.files.length;
  
  if (kDebugMode && debugLabel != null) {
    print('[$debugLabel] Testing arrow keys - Initial index: $initialIndex, File count: $fileCount');
  }

  // Send arrow down key
  await sendKeyAndWait(tester, LogicalKeyboardKey.arrowDown);
  
  final newIndex = paneStore.directoryViewStore.focusedRowIndex;
  
  if (kDebugMode && debugLabel != null) {
    print('[$debugLabel] After arrow down - New index: $newIndex');
  }

  // If there are multiple files, the index should change
  if (fileCount > 1) {
    expect(newIndex, isNot(equals(initialIndex)), 
           reason: 'Arrow key should change focused row when ${isLeftPane ? 'left' : 'right'} pane has focus');
  } else {
    expect(newIndex, equals(initialIndex),
           reason: 'With only one file, focused index should remain the same');
  }
}

/// Verify tab key switches between panes
Future<void> verifyTabSwitching(WidgetTester tester, RootStore rootStore, {String? debugLabel}) async {
  // Should start with left pane as source
  expect(rootStore.allPanesStore.sourcePaneSide.name, equals('left'));
  
  if (kDebugMode && debugLabel != null) {
    print('[$debugLabel] Before Tab - Source: ${rootStore.allPanesStore.sourcePaneSide}');
  }

  // Press Tab to switch to right pane
  await sendKeyAndWait(tester, LogicalKeyboardKey.tab);
  
  if (kDebugMode && debugLabel != null) {
    print('[$debugLabel] After Tab - Source: ${rootStore.allPanesStore.sourcePaneSide}');
  }
  
  expect(rootStore.allPanesStore.sourcePaneSide.name, equals('right'));

  // Press Tab again to switch back to left pane
  await sendKeyAndWait(tester, LogicalKeyboardKey.tab);
  
  if (kDebugMode && debugLabel != null) {
    print('[$debugLabel] After second Tab - Source: ${rootStore.allPanesStore.sourcePaneSide}');
  }
  
  expect(rootStore.allPanesStore.sourcePaneSide.name, equals('left'));
}

/// Create test files in directory
Future<void> _createTestFiles(io.Directory dir, List<String> fileNames) async {
  for (int i = 0; i < fileNames.length; i++) {
    final fileName = fileNames[i];
    await io.File(p.join(dir.path, fileName)).writeAsString('Test file content $i');
  }
}

/// Creates a test RootStore with custom temporary directories (extracted from simple_test.dart)
Future<RootStore> createTestRootStore({
  required RawPath leftInitialDir,
  required RawPath rightInitialDir,
  required RawPath dataDir,
}) async {
  final fileSystem = RustFileSystemAdapter();
  final pendingOperationRepository = PendingOperationRepository();
  final fileRepository = FileRepository(PathRepository(fileSystem), pendingOperationRepository);

  final notificationRepository = NotificationRepository();
  final notificationStore = NotificationStore();
  final executor = FileOpExecutor(
    fileSystem,
    pendingOperationRepository,
    _TestShowConfirmOverwriteDialog(),
    _TestShowFileOpErrorDialog(),
  );

  final jobPlanner = JobPlanner(fileRepository: fileRepository, pendingOperationRepository: pendingOperationRepository);
  final jobStore = JobStore(executor, notificationStore, jobPlanner);

  final dataRepository = DataRepository(
    DataRepositoryConfig(userDataDir: dataDir),
    fileSystem,
    notificationRepository,
  );

  final settingsRepository = await SettingsRepository.create(dataRepository);
  final commandRepository = CommandRepository.create();
  final keyBindRepository = await KeyBindRepository.create(dataRepository);
  final commandContextRepository = CommandContextRepository();

  final renameStore = RenameStore(jobStore, notificationStore, commandContextRepository);
  renameStore.setupRenameSyncListener();

  final left = await _createTestPaneStore(settingsRepository, fileRepository, dataRepository, notificationStore, renameStore, Side.left, leftInitialDir);
  final right = await _createTestPaneStore(settingsRepository, fileRepository, dataRepository, notificationStore, renameStore, Side.right, rightInitialDir);

  final allPanesStore = AllPanesStore(left: left, right: right);

  final commandDispatcher = CommandDispatcher(
    allPanesStore: allPanesStore,
    commandContextRepository: commandContextRepository,
    jobStore: jobStore,
    notificationStore: notificationStore,
  );

  final keyBindManager = KeyBindManager(
    keyBindRepository,
    commandRepository,
    commandContextRepository,
    commandDispatcher,
  );

  left.setAllPanesStore(allPanesStore);
  right.setAllPanesStore(allPanesStore);

  return RootStore(
    fileRepository: fileRepository,
    allPanesStore: allPanesStore,
    settingsRepository: settingsRepository,
    commandRepository: commandRepository,
    keyBindRepository: keyBindRepository,
    commandContextRepository: commandContextRepository,
    keyBindManager: keyBindManager,
    commandDispatcher: commandDispatcher,
    renameStore: renameStore,
  );
}

Future<PaneStore> _createTestPaneStore(
  SettingsRepository settingsRepository,
  FileRepository fileRepository,
  DataRepository dataRepository,
  NotificationStore notificationStore,
  RenameStore renameStore,
  Side side,
  RawPath initialDir,
) async {
  final historyStore = await _createTestHistoryStore(dataRepository, side, initialDir);
  final directoryViewStore = DirectoryViewStore(historyStore, fileRepository, renameStore, side.name);
  final paneStore = PaneStore(historyStore, directoryViewStore, side);
  return paneStore;
}

Future<HistoryStore> _createTestHistoryStore(DataRepository dataRepository, Side side, RawPath initialDir) async {
  final historyStore = await HistoryStore.create(dataRepository, side);
  historyStore.histories.clear();
  historyStore.histories.add(History([DirectoryState(initialDir)], 0));
  historyStore.setCurrentIndex(0);
  return historyStore;
}

class _TestShowConfirmOverwriteDialog implements ShowConfirmOverwriteDialog {
  @override
  Future<OverwriteChoice> call({required Operation op, required bool selectOverwriteAll}) async {
    return OverwriteChoice(overwriteMode: OverwriteMode.overwrite, all: selectOverwriteAll);
  }
}

class _TestShowFileOpErrorDialog implements ShowFileOpErrorDialog {
  @override
  Future<OperationErrorChoice> call({required Operation op, required Object error}) async {
    return OperationErrorChoice(mode: OperationErrorMode.retry, all: false);
  }
}
