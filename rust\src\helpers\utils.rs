use chrono::{DateTime, Utc};
use std::io;
use std::time::UNIX_EPOCH;

use crate::api::domain::{PathStats, PathStatsType};

impl From<io::Result<std::fs::Metadata>> for PathStats {
    fn from(metadata_result: io::Result<std::fs::Metadata>) -> Self {
        match metadata_result {
            Ok(metadata) => {
                let file_type = metadata.file_type();
                let type_ = if file_type.is_file() {
                    PathStatsType::File
                } else if file_type.is_dir() {
                    PathStatsType::Directory
                } else if file_type.is_symlink() {
                    PathStatsType::Link
                } else {
                    PathStatsType::Unknown
                };

                let create_time = DateTime::<Utc>::from(metadata.created().unwrap_or(UNIX_EPOCH));
                let modify_time = DateTime::<Utc>::from(metadata.modified().unwrap_or(UNIX_EPOCH));
                let access_time = DateTime::<Utc>::from(metadata.accessed().unwrap_or(UNIX_EPOCH));

                // Only include size for files, not directories
                let size = if type_ == PathStatsType::Directory {
                    None
                } else {
                    Some(metadata.len())
                };

                PathStats {
                    type_,
                    create_time,
                    modify_time,
                    access_time,
                    size,
                    error: None,
                }
            }
            Err(e) => PathStats {
                type_: PathStatsType::Unknown,
                create_time: DateTime::<Utc>::from(UNIX_EPOCH),
                modify_time: DateTime::<Utc>::from(UNIX_EPOCH),
                access_time: DateTime::<Utc>::from(UNIX_EPOCH),
                size: None,
                error: Some(e.into()),
            },
        }
    }
}
