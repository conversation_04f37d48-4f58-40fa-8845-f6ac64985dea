import 'package:logging/logging.dart';

// --- DEBUGGING FLAG ---
const bool _enableDebugPrints = false;
// --------------------

void _debugPrint(String message) {
  if (_enableDebugPrints) {
    print(message);
  }
}

/// Maps log levels to ANSI color codes for colorized output.
final Map<Level, String> levelColors = {
  /* Unchanged */ Level.FINEST: '\x1B[38;5;244m',
  Level.FINER: '\x1B[38;5;244m',
  Level.FINE: '\x1B[38;5;244m',
  Level.CONFIG: '\x1B[38;5;39m',
  Level.INFO: '\x1B[38;5;34m',
  Level.WARNING: '\x1B[38;5;214m',
  Level.SEVERE: '\x1B[38;5;196m',
  Level.SHOUT: '\x1B[48;5;196m\x1B[38;5;15m',
};

//region Tokenization (Pass 1)
enum TokenType {
  /* Unchanged */ identifier,
  lParen,
  rParen,
  lBracket,
  rBracket,
  lBrace,
  rBrace,
  comma,
  colon,
  equals,
  arrow,
  string,
  whitespace,
  unknown,
  eof,
}

class TokenSegment {
  /* Unchanged */ final TokenType type;
  final String text;
  final int start;
  final int end;
  TokenSegment(this.type, this.text, this.start, this.end);
  @override
  String toString() => 'TokenSegment($type, "${text.replaceAll('\n', '\\n')}", $start-$end)';
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TokenSegment &&
          runtimeType == other.runtimeType &&
          type == other.type &&
          text == other.text &&
          start == other.start &&
          end == other.end;
  @override
  int get hashCode => type.hashCode ^ text.hashCode ^ start.hashCode ^ end.hashCode;
}

class SegmentTokenizer {
  /* Unchanged */ final String input;
  int pos = 0;
  SegmentTokenizer(this.input);
  static final _idReg = RegExp(r"[a-zA-Z0-9_.$'\-\\/]+");
  TokenSegment nextToken() {
    if (pos >= input.length) return TokenSegment(TokenType.eof, '', pos, pos);
    final start = pos;
    final c = input[pos];
    if (c == '(') {
      pos++;
      return TokenSegment(TokenType.lParen, c, start, pos);
    }
    if (c == ')') {
      pos++;
      return TokenSegment(TokenType.rParen, c, start, pos);
    }
    if (c == '[') {
      pos++;
      return TokenSegment(TokenType.lBracket, c, start, pos);
    }
    if (c == ']') {
      pos++;
      return TokenSegment(TokenType.rBracket, c, start, pos);
    }
    if (c == '{') {
      pos++;
      return TokenSegment(TokenType.lBrace, c, start, pos);
    }
    if (c == '}') {
      pos++;
      return TokenSegment(TokenType.rBrace, c, start, pos);
    }
    if (c == ',') {
      pos++;
      return TokenSegment(TokenType.comma, c, start, pos);
    }
    if (c == ':') {
      pos++;
      return TokenSegment(TokenType.colon, c, start, pos);
    }
    if (c == '=') {
      pos++;
      return TokenSegment(TokenType.equals, c, start, pos);
    }
    if (c == '-' && pos + 1 < input.length && input[pos + 1] == '>') {
      pos += 2;
      return TokenSegment(TokenType.arrow, '->', start, pos);
    }
    if (c == '"' || c == '\'') {
      final quote = c;
      pos++;
      while (pos < input.length && input[pos] != quote) {
        if (input[pos] == '\\' && pos + 1 < input.length) pos++;
        pos++;
      }
      if (pos < input.length) pos++;
      return TokenSegment(TokenType.string, input.substring(start, pos), start, pos);
    }
    if (c.trim().isEmpty) {
      final wsStart = pos;
      while (pos < input.length && input[pos].trim().isEmpty) {
        pos++;
      }
      return TokenSegment(TokenType.whitespace, input.substring(wsStart, pos), wsStart, pos);
    }
    final idMatch = _idReg.matchAsPrefix(input, pos);
    if (idMatch != null) {
      final value = idMatch.group(0)!;
      pos += value.length;
      return TokenSegment(TokenType.identifier, value, start, pos);
    }
    pos++;
    return TokenSegment(TokenType.unknown, c, start, pos);
  }

  List<TokenSegment> tokenizeAll() {
    final t = <TokenSegment>[];
    TokenSegment tk;
    do {
      tk = nextToken();
      t.add(tk);
    } while (tk.type != TokenType.eof);
    return t;
  }
}
//endregion

//region Semantic Parsing (Pass 2)

class LogColorizerConfig {
  /* Unchanged */ final bool debug;
  final int bracketLevel;
  static const _resetColor = '\x1B[0m';
  static const _functionCallColor = '\x1B[38;5;105m';
  static const _paramKeyColor = '\x1B[38;5;208m';
  static const _paramSeparatorColor = '\x1B[38;5;244m';
  static const _paramValueColor = '\x1B[38;5;45m';
  static const _functionArgColor = '\x1B[38;5;221m';
  static const _nullValueColor = '\x1B[38;5;160m';
  static const _resultValueColor = '\x1B[38;5;34m';
  static const _bracketColors = ['\x1B[38;5;141m', '\x1B[38;5;208m', '\x1B[38;5;118m', '\x1B[38;5;81m', '\x1B[38;5;203m'];
  static const _parenColor = '\x1B[38;5;117m';
  static const _braceColor = '\x1B[38;5;244m';
  static const _commaColor = '\x1B[38;5;244m';
  LogColorizerConfig({this.debug = false, this.bracketLevel = 0});
  String wrap(String text, {required String ansi, required String debugTag}) {
    if (text.isEmpty && debug && debugTag.isNotEmpty) {
      return '[c:$debugTag][/c:$debugTag]';
    }
    if (text.isEmpty) return '';
    if (debug) return '[c:$debugTag]$text[/c:$debugTag]';
    return '$ansi$text$_resetColor';
  }

  LogColorizerConfig copyWith({bool? debug, int? bracketLevel}) =>
      LogColorizerConfig(debug: debug ?? this.debug, bracketLevel: bracketLevel ?? this.bracketLevel);
}

abstract class SemanticNode {
  /* Unchanged */ List<TokenSegment> get segments;
  String toOriginalString() => segments.map((s) => s.text).join();
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false});
  String toTreeString(int indentLevel) {
    String indent = '  ' * indentLevel;
    String shortOriginal = toOriginalString().replaceAll('\n', '\\n');
    int maxLen = 80 - indent.length - runtimeType.toString().length - 10;
    if (maxLen < 10) maxLen = 10;
    if (shortOriginal.length > maxLen) {
      shortOriginal = '${shortOriginal.substring(0, maxLen - 3)}...';
    }
    return '$indent$runtimeType(orig: "$shortOriginal")';
  }
}

class RootSemanticNode extends SemanticNode {
  final List<SemanticNode> children;
  @override
  late final List<TokenSegment> segments;
  RootSemanticNode(this.children) {
    segments = children.expand((child) => child.segments).toList();
  }
  @override
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint("Colorizing RootSemanticNode with ${children.length} children (isResultValue: $isResultValue, isArgumentList: $isArgumentList)");
    if (children.isEmpty) return '';
    if (children.length == 1) {
      return children.first.colorize(config, isResultValue: isResultValue, isArgumentList: isArgumentList);
    }
    final b = StringBuffer();
    for (int i = 0; i < children.length; i++) {
      final child = children[i];
      b.write(child.colorize(config, isResultValue: isResultValue, isArgumentList: isArgumentList));
    }
    return b.toString();
  }

  @override
  String toTreeString(int indentLevel) {
    String indent = '  ' * indentLevel;
    StringBuffer sb = StringBuffer('${indent}RootSemanticNode:\n');
    for (var c in children) {
      sb.write('${c.toTreeString(indentLevel + 1)}\n');
    }
    return sb.toString().trimRight();
  }
}

class LiteralSemanticNode extends SemanticNode {
  /* Unchanged */ final TokenSegment segment;
  LiteralSemanticNode(this.segment);
  @override
  List<TokenSegment> get segments => [segment];
  @override
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint("Colorizing LiteralSemanticNode: '${segment.text.replaceAll('\n', '\\n')}' (isResultValue: $isResultValue)");
    final text = segment.text;
    if (text.trim() == 'null') {
      return config.wrap(text, ansi: LogColorizerConfig._nullValueColor, debugTag: 'null');
    }
    if (isResultValue) {
      return config.wrap(text, ansi: LogColorizerConfig._resultValueColor, debugTag: 'result');
    }
    return text;
  }

  @override
  String toTreeString(int indentLevel) => '${'  ' * indentLevel}Literal(${segment.type}, "${segment.text.replaceAll('\n', '\\n')}")';
}

class WhitespaceSemanticNode extends SemanticNode {
  /* Unchanged */ final TokenSegment segment;
  WhitespaceSemanticNode(this.segment) : assert(segment.type == TokenType.whitespace);
  @override
  List<TokenSegment> get segments => [segment];
  @override
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint("Colorizing WhitespaceSemanticNode: '${segment.text.replaceAll('\n', '\\n')}' (isResultValue: $isResultValue)");
    return segment.text;
  }

  @override
  String toTreeString(int indentLevel) => '${'  ' * indentLevel}Whitespace("${segment.text.replaceAll('\n', '\\n')}")';
}

class FunctionCallSemanticNode extends SemanticNode {
  final TokenSegment nameSegment;
  final List<SemanticNode> leadingWhitespaceAndOpenParen;
  final List<SemanticNode> argumentsAndSeparators;
  final TokenSegment? closeParenSegment;
  // Removed trailingContentAndWhitespace

  FunctionCallSemanticNode({
    required this.nameSegment,
    required this.leadingWhitespaceAndOpenParen,
    required this.argumentsAndSeparators,
    this.closeParenSegment,
  });

  @override
  List<TokenSegment> get segments {
    final segs = <TokenSegment>[nameSegment];
    segs.addAll(leadingWhitespaceAndOpenParen.expand((n) => n.segments));
    segs.addAll(argumentsAndSeparators.expand((n) => n.segments));
    if (closeParenSegment != null) segs.add(closeParenSegment!);
    return segs;
  }

  @override
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint("Colorizing FunctionCallSemanticNode: '${nameSegment.text}' (isResultValue: $isResultValue)");
    final b = StringBuffer();
    b.write(config.wrap(nameSegment.text, ansi: LogColorizerConfig._functionCallColor, debugTag: 'func'));
    for (var node in leadingWhitespaceAndOpenParen) {
      if (node is LiteralSemanticNode && node.segment.type == TokenType.lParen) {
        b.write(config.wrap(node.toOriginalString(), ansi: LogColorizerConfig._parenColor, debugTag: 'paren'));
      } else {
        b.write(node.colorize(config, isResultValue: isResultValue));
      }
    }

    for (int i = 0; i < argumentsAndSeparators.length; i++) {
      final node = argumentsAndSeparators[i];
      if (node is LiteralSemanticNode) {
        if (node.segment.type == TokenType.comma) {
          b.write(config.wrap(node.toOriginalString(), ansi: LogColorizerConfig._commaColor, debugTag: 'comma'));
        } else if (node.segment.type == TokenType.equals) {
          b.write(config.wrap(node.toOriginalString(), ansi: LogColorizerConfig._paramSeparatorColor, debugTag: 'eq'));
        } else if (node.segment.type == TokenType.colon) {
          b.write(config.wrap(node.toOriginalString(), ansi: LogColorizerConfig._paramSeparatorColor, debugTag: 'colon'));
        } else {
          b.write(node.colorize(config, isResultValue: isResultValue));
        }
      } else if (node is ParameterSemanticNode) {
        b.write(node.colorize(config, isResultValue: isResultValue));
      } else if (node is WhitespaceSemanticNode) {
        b.write(node.colorize(config, isResultValue: isResultValue));
      } else if (node is BracketGroupSemanticNode) {
        b.write(node.colorize(config, isResultValue: isResultValue));
      } else {
        b.write(node.colorize(config, isResultValue: isResultValue));
      }
    }

    if (closeParenSegment != null) {
      b.write(config.wrap(closeParenSegment!.text, ansi: LogColorizerConfig._parenColor, debugTag: 'paren'));
    }
    return b.toString();
  }

  @override
  String toTreeString(int indentLevel) {
    /* Unchanged */ String indent = '  ' * indentLevel;
    StringBuffer sb = StringBuffer('${indent}FunctionCall(${nameSegment.text}):\n');
    sb.write('$indent  LeadWS+Paren:\n');
    for (var c in leadingWhitespaceAndOpenParen) {
      sb.write('${c.toTreeString(indentLevel + 2)}\n');
    }
    sb.write('$indent  Args+Seps:\n');
    for (var c in argumentsAndSeparators) {
      sb.write('${c.toTreeString(indentLevel + 2)}\n');
    }
    if (closeParenSegment != null) {
      sb.write('$indent  CloseParen: ${closeParenSegment!.text}\n');
    }
    return sb.toString().trimRight();
  }
}

class ParameterSemanticNode extends SemanticNode {
  /* Unchanged */
  final TokenSegment keySegment;
  final List<SemanticNode> whitespaceAndSeparator;
  final SemanticNode valueNode;
  ParameterSemanticNode({
    required this.keySegment,
    required this.whitespaceAndSeparator,
    required this.valueNode,
  });
  @override
  List<TokenSegment> get segments => [keySegment, ...whitespaceAndSeparator.expand((s) => s.segments), ...valueNode.segments];
  @override
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint("Colorizing ParameterSemanticNode: '${keySegment.text}' (isResultValue: $isResultValue)");
    final b = StringBuffer();
    final keyTextOriginal = keySegment.text;
    final keyTextLower = keyTextOriginal.trim().toLowerCase();
    final isResultParam = keyTextLower == 'result' || keyTextLower == 'results';
    b.write(config.wrap(keyTextOriginal, ansi: LogColorizerConfig._paramKeyColor, debugTag: 'param'));
    for (var node in whitespaceAndSeparator) {
      if (node is LiteralSemanticNode && (node.segment.type == TokenType.equals || node.segment.type == TokenType.colon)) {
        b.write(
          config.wrap(
            node.toOriginalString(),
            ansi: LogColorizerConfig._paramSeparatorColor,
            debugTag: node.segment.type == TokenType.equals ? 'eq' : 'colon',
          ),
        );
      } else {
        b.write(node.colorize(config, isResultValue: isResultValue));
      }
    }
    final originalValueText = valueNode.toOriginalString();
    bool isValueNull = (originalValueText.trim() == 'null');
    if (isValueNull) {
      b.write(config.wrap(originalValueText, ansi: LogColorizerConfig._nullValueColor, debugTag: 'null'));
    } else if (isResultParam || isResultValue) {
      _debugPrint("    Value is result parameter or nested result, colorizing recursively with isResultValue: true");
      b.write(valueNode.colorize(config.copyWith(debug: config.debug), isResultValue: true));
    } else {
      if (valueNode is BracketGroupSemanticNode) {
        _debugPrint("    Value is BracketGroup, colorizing recursively");
        b.write(valueNode.colorize(config.copyWith(debug: config.debug), isResultValue: isResultValue));
      } else {
        _debugPrint("    Value is simple, wrapping with param value color");
        b.write(
          config.wrap(
            valueNode.colorize(config.copyWith(debug: config.debug), isResultValue: isResultValue),
            ansi: LogColorizerConfig._paramValueColor,
            debugTag: 'val',
          ),
        );
      }
    }
    return b.toString();
  }

  @override
  String toTreeString(int indentLevel) {
    String indent = '  ' * indentLevel;
    StringBuffer sb = StringBuffer('${indent}Parameter(${keySegment.text}):\n');
    sb.write('$indent  Sep+WS:\n');
    for (var c in whitespaceAndSeparator) {
      sb.write('${c.toTreeString(indentLevel + 2)}\n');
    }
    sb.write('$indent  Value:\n');
    sb.write('${valueNode.toTreeString(indentLevel + 2)}\n');
    return sb.toString().trimRight();
  }
}

class BracketGroupSemanticNode extends SemanticNode {
  final TokenSegment openBracketSegment;
  final SemanticNode contentNode;
  final TokenSegment closeBracketSegment;
  BracketGroupSemanticNode({required this.openBracketSegment, required this.contentNode, required this.closeBracketSegment});
  @override
  List<TokenSegment> get segments => [openBracketSegment, ...contentNode.segments, closeBracketSegment];

  @override
  String colorize(LogColorizerConfig config, {bool contentIsResult = false, bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint(
      "Colorizing BracketGroupSemanticNode: '${openBracketSegment.text}' (contentIsResult: $contentIsResult, isResultValue: $isResultValue)",
    );
    final b = StringBuffer();
    final nextBracketLevelConfig = config.copyWith(bracketLevel: config.bracketLevel + 1);
    bool passIsArgumentList = false;
    String bracketTag;
    if (openBracketSegment.text == '(') {
      bracketTag = 'paren';
      passIsArgumentList = true;
    } else if (openBracketSegment.text == '[') {
      bracketTag = 'bracket';
      passIsArgumentList = true;
    } else if (openBracketSegment.text == '{') {
      bracketTag = 'brace';
    } else {
      bracketTag = 'bracket';
    }
    b.write('[c:$bracketTag]');
    b.write(openBracketSegment.text);
    b.write('[/c:$bracketTag]');

    // Only wrap in [c:val] if contentNode is a single literal and not already wrapped
    final contentStr = contentNode.colorize(nextBracketLevelConfig.copyWith(debug: config.debug),
        isResultValue: isResultValue, isArgumentList: passIsArgumentList);
    if (isResultValue) {
      b.write(config.wrap(contentStr, ansi: LogColorizerConfig._resultValueColor, debugTag: 'result'));
    } else if (isArgumentList) {
      b.write(contentStr);
    } else if (contentNode is DirectArgumentSemanticNode) {
      // Let DirectArgumentSemanticNode handle per-child wrapping
      b.write(contentStr);
    } else {
      // Only wrap in [c:val] if not already wrapped
      final trimmed = contentStr.trimLeft();
      if (trimmed.startsWith('[c:val]') ||
          trimmed.startsWith('[c:null]') ||
          trimmed.startsWith('[c:result]') ||
          trimmed.startsWith('[c:param]')) {
        b.write(contentStr);
      } else {
        b.write('[c:val]');
        b.write(contentStr);
        b.write('[/c:val]');
      }
    }

    b.write('[c:$bracketTag]');
    b.write(closeBracketSegment.text);
    b.write('[/c:$bracketTag]');
    return b.toString();
  }

  @override
  String toTreeString(int indentLevel) {
    /* Unchanged */ String indent = '  ' * indentLevel;
    StringBuffer sb = StringBuffer('${indent}BracketGroup(${openBracketSegment.text}${closeBracketSegment.text}):\n');
    sb.write('${contentNode.toTreeString(indentLevel + 1)}\n');
    return sb.toString().trimRight();
  }
}

class ArrowSemanticNode extends SemanticNode {
  /* Unchanged */
  final SemanticNode? sourceNode;
  final TokenSegment arrowSegment;
  final SemanticNode targetNode;
  ArrowSemanticNode({this.sourceNode, required this.arrowSegment, required this.targetNode}) : assert(arrowSegment.type == TokenType.arrow);
  @override
  List<TokenSegment> get segments {
    final s = <TokenSegment>[];
    if (sourceNode != null) s.addAll(sourceNode!.segments);
    s.add(arrowSegment);
    s.addAll(targetNode.segments);
    return s;
  }

  @override
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint("Colorizing ArrowSemanticNode (isResultValue: $isResultValue)");
    final b = StringBuffer();
    // Pass down the isResultValue flag to source and target nodes
    String sText = sourceNode?.colorize(config, isResultValue: isResultValue) ?? "";
    String tText = targetNode.colorize(config, isResultValue: isResultValue) ?? "";
    if (sText.isNotEmpty) {
      b.write(sText.trimRight());
      b.write(" ");
    }
    b.write(arrowSegment.text);
    if (tText.isNotEmpty) {
      b.write(" ");
      b.write(tText.trimLeft());
    } else if (sText.isEmpty && tText.isEmpty) {
      b.write(arrowSegment.text);
    }
    return b.toString();
  }

  @override
  String toTreeString(int indentLevel) {
    String indent = '  ' * indentLevel;
    StringBuffer sb = StringBuffer('${indent}Arrow:\n');
    sb.write('$indent  Source:\n');
    if (sourceNode != null) {
      sb.write('${sourceNode!.toTreeString(indentLevel + 2)}\n');
    } else {
      sb.write('$indent    (none)\n');
    }
    sb.write('$indent  Target:\n');
    sb.write('${targetNode.toTreeString(indentLevel + 2)}\n');
    return sb.toString().trimRight();
  }
}

class ParseException implements Exception {
  /* Unchanged */ final String message;
  final int? position;
  ParseException(this.message, {this.position});
  @override
  String toString() => 'ParseException: $message ${position != null ? "at $position" : ""}';
}

// Removed ComplexArgumentNode

class SemanticParser {
  final List<TokenSegment> _tokens;
  int _currentIndex = 0;
  SemanticParser(String input) : _tokens = SegmentTokenizer(input).tokenizeAll().where((t) => t.type != TokenType.eof).toList();

  TokenSegment get _current =>
      (_currentIndex < _tokens.length) ? _tokens[_currentIndex] : TokenSegment(TokenType.eof, '', _tokens.isNotEmpty ? _tokens.last.end : 0, 0);
  TokenSegment _peek(int offset) {
    final i = _currentIndex + offset;
    return (i < _tokens.length) ? _tokens[i] : TokenSegment(TokenType.eof, '', _tokens.isNotEmpty ? _tokens.last.end : 0, 0);
  }

  TokenSegment _consume() {
    final token = _current;
    if (token.type != TokenType.eof) _currentIndex++;
    return token;
  }

  TokenSegment _consumeType(TokenType type, {String? errorMessage}) {
    if (_current.type == type) return _consume();
    throw ParseException(errorMessage ?? "Expected $type but got ${_current.type} ('${_current.text}')", position: _current.start);
  }

  List<SemanticNode> _consumeAndCollectWhitespaceNodes() {
    final ws = <SemanticNode>[];
    while (_current.type == TokenType.whitespace) {
      ws.add(WhitespaceSemanticNode(_consume()));
    }
    return ws;
  }

  SemanticNode parse() {
    // Unchanged
    _debugPrint("--- Starting Parse ---");
    final nodes = <SemanticNode>[];
    while (_current.type != TokenType.eof) {
      nodes.addAll(_consumeAndCollectWhitespaceNodes());
      if (_current.type == TokenType.eof) break;
      nodes.add(_parseExpression());
    }
    nodes.addAll(_consumeAndCollectWhitespaceNodes());
    _debugPrint("--- Finished Parse (Nodes: ${nodes.length}) ---");
    if (nodes.isEmpty) return RootSemanticNode([]);
    return RootSemanticNode(nodes);
  }

  SemanticNode _parseExpression() {
    // Unchanged
    _debugPrint("Parsing Expression starting at: $_current");
    SemanticNode left = _parseAtomChain();
    List<SemanticNode> wsBeforeArrow = _consumeAndCollectWhitespaceNodes();
    if (_current.type == TokenType.arrow) {
      final arrowToken = _consume();
      List<SemanticNode> wsAfterArrow = _consumeAndCollectWhitespaceNodes();
      SemanticNode right = _parseAtomChain();
      _debugPrint("Parsed Arrow Expression");
      return RootSemanticNode(
        [left, ...wsBeforeArrow, ArrowSemanticNode(sourceNode: left, arrowSegment: arrowToken, targetNode: right), ...wsAfterArrow],
      );
    } else {
      _currentIndex -= wsBeforeArrow.fold(0, (sum, node) => sum + node.segments.length);
    }
    _debugPrint("Parsed Expression (non-arrow)");
    return left;
  }

  SemanticNode _parseAtomChain() {
    // Unchanged
    _debugPrint("Parsing Atom Chain starting at: $_current");
    List<SemanticNode> chain = [_parsePrimaryAtom()];
    while (true) {
      List<SemanticNode> interstitialWs = _consumeAndCollectWhitespaceNodes();
      TokenType nextType = _current.type;
      bool canFollow = false;
      if (chain.last is LiteralSemanticNode && nextType == TokenType.lBracket) {
        canFollow = true;
      } else if (chain.last is BracketGroupSemanticNode &&
          nextType == TokenType.identifier &&
          _peekNonWhitespaceOffset(0).type == TokenType.lParen)
        canFollow = true;
      else if (chain.last is FunctionCallSemanticNode && (nextType == TokenType.lBrace || nextType == TokenType.lBracket))
        canFollow = true;
      else if (chain.last is BracketGroupSemanticNode && nextType == TokenType.lBrace) canFollow = true;
      if (canFollow) {
        _debugPrint("Atom Chain continuing with: $_current");
        chain.addAll(interstitialWs);
        chain.add(_parsePrimaryAtom());
      } else {
        _currentIndex -= interstitialWs.fold(0, (sum, node) => sum + node.segments.length);
        break;
      }
    }
    _debugPrint("Finished Atom Chain, length: ${chain.length}");
    return chain.length == 1 ? chain.first : RootSemanticNode(chain);
  }

  SemanticNode _parsePrimaryAtom() {
    final token = _current;
    _debugPrint("Parsing Primary Atom: $token");
    // Only treat as function call if identifier is IMMEDIATELY followed by '(' (no whitespace)
    if (token.type == TokenType.identifier && _peek(1).type == TokenType.lParen && _peek(1).start == token.end) {
      _debugPrint("Detected function call for ${token.text}");
      return _parseFunctionCall();
    }
    // Handle standalone parentheses as bracket groups
    if (token.type == TokenType.lParen) {
      return _parseBracketGroup(TokenType.lParen, TokenType.rParen);
    }
    if (token.type == TokenType.identifier || token.type == TokenType.string || token.type == TokenType.unknown) {
      return LiteralSemanticNode(_consume());
    }
    if (token.type == TokenType.lBracket) {
      return _parseBracketGroup(TokenType.lBracket, TokenType.rBracket);
    }
    if (token.type == TokenType.lBrace) {
      return _parseBracketGroup(TokenType.lBrace, TokenType.rBrace);
    }
    if (token.type == TokenType.equals ||
        token.type == TokenType.comma ||
        token.type == TokenType.arrow ||
        token.type == TokenType.colon ||
        token.type == TokenType.rParen) {
      return LiteralSemanticNode(_consume());
    }
    if (token.type == TokenType.eof) {
      throw ParseException("Unexpected EOF", position: token.start);
    }
    throw ParseException("Unhandled token type: ${token.type} ('${token.text}')", position: token.start);
  }

  FunctionCallSemanticNode _parseFunctionCall() {
    _debugPrint("Parsing Function Call starting at: $_current");
    final nameToken = _consumeType(TokenType.identifier);
    final List<SemanticNode> wsAndParen = [..._consumeAndCollectWhitespaceNodes(), LiteralSemanticNode(_consumeType(TokenType.lParen))];
    final argsSeps = <SemanticNode>[..._consumeAndCollectWhitespaceNodes()];

    while (_current.type != TokenType.rParen && _current.type != TokenType.eof) {
      argsSeps.add(_parseArgumentOrParameter(inFunctionCallArguments: true));
      argsSeps.addAll(_consumeAndCollectWhitespaceNodes());
      if (_current.type == TokenType.comma) {
        argsSeps.add(LiteralSemanticNode(_consume()));
        argsSeps.addAll(_consumeAndCollectWhitespaceNodes());
      } else if (_current.type != TokenType.rParen) break;
    }
    TokenSegment? closeParenToken = (_current.type == TokenType.rParen) ? _consume() : null;
    List<SemanticNode> trailingWsAndContent = []; // Handled by AtomChain

    _debugPrint("Finished Function Call: ${nameToken.text}");
    return FunctionCallSemanticNode(
      nameSegment: nameToken,
      leadingWhitespaceAndOpenParen: wsAndParen,
      argumentsAndSeparators: argsSeps,
      closeParenSegment: closeParenToken,
    );
  }

  SemanticNode _parseArgumentOrParameter({bool inFunctionCallArguments = false, bool inBracketGroup = false}) {
    // Prevent infinite loop: if current token is a closing bracket/paren/curly or EOF, return an empty node and advance index
    if (_current.type == TokenType.rParen ||
        _current.type == TokenType.rBracket ||
        _current.type == TokenType.rBrace ||
        _current.type == TokenType.eof) {
      _debugPrint("Parsed empty Direct Argument");
      if (_current.type != TokenType.eof) _consume();
      return RootSemanticNode([]);
    }
    _debugPrint("Parsing Argument/Parameter starting at: $_current");
    // Split on top-level commas
    List<SemanticNode> argumentNodes = [];

    while (_current.type != TokenType.eof &&
        _current.type != TokenType.rParen &&
        _current.type != TokenType.rBracket &&
        _current.type != TokenType.rBrace) {
      // Check for parameter assignment: identifier [ws] (= or :) [ws] value
      // Always check for parameter assignment at the start of every argument (after comma or opening bracket)
      if (_current.type == TokenType.identifier) {
        final keyToken = _current;
        int lookahead = _currentIndex + 1;
        // Skip whitespace after identifier
        int wsCheck = lookahead;
        while (wsCheck < _tokens.length && _tokens[wsCheck].type == TokenType.whitespace) {
          wsCheck++;
        }
        bool isParameter = false;
        if (wsCheck < _tokens.length && (_tokens[wsCheck].type == TokenType.equals || _tokens[wsCheck].type == TokenType.colon)) {
          // Only if this is the start of the argument (not after a literal, comma, or bracket)
          final atStart = _currentIndex == 0 ||
              _tokens[_currentIndex - 1].type == TokenType.comma ||
              _tokens[_currentIndex - 1].type == TokenType.lParen ||
              _tokens[_currentIndex - 1].type == TokenType.lBracket ||
              _tokens[_currentIndex - 1].type == TokenType.lBrace;
          final nextIsIdOrColon = wsCheck + 1 < _tokens.length &&
              (_tokens[wsCheck + 1].type == TokenType.identifier || _tokens[wsCheck + 1].type == TokenType.colon);
          if (atStart && !nextIsIdOrColon) {
            isParameter = true;
          }
        }
        if (isParameter) {
          int sepIdx = lookahead;
          while (sepIdx < _tokens.length && _tokens[sepIdx].type != TokenType.equals && _tokens[sepIdx].type != TokenType.colon) {
            sepIdx++;
          }
          _consume(); // key
          List<SemanticNode> wsBeforeSep = [];
          while (_current.type == TokenType.whitespace) {
            wsBeforeSep.add(WhitespaceSemanticNode(_consume()));
          }
          final sepNode = LiteralSemanticNode(_consume()); // = or :
          List<SemanticNode> wsAfterSep = [];
          while (_current.type == TokenType.whitespace) {
            wsAfterSep.add(WhitespaceSemanticNode(_consume()));
          }
          // Always parse the value as a single node (bracket group, literal, or special value)
          SemanticNode valueNode;
          if (_current.type == TokenType.lParen) {
            valueNode = _parseBracketGroup(TokenType.lParen, TokenType.rParen);
          } else if (_current.type == TokenType.lBracket) {
            valueNode = _parseBracketGroup(TokenType.lBracket, TokenType.rBracket);
          } else if (_current.type == TokenType.lBrace) {
            valueNode = _parseBracketGroup(TokenType.lBrace, TokenType.rBrace);
          } else if (_current.type == TokenType.string) {
            valueNode = LiteralSemanticNode(_consume());
          } else if (_current.type == TokenType.identifier &&
              (_current.text == 'null' || _current.text == 'success' || _current.text == 'partial')) {
            valueNode = LiteralSemanticNode(_consume());
          } else {
            valueNode = LiteralSemanticNode(_consume());
          }
          argumentNodes.add(
            ParameterSemanticNode(
              keySegment: keyToken,
              whitespaceAndSeparator: [...wsBeforeSep, sepNode, ...wsAfterSep],
              valueNode: valueNode,
            ),
          );
          // After value, may be comma/whitespace
          if (_current.type == TokenType.comma) {
            argumentNodes.add(LiteralSemanticNode(_consume()));
          }
          while (_current.type == TokenType.whitespace) {
            argumentNodes.add(WhitespaceSemanticNode(_consume()));
          }
        }
      }
      // Handle bracket groups
      if (_current.type == TokenType.lParen) {
        argumentNodes.add(_parseBracketGroup(TokenType.lParen, TokenType.rParen));
        continue;
      } else if (_current.type == TokenType.lBracket) {
        argumentNodes.add(_parseBracketGroup(TokenType.lBracket, TokenType.rBracket));
        continue;
      } else if (_current.type == TokenType.lBrace) {
        argumentNodes.add(_parseBracketGroup(TokenType.lBrace, TokenType.rBrace));
        continue;
      }
      // Null literal
      if (_current.type == TokenType.identifier && _current.text.trim() == 'null') {
        argumentNodes.add(LiteralSemanticNode(_consume()));
        if (_current.type == TokenType.comma) {
          argumentNodes.add(LiteralSemanticNode(_consume()));
        }
        while (_current.type == TokenType.whitespace) {
          argumentNodes.add(WhitespaceSemanticNode(_consume()));
        }
        continue;
      }
      // Otherwise, collect until comma or end
      List<TokenSegment> valTokens = [];
      while (_current.type != TokenType.eof &&
          _current.type != TokenType.comma &&
          _current.type != TokenType.rParen &&
          _current.type != TokenType.rBracket &&
          _current.type != TokenType.rBrace) {
        // If we hit a bracket, break to let the outer loop handle
        if (_current.type == TokenType.lParen || _current.type == TokenType.lBracket || _current.type == TokenType.lBrace) {
          break;
        }
        valTokens.add(_consume());
      }
      if (valTokens.isNotEmpty) {
        // If all tokens are whitespace, add as whitespace nodes
        if (valTokens.every((t) => t.type == TokenType.whitespace)) {
          argumentNodes.addAll(valTokens.map((t) => WhitespaceSemanticNode(t)));
        } else {
          // Otherwise, join as a literal
          final joinedText = valTokens.map((t) => t.text).join();
          final start = valTokens.first.start;
          final end = valTokens.last.end;
          argumentNodes.add(LiteralSemanticNode(TokenSegment(valTokens.first.type, joinedText, start, end)));
        }
      }
      if (_current.type == TokenType.comma) {
        argumentNodes.add(LiteralSemanticNode(_consume()));
      }
      while (_current.type == TokenType.whitespace) {
        argumentNodes.add(WhitespaceSemanticNode(_consume()));
      }
    }
    if (argumentNodes.length == 1) return argumentNodes.first;
    return DirectArgumentSemanticNode(argumentNodes);
  }

  BracketGroupSemanticNode _parseBracketGroup(TokenType open, TokenType close) {
    final openBracketSegment = _consume(); // consume open bracket
    List<SemanticNode> contentNodes = [];
    // Special handling for function argument lists: split on commas at top level
    // Always treat bracket group content as a comma-separated list of arguments/parameters at the top level
    while (_current.type != close && _current.type != TokenType.eof) {
      // Always skip whitespace before parsing the next argument
      while (_current.type == TokenType.whitespace) {
        contentNodes.add(WhitespaceSemanticNode(_consume()));
      }
      // Check for parameter assignment at the start of every argument
      bool isParameter = false;
      if (_current.type == TokenType.identifier) {
        int lookahead = _currentIndex + 1;
        int wsCheck = lookahead;
        while (wsCheck < _tokens.length && _tokens[wsCheck].type == TokenType.whitespace) {
          wsCheck++;
        }
        if (wsCheck < _tokens.length && (_tokens[wsCheck].type == TokenType.equals || _tokens[wsCheck].type == TokenType.colon)) {
          // Only if this is the start of the argument (not after a literal, comma, or bracket)
          final atStart = _currentIndex == 0 ||
              _tokens[_currentIndex - 1].type == TokenType.comma ||
              _tokens[_currentIndex - 1].type == TokenType.lParen ||
              _tokens[_currentIndex - 1].type == TokenType.lBracket ||
              _tokens[_currentIndex - 1].type == TokenType.lBrace;
          final nextIsIdOrColon = wsCheck + 1 < _tokens.length &&
              (_tokens[wsCheck + 1].type == TokenType.identifier || _tokens[wsCheck + 1].type == TokenType.colon);
          if (atStart && !nextIsIdOrColon) {
            isParameter = true;
          }
        }
      }
      if (isParameter) {
        final keyToken = _current;
        _consume(); // key
        List<SemanticNode> wsBeforeSep = [];
        while (_current.type == TokenType.whitespace) {
          wsBeforeSep.add(WhitespaceSemanticNode(_consume()));
        }
        final sepNode = LiteralSemanticNode(_consume()); // = or :
        List<SemanticNode> wsAfterSep = [];
        while (_current.type == TokenType.whitespace) {
          wsAfterSep.add(WhitespaceSemanticNode(_consume()));
        }
        // Always parse the value as a single node (bracket group, literal, or special value)
        SemanticNode valueNode;
        if (_current.type == TokenType.lParen) {
          valueNode = _parseBracketGroup(TokenType.lParen, TokenType.rParen);
        } else if (_current.type == TokenType.lBracket) {
          valueNode = _parseBracketGroup(TokenType.lBracket, TokenType.rBracket);
        } else if (_current.type == TokenType.lBrace) {
          valueNode = _parseBracketGroup(TokenType.lBrace, TokenType.rBrace);
        } else if (_current.type == TokenType.string) {
          valueNode = LiteralSemanticNode(_consume());
        } else if (_current.type == TokenType.identifier &&
            (_current.text == 'null' || _current.text == 'success' || _current.text == 'partial')) {
          valueNode = LiteralSemanticNode(_consume());
        } else {
          valueNode = LiteralSemanticNode(_consume());
        }
        contentNodes.add(
          ParameterSemanticNode(
            keySegment: keyToken,
            whitespaceAndSeparator: [...wsBeforeSep, sepNode, ...wsAfterSep],
            valueNode: valueNode,
          ),
        );
      } else {
        // Not a parameter assignment, treat as a value node
        contentNodes.add(_parseArgumentOrParameter(inFunctionCallArguments: true, inBracketGroup: true));
      }
      // If next token is a comma, consume it and continue
      if (_current.type == TokenType.comma) {
        contentNodes.add(LiteralSemanticNode(_consume()));
      }
      // Skip whitespace between arguments
      while (_current.type == TokenType.whitespace) {
        contentNodes.add(WhitespaceSemanticNode(_consume()));
      }
    }
    final closeBracketSegment = _current.type == close ? _consume() : (throw Exception('Expected closing bracket $close'));
    return BracketGroupSemanticNode(
      openBracketSegment: openBracketSegment,
      contentNode: DirectArgumentSemanticNode(contentNodes),
      closeBracketSegment: closeBracketSegment,
    );
  }

  TokenSegment _peekNonWhitespaceOffset(int offset) {
    /* Unchanged */ int i = _currentIndex + offset;
    while (i < _tokens.length && _tokens[i].type == TokenType.whitespace) {
      i++;
    }
    return (i < _tokens.length) ? _tokens[i] : TokenSegment(TokenType.eof, '', _tokens.isNotEmpty ? _tokens.last.end : 0, 0);
  }
}

class DirectArgumentSemanticNode extends SemanticNode {
  final List<SemanticNode> children;
  @override
  late final List<TokenSegment> segments;
  DirectArgumentSemanticNode(this.children) {
    segments = children.expand((child) => child.segments).toList();
  }

  @override
  String colorize(LogColorizerConfig config, {bool isResultValue = false, bool isArgumentList = false}) {
    _debugPrint("Colorizing DirectArgumentSemanticNode with ${children.length} children (isResultValue: $isResultValue)");
    // If this argument is a single literal, wrap as [c:val] unless it's null
    if (children.length == 1 && children.first is LiteralSemanticNode) {
      final child = children.first as LiteralSemanticNode;
      final text = child.toOriginalString();
      if (text.trim() == 'null') {
        return config.wrap(text, ansi: LogColorizerConfig._nullValueColor, debugTag: 'null');
      }
      return config.wrap(text, ansi: LogColorizerConfig._paramValueColor, debugTag: 'val');
    }
    // Otherwise, composite argument: wrap each child if not already wrapped
    final b = StringBuffer();
    for (var child in children) {
      final colored = child.colorize(config, isResultValue: isResultValue);
      final trimmed = colored.trimLeft();
      if (trimmed.startsWith('[c:val]') ||
          trimmed.startsWith('[c:param]') ||
          trimmed.startsWith('[c:null]') ||
          trimmed.startsWith('[c:result]')) {
        b.write(colored);
      } else {
        b.write(config.wrap(colored, ansi: LogColorizerConfig._paramValueColor, debugTag: 'val'));
      }
    }
    return b.toString();
  }

  @override
  String toTreeString(int indentLevel) {
    String indent = '  ' * indentLevel;
    StringBuffer sb = StringBuffer('${indent}DirectArgument:\n');
    for (var c in children) {
      sb.write('${c.toTreeString(indentLevel + 1)}\n');
    }
    return sb.toString().trimRight();
  }
}

//region Colorizer
class LogColorizer {
  /* Unchanged */
  static String colorizeRecord(LogRecord record, {bool debug = false}) {
    final time = record.time.toIso8601String().substring(11, 19);
    final levelName = record.level.name;
    final loggerName = record.loggerName;
    final message = LogColorizer.colorString(record.message, debug: debug);
    final levelColor = levelColors[record.level] ?? LogColorizerConfig._resetColor;
    final sb = StringBuffer()
      ..write(time)
      ..write(" ")
      ..write(levelColor)
      ..write(levelName)
      ..write(LogColorizerConfig._resetColor)
      ..write(" [")
      ..write(loggerName)
      ..write("] ")
      ..write(message);
    if (record.error != null) {
      sb
        ..write("\n")
        ..write(levelColor)
        ..write(record.error)
        ..write(LogColorizerConfig._resetColor);
    }
    if (record.stackTrace != null) {
      sb
        ..write("\n")
        ..write(record.stackTrace);
    }
    return sb.toString();
  }

  static String colorString(String input, {bool debug = false}) {
    if (input.trim().isEmpty) return input;
    SemanticNode astRoot;
    try {
      astRoot = SemanticParser(input).parse();
      if (debug && _enableDebugPrints) {
        print("\n--- AST for input: '$input' ---\n${astRoot.toTreeString(0)}\n--- End AST ---");
      }
    } catch (e, s) {
      print('Parser Error for input "$input": $e\n$s');
      return input;
    }
    final config = LogColorizerConfig(debug: debug);
    _debugPrint("Applying default recursive colorization.");
    return astRoot.colorize(config);
  }
}
//endregion
