import 'dart:io' show Platform;

import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart' hide Store;
import 'package:flutter_mobx/flutter_mobx.dart';

import '../../../app/domain/file.dart';
import '../../../app/domain/path.dart';
import '../../../app/util/log_utils.dart';
import '../../util/context_extensions.dart';
import '../../util/hooks/hook_utils.dart';
import '../../util/widgets/auto_complete_text_field.dart';
import '../../util/widgets/breadcrumbs.dart';

const ellipsisBreadcrumbIndex = -1;

class NavigationBar extends HookWidget {
  const NavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsRepository = context.settingsRepository;
    final directoryViewStore = context.directoryViewStore;

    final isInputVisible = useState(false);
    void hideInput() => isInputVisible.value = false;
    void showInput() => isInputVisible.value = true;

    final textController = useMemoized(
      () => AutoCompleteTextFieldController(
        text: directoryViewStore.dir.absolutePath,
        calcSuggestions: directoryViewStore.calcAutoCompleteSuggestions,
        stringifySuggestion: (it) => it.absolutePath,
        config: AutoCompleteTextFieldControllerConfig(
          preselectFirstSuggestion: settingsRepository.settings.pathTextfieldSettings.preselectFirstSuggestion,
          showAutoCompletedTextInTheMiddle: settingsRepository.settings.pathTextfieldSettings.showAutoCompletedTextInTheMiddle,
          submitSuggestionOnEnter: settingsRepository.settings.pathTextfieldSettings.submitSuggestionOnEnter,
        ),
      ),
      [settingsRepository.settings],
    );

    void confirmInput([String? path]) {
      hideInput();
      directoryViewStore.changeDir((path ?? textController.text).asPath(resolve: true));
    }

    final focusNode = useFocusNode(
      debugLabel: 'CurrentPath',
      skipTraversal: true,
      onKeyEvent: (_, event) {
        if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.escape) {
          hideInput();
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
    );
    useListener(focusNode, () {
      if (!focusNode.hasFocus) {
        hideInput();
      }
    });

    useEffect(() {
      if (isInputVisible.value) {
        textController.text = directoryViewStore.dir.absolutePath +
            (settingsRepository.settings.pathTextfieldSettings.addPathSeparatorOnShow ? RawPath.pathSeparator : '');
        textController.selection = TextSelection(baseOffset: 0, extentOffset: textController.text.length);
        focusNode.requestFocus();
      } else {
        focusNode.unfocus();
      }
      return null;
    }, [isInputVisible.value]);

    final hoveredIndex = useState<int?>(null);
    bool breadcrumbsIsHovered(int index) => hoveredIndex.value == index;

    void breadcrumbsHoverOnEnter(int index, RawPath? path) {
      if (kDebugMode) {
        logger.finest("breadcrumbsHoverOnEnter(index=$index, path=$path)");
      }
      assert(hoveredIndex.value == null, "Already hovered on [$hoveredIndex]");
      hoveredIndex.value = index;
    }

    void breadcrumbsHoverOnExit(int index, RawPath? path) {
      if (kDebugMode) {
        logger.finest("breadcrumbsHoverOnExit(index=$index, path=$path)");
      }
      assert(hoveredIndex.value != null, "Not hovered!");
      hoveredIndex.value = null;
    }

    if (isInputVisible.value) {
      return SizedBox(
        height: 28,
        child: AutoCompleteTextField<File>(
          onHide: directoryViewStore.stopAutoComplete,
          onSubmitted: confirmInput,
          focusNode: focusNode,
          controller: textController,
          inputDecoration: InputDecoration(
            isDense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 7.5),
            suffixIcon: TextButton(
              onPressed: confirmInput,
              style: TextButton.styleFrom(shape: const BeveledRectangleBorder()),
              child: Icon(Icons.check, color: context.primaryColor),
            ),
          ),
        ),
      );
    } else {
      return Observer(
        builder: (context) {
          final elements = directoryViewStore.dir.elements;
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: showInput,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    child: Breadcrumbs(
                      items: elements,
                      itemBuilder: (context, e, index) {
                        final lastItem = index == elements.length - 1;
                        final onlyItem = elements.length == 1;
                        return TextSpan(
                          text: !onlyItem && e.isRoot && Platform.isWindows ? e.name.removeSuffix(RawPath.pathSeparator) : e.name,
                          onEnter: (_) => breadcrumbsHoverOnEnter(index, e),
                          onExit: (_) => breadcrumbsHoverOnExit(index, e),
                          recognizer: TapGestureRecognizer()..onTap = () => !lastItem ? directoryViewStore.changeDir(e) : showInput(),
                          mouseCursor: lastItem || onlyItem ? SystemMouseCursors.text : SystemMouseCursors.click,
                          style: lastItem || breadcrumbsIsHovered(index) ? TextStyle(color: context.theme.colorScheme.secondary) : null,
                        );
                      },
                      separator: RawPath.pathSeparator,
                      collapsedElementBuilder: (context, collapse) => TextSpan(
                        text: "...",
                        onEnter: (_) => breadcrumbsHoverOnEnter(ellipsisBreadcrumbIndex, null),
                        onExit: (_) => breadcrumbsHoverOnExit(ellipsisBreadcrumbIndex, null),
                        recognizer: TapGestureRecognizer()..onTap = collapse,
                        mouseCursor: SystemMouseCursors.click,
                        style: breadcrumbsIsHovered(ellipsisBreadcrumbIndex) ? TextStyle(color: context.theme.colorScheme.secondary) : null,
                      ),
                      style: context.titleMedium,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: directoryViewStore.reload,
                  style: TextButton.styleFrom(foregroundColor: context.primaryColor, shape: const BeveledRectangleBorder()),
                  child: const Icon(Icons.refresh),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  static final logger = loggerFor(NavigationBar, Level.FINE);
}
