import 'package:flutter/material.dart';

class ErrorText extends StatelessWidget {
  const ErrorText(this.error, {super.key});

  final String error;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Try horizontal layout first if there's enough width
            const iconSize = 24.0;
            const spacing = 8.0;
            const minTextWidth = 100.0; // Minimum width for readable text

            final availableWidth = constraints.maxWidth;
            final hasEnoughWidth = availableWidth >= (iconSize + spacing + minTextWidth);

            if (hasEnoughWidth) {
              // Horizontal layout with icon on the left
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red[700],
                    size: iconSize,
                  ),
                  const SizedBox(width: spacing),
                  Flexible(
                    child: Text(
                      error,
                      textAlign: TextAlign.center,
                      softWrap: true,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ],
              );
            } else {
              // Vertical layout when space is constrained
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red[700],
                    size: iconSize,
                  ),
                  const SizedBox(height: spacing),
                  Text(
                    error,
                    textAlign: TextAlign.center,
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }
}
