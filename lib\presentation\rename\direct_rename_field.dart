import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';

import '../directory_view/domain/display_file.dart';
import '../util/context_extensions.dart';

/// A direct implementation of a rename field with minimal wrappers
class DirectRenameField extends StatefulWidget {
  final DisplayFile file;

  const DirectRenameField({
    super.key,
    required this.file,
  });

  @override
  State<DirectRenameField> createState() => _DirectRenameFieldState();
}

class _DirectRenameFieldState extends State<DirectRenameField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  final List<ReactionDisposer> _reactionDisposers = [];

  @override
  void initState() {
    super.initState();

    // Setup the controller with the initial file name
    final renameStore = context.renameStore;

    _controller.text = renameStore.newFileName;

    // Set up reactions to observe the rename store
    _reactionDisposers.add(reaction(
      (_) => renameStore.isRenaming,
      (isRenaming) {
        if (!(isRenaming)) {
          // Rename was cancelled, make sure to hide the tooltip
          _hideErrorTooltip();
        }
      },
    ));

    // Set up a reaction to observe the rename store's newFileName property
    // This will help us keep the text field in sync across panes
    _reactionDisposers.add(reaction(
      (_) => renameStore.newFileName,
      (newFileName) {
        // Only update if the text is different to avoid cursor jumping
        if (_controller.text != newFileName) {
          // Remember the current selection
          final selection = _controller.selection;

          // Update the text
          _controller.text = newFileName;

          // Restore the selection if possible
          if (selection.baseOffset <= newFileName.length && selection.extentOffset <= newFileName.length) {
            _controller.selection = selection;
          }
        }
      },
    ));

    // Force focus and selection in multiple ways to ensure it works
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Force focus
      FocusScope.of(context).requestFocus(_focusNode);

      // Select the appropriate part of the text
      if (widget.file is FileDisplayFile) {
        final file = (widget.file as FileDisplayFile).file;
        if (file.isDirectory) {
          // Select entire name for directories
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        } else {
          // Select only name without extension for files
          final nameWithoutExt = renameStore.getNameWithoutExtension(file);
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: nameWithoutExt.length,
          );
        }
      }

      // Schedule multiple focus requests to ensure it gets focus
      // for (int i = 0; i < 5; i++) {
      //   Future.delayed(Duration(milliseconds: 50 * (i + 1)), () {
      //     if (mounted) {
      //       FocusScope.of(context).requestFocus(_focusNode);
      //     }
      //   });
      // }
    });

    // Add keyboard listeners
    _focusNode.addListener(() {
      // Monitor focus state
      if (_focusNode.hasFocus) {
        // Has focus
      } else {
        // Lost focus - cancel rename
        final renameStore = context.renameStore;
        renameStore.cancelRename();
      }
    });
  }

  @override
  void dispose() {
    // Clean up resources
    _hideErrorTooltip();
    // Dispose of all reactions
    for (final disposer in _reactionDisposers) {
      disposer();
    }
    _reactionDisposers.clear();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Create a tooltip overlay for validation errors
  OverlayEntry? _errorTooltip;

  void _showErrorTooltip(BuildContext context, String errorMessage) {
    _hideErrorTooltip();

    // Get the position of the text field
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    _errorTooltip = OverlayEntry(
      builder: (context) {
        return Positioned(
          left: position.dx,
          // Position above the text field
          top: position.dy - 40,
          child: Material(
            color: Colors.transparent,
            child: Container(
              constraints: const BoxConstraints(maxWidth: 300),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                border: Border.all(color: Colors.red.shade300),
                borderRadius: BorderRadius.circular(4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(40),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                errorMessage,
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
          ),
        );
      },
    );

    Overlay.of(context).insert(_errorTooltip!);
  }

  void _hideErrorTooltip() {
    _errorTooltip?.remove();
    _errorTooltip = null;
  }

  @override
  Widget build(BuildContext context) {
    final renameStore = context.renameStore;

    // Force focus on the text field
    FocusScope.of(context).requestFocus(_focusNode);

    return Observer(builder: (_) {
      // Check if there's a validation error
      final hasError = renameStore.validationError != null;
      final errorMessage = renameStore.validationError;

      // Show or hide error tooltip
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (hasError && errorMessage != null) {
          _showErrorTooltip(context, errorMessage);
        } else {
          _hideErrorTooltip();
        }
      });

      return Material(
        color: Colors.transparent,
        child: Container(
          height: 22, // Match the standard row height
          padding: EdgeInsets.zero,
          margin: EdgeInsets.zero,
          alignment: Alignment.centerLeft,
          child: Focus(
            onKeyEvent: (FocusNode node, KeyEvent event) {
              if (event is KeyDownEvent) return KeyEventResult.ignored;
              switch (event.logicalKey) {
                case LogicalKeyboardKey.enter:
                  if (renameStore.isRenaming && renameStore.validationError == null) {
                    renameStore.acceptRename();
                  }
                  return KeyEventResult.handled;
                case LogicalKeyboardKey.escape:
                  renameStore.cancelRename();
                  return KeyEventResult.handled;
              }
              return KeyEventResult.ignored;
            },
            child: TextFormField(
              controller: _controller,
              focusNode: _focusNode,
              style: const TextStyle(fontSize: 14),
              autofocus: true,
              maxLines: 1,
              decoration: InputDecoration(
                isDense: true,
                contentPadding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 4.0),
                // No error text - we're using a tooltip instead
                errorText: null,
                filled: true,
                fillColor: hasError ? Colors.red.withAlpha(20) : Colors.blue.withAlpha(10),
                // Always show a border to make the rename field more obvious
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.0),
                  borderSide: BorderSide(
                      color: hasError ? Colors.red.withAlpha(128) : Colors.blue.withAlpha(128)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.0),
                  borderSide: BorderSide(
                      color: hasError ? Colors.red.withAlpha(128) : Colors.blue.withAlpha(128)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.0),
                  borderSide: BorderSide(
                      color: hasError ? Colors.red.withAlpha(179) : Colors.blue.withAlpha(179), width: 1.5),
                ),
                // No suffix icons
              ),
              onChanged: (value) {
                renameStore.setRenamedFileName(value);
              },
              onEditingComplete: () {
                if (renameStore.isRenaming && renameStore.validationError == null) {
                  renameStore.acceptRename();
                }
              },
              onFieldSubmitted: (_) {
                if (renameStore.isRenaming && renameStore.validationError == null) {
                  renameStore.acceptRename();
                }
              },
              // We're not using input formatters anymore because we want to show validation errors
              // instead of silently preventing invalid characters
              // This allows for better user feedback
            ),
          ),
        ),
      );
    });
  }
}
