# Project Overview
- QFiler is a dual-pane file explorer built with Flutter for desktop platforms
- Primary focus is on Windows platform with future multi-platform support
- The application supports extensive keyboard and mouse navigation
- File operations can be queued to operate asynchronously with preview functionality
- This is a rewrite of an existing Electron application (found in the `legacy` directory)

# Code Structure
- Main Flutter code is in the `lib` directory
- UI components and presentation logic are in `lib/presentation`
- Business logic and domain models are in `lib/app`
- Legacy implementation in Electron/TypeScript/React is in the `legacy` directory
- Most UI components and business logic can be found in `legacy/renderer`

# Core Components
- The dual-pane interface is managed by `AllPanesStore` with left and right panes
- Each pane is represented by a `PaneStore` that manages its state
- Directory navigation and file operations are handled in `lib/app/file_system`
- User settings and preferences are managed in `lib/app/settings`
- File operations are managed through an asynchronous queue in `lib/app/task`

# Architecture Overview

## File System Architecture
- The application has a clear separation between global data (file system) and pane-specific data (views into the file system)
- Global data includes the file system and its content, while pane data represents views into subfolders

### Path Repository (`lib/app/file_system/real/path_repository.dart`)
- Lowest level repository responsible for reading real files from the file system
- Exposes files as `PathWithStatus` objects
- Uses `FileSystem` abstraction (`lib/app/domain/file_system.dart`) for platform independence
- Uses `DirectoryLister` for efficient parallel directory listing through a thread pool
- Handles file system watching and returns `Paths` objects (static or live views of folders)
- `Paths` is a live-updating view of the real file system that automatically updates when files change, if its set to watch

### File Repository (`lib/app/file_system/file_repository.dart`)
- Higher-level layer and main file system accessor throughout the app
- Manages when to watch/unwatch directories
- Communicates with `PathRepository`
- Operates with `File` objects (`lib/app/domain/file.dart`)
- Returns `Files` objects which are wrappers around both `Paths` and `PendingFiles`
- `Files` is a reconciled view that observes updates from both `Paths` and `PendingFiles`
- Responsible for combining real file system files with pending files to create a unified view

### Task System
- `TaskStore` (`lib/app/task/task_store.dart`) manages the queue of file operations
- `PendingFileStore` (`lib/app/task/pending_file_store.dart`) tracks files affected by pending operations
  - Maintains a collection of `PendingFiles` that represent files being added, modified, or removed
  - Provides a live-updating view of pending file operation for a specific directory
  - Notifies observers when pending files are added, updated, or completed
- `TaskProcessor` (`lib/app/task/task_processor.dart`) plans tasks and adds files to `PendingFileStore`
- `FileOpExecutor` (`lib/app/task/file_op_executor.dart`) executes individual file operations
- Pending files are immediately added to the file system view before actual execution
- Operations can be queued to operate on pending files
- Pending files may cancel each other out (e.g., add then delete)
- When a pending file operation completes, `FileRepository` updates its reconciled view accordingly

### Directory View Store (`lib/presentation/directory_view/directory_view_store.dart`)
- Pane-specific store for displaying directory contents
- Acts as a view into a specific part of the `FileRepository`
- Maintains pane-specific state (selected files, focused file)
- Consumes the reconciled `Files` view from `FileRepository` for the current directory
- Observes changes to both real files and pending files through the `Files` wrapper
- Displays the unified view to the user, showing both real and pending files seamlessly

# UI Architecture
- The application uses MobX for reactive state management
  - Stores are defined with `@observable` properties and `@action` methods
  - UI components use `Observer` widgets to react to state changes
  - Reactions are used to respond to state changes programmatically
- The main UI is split using `MultiSplitView` for resizable panes
- Each pane has tabs managed by `DirectoryTabs` component
- Directory contents are displayed using `DirectoryViewStore` and rendered with the `Davi` table widget
- Navigation history is tracked by `HistoryStore`
- UI components are connected to stores using context extensions and the Provider pattern

# Key Features
- Command-based keyboard shortcuts and navigation system
  - VSCode-like 'when' expressions for context-aware keybindings
  - Command palette for executing commands with search functionality
- Advanced file operation queueing system that allows:
  - Operations to be queued without waiting for previous operations to complete
  - Preview of destination files before operations are complete
  - Ability to operate on files that are still being processed
  - Progress tracking for all queued operations
- Tab-based navigation within each pane
- History tracking for navigation within each pane
- Settings persistence for user preferences
- In-place file renaming with validation and error feedback
- Hot-reload support for development

# Development Guidelines
- Use Flutter best practices for desktop application development
- Follow MobX patterns for state management
- Use flutter_hooks and HookWidget for stateful components
- Access dependencies through context extensions instead of Provider.of
- Strongly prefer dependency injection over singleton patterns
- Create instances in the root store and inject them where needed
- NEVER use static instances or singletons with static accessors (e.g., RootStore.instance)
- Always pass dependencies through constructors
- Use DataRepository for reading and writing config files
- Follow naming convention: Stores hold UI state, Repositories hold business logic state (not tied to UI)
- All BuildContext extensions should be centralized in lib/presentation/extensions/context_extensions.dart
- Don't create Store classes for repositories (e.g., SettingsStore should be SettingsRepository)
- Use logging at various levels instead of debug prints
  - Protect logging with if (kDebugMode) checks
  - Use info level for interesting notices
  - Use fine/finer/finest levels for more verbose logging
  - Use warning/severe for errors
  - When logging in the context of a directory, prefix log messages with the directory path
  - When logging file operations, include the file path in square brackets
  - It's ok to use debugPrint for temporary prints while debugging, but anything more permanent should go through logging
- Maintain separation between presentation and business logic
- Support keyboard navigation for all operations
- Do not refactor code that you did not write or do not understand unless explicitly requested
- Ensure file operations are safe and provide proper feedback
- Reference the legacy implementation in `legacy` when implementing features
- Most of the logic already exists and works in the legacy implementation
- New features should be rewritten from TypeScript/React to Dart/Flutter
- The `legacy/renderer` directory contains most of the interesting UI components and business logic

# Platform Specifics
- Windows is the primary target platform
- Platform-specific code is organized in respective platform directories
- File system operations should handle platform-specific paths and behaviors

# Legacy Implementation
- The legacy implementation uses Electron with TypeScript and React
- Most of the UI components are in `legacy/renderer`
- The rest of the code in `legacy` is mostly Electron plumbing
- When implementing new features, refer to the legacy code for guidance
- The existing logic should be translated from TypeScript to Dart

# QFiler Project Structure and Functionality

## Main Application Structure

### Flutter Implementation (Current)

#### Core Directories:
- `lib/`: Main Flutter code
  - `app/`: Business logic and domain models
    - `app/domain/`: Core domain models (File, Path, etc.)
    - `app/file_system/`: File system operations and abstractions
    - `app/settings/`: User preferences and settings management
    - `app/task/`: Async task queue for file operations
    - `app/util/`: Common utilities and helper functions
  - `presentation/`: UI components and presentation logic
    - `presentation/directory_view/`: Directory listing and navigation components
    - `presentation/intent/`: Keyboard shortcuts and intent actions
    - `presentation/pane/`: Dual-pane management
    - `presentation/util/`: UI utility components and widgets

#### Key Files:

##### Directory Navigation & View
- `lib/presentation/directory_view/directory_view_store.dart`: Core store managing directory navigation, file lists, and focus
  - Implements `changeDir()`, `goToParentDir()`, and handles focus tracking
  - Manages loading files, handling errors, and maintaining file sort order
  - Tracks focused files and handles navigation between directories

- `lib/presentation/directory_view/widgets/file_list.dart`: UI component for displaying files
  - Handles rendering the list of files with proper styling
  - Includes keyboard focus management
  - Integrates with the keyboard shortcuts system

- `lib/presentation/extensions/context_extensions.dart`: BuildContext extensions for dependency access
  - Provides easy access to stores and services through context
  - Replaces Provider.of<T>(context) with context.t syntax
  - Makes code more readable and concise

##### Keyboard Navigation and Commands
- `lib/presentation/command/command_repository.dart`: Repository of all available commands
  - Defines commands with IDs, labels, and descriptions
  - Provides command search functionality

- `lib/presentation/command/command_dispatcher.dart`: Executes commands based on context
  - Dispatches commands to appropriate handlers
  - Checks if commands are applicable in the current context

- `lib/presentation/command/keybind_manager.dart`: Manages keyboard shortcuts
  - Maps keyboard shortcuts to commands
  - Handles context-aware keybinding activation
  - Uses VSCode-like 'when' expressions for context-specific shortcuts
  - Maintains a map of CommandContext to applicable keybinds for efficient lookup

- `lib/presentation/command/command_palette_widget.dart`: UI for command execution
  - Provides searchable command palette (Ctrl+Shift+P)
  - Shows keyboard shortcuts for commands
  - Filters commands based on search query

##### Pane Management
- `lib/presentation/pane/all_panes_store.dart`: Manages dual-pane interface
  - Tracks left and right panes
  - Handles source/target pane switching

- `lib/presentation/pane/pane_store.dart`: Individual pane state management
  - Manages tabs within a pane
  - Handles history tracking

##### File Operations
- `lib/app/domain/file.dart`: File model with core properties and methods
  - Defines file attributes, paths, and type information
  - Includes helper methods for path manipulation

- `lib/app/domain/path.dart`: Path manipulation and representation
  - Handles platform-specific path operations
  - Provides navigation between paths (parent, children, etc.)

##### File Renaming
- `lib/presentation/rename/rename_store.dart`: Manages file rename operations
  - Tracks the file being renamed and the new file name
  - Provides validation for file names
  - Handles accepting and canceling rename operations

- `lib/presentation/rename/direct_rename_field.dart`: UI component for in-place renaming
  - Provides a text field for editing file names with a visible border
  - Handles keyboard events (Enter to accept, Escape to cancel)
  - Shows validation errors as tooltips
  - Automatically selects name without extension for files
  - Ensures keyboard events are handled properly without interference from global handlers

### Legacy Implementation (Electron/TypeScript)

#### Core Directories:
- `legacy/renderer/`: Main UI and business logic
  - `legacy/renderer/components/`: React UI components
  - `legacy/renderer/fileview/`: File viewing and manipulation logic
  - `legacy/renderer/filesystem/`: File system operations
  - `legacy/renderer/history/`: Navigation history tracking
  - `legacy/renderer/task/`: Task queue implementation

#### Key Files:

##### Directory Navigation & View
- `legacy/renderer/fileview/store/DirectoryViewStore.ts`: Core store for directory navigation
  - Implements directory change handling and focus management
  - Handles file listing and selection
  - Reference for Flutter implementation patterns

- `legacy/renderer/components/DirectoryView/DirectoryView.tsx`: UI component for directory view
  - React component for rendering file lists
  - Integrates with directory store

- `legacy/renderer/components/FileList/FileList.tsx`: File list component
  - Handles rendering files with proper styling
  - Manages keyboard focus

##### Pane Management
- `legacy/renderer/fileview/store/PaneStore.ts`: Pane state management
  - Manages tab state and pane focus
  - Handles history tracking within panes

## Navigation Patterns

### Directory Navigation
- First-time directory visits: Focus on parent directory entry (..)
- Revisiting directories: Restore previous focused file
- Parent navigation: Select child directory in parent view

### Focus Management
- Tab key: Switch between source and target panes
- Arrow keys: Move focus up/down in file list
- Enter: Execute focused file (navigate into directory or open file)
- Backspace: Navigate to parent directory

## Implementation Reference Guide

When implementing new features:
1. Check the legacy implementation first for reference
2. Follow the MobX pattern for state management:
   - Use `@observable` for state properties
   - Use `@action` for methods that modify state
   - Use `@computed` for derived state
   - Use `Observer` widgets to react to state changes
3. Separate presentation logic from business logic
4. Support keyboard navigation for all operations
5. Ensure proper error handling and user feedback
6. Use hot-reload for testing UI changes rather than restarting the application

## File Structure for Common Tasks

### Adding New Keyboard Shortcuts
1. Define command in `lib/presentation/command/command_repository.dart`
2. Add keybinding in `lib/presentation/command/keybind_repository.dart` or in default_keybindings.json
3. Implement command handling in `lib/presentation/command/command_dispatcher.dart`
4. Ensure proper context handling for when the command should be active

### Modifying File Navigation
1. Update `DirectoryViewStore.changeDir()` or related methods
2. Ensure proper focus handling with history integration
3. Reference legacy implementation in `legacy/renderer/fileview/store/DirectoryViewStore.ts`

### Changing UI Components
1. Modify widget implementation in `lib/presentation/`
2. Ensure keyboard focus handling is maintained
3. Update store integration as needed

### Implementing File Operations
1. Define the operation in `lib/app/task/task.dart`
2. Implement processing logic in `lib/app/task/task_processor.dart`
3. Connect to UI through appropriate store methods

### Modifying Rename Functionality
1. Update validation logic in `lib/presentation/rename/rename_store.dart`
2. Modify UI behavior in `lib/presentation/rename/direct_rename_field.dart`
3. Ensure proper focus handling and keyboard navigation
4. Provide immediate visual feedback for validation errors
5. Make sure rename operations are visible in both panes when they show the same directory
6. Ensure the rename field has visual prominence with a clear border