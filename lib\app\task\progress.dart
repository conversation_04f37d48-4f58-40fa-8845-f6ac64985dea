import 'package:mobx/mobx.dart';

import '../../infra/mobx/debounced_computed_annotation.dart';
import '../../infra/utils/speed_calculator.dart';
import '../../infra/utils/zip_with_prev.dart';
import '../domain/constants.dart';

part '.gen/progress.g.dart';

const int debounceMillis = 300;

/// The status of a progress operation
enum ProgressStatus {
  initialized,
  running,
  finished,
}

/// Represents the progress of a file operation
class Progress extends ProgressBase with _$Progress {
  Progress({
    super.bytesTotal,
    super.filesTotal,
    super.name,
  });
}

abstract class ProgressBase with Store {
  ProgressBase({
    this.bytesTotal = 0,
    this.filesTotal = 0,
    this.name,
  });

  /// The current status of the operation
  @observable
  ProgressStatus status = ProgressStatus.initialized;

  /// The runtime of the operation in milliseconds
  @observable
  int runtime = 0;

  /// The total number of bytes to process
  @observable
  int bytesTotal = 0;

  /// The number of bytes processed so far
  @observable
  int bytesProcessed = 0;

  /// The total number of files to process
  @observable
  int filesTotal = 0;

  /// The number of files processed so far
  @observable
  int filesProcessed = 0;

  /// The error that occurred, if any
  @observable
  Object? error;

  /// Parent progress to report to
  Progress? parent;

  /// Speed calculator
  final SpeedCalculator _speedCalculator = SpeedCalculator();

  /// The name of this progress tracker
  final String? name;

  bool get isQuick => filesTotal == 1 && bytesTotal <= quickFileProgressThreshold;

  /// The debounced total number of bytes to process
  @DebouncedComputed(debounceMillis: debounceMillis)
  int get bytesTotalDebounced => bytesTotal;

  /// The debounced number of bytes processed so far
  @DebouncedComputed(debounceMillis: debounceMillis)
  int get bytesProcessedDebounced => bytesProcessed;

  /// The percentage of byte completion (0-100)
  @computed
  double get byteProgress => bytesTotal > 0 ? (bytesProcessed / bytesTotal * 100).clamp(0, 100) : 0;

  /// The debounced percentage of byte completion (0-100)
  @DebouncedComputed(debounceMillis: debounceMillis)
  double get byteProgressDebounced => byteProgress;

  /// The debounced total number of files to process
  @DebouncedComputed(debounceMillis: debounceMillis)
  int get filesTotalDebounced => filesTotal;

  /// The debounced number of files processed so far
  @DebouncedComputed(debounceMillis: debounceMillis)
  int get filesProcessedDebounced => filesProcessed;

  /// The percentage of file completion (0-100)
  @computed
  double get fileProgress => filesTotal > 0 ? (filesProcessed / filesTotal * 100).clamp(0, 100) : 0;

  /// The debounced percentage of file completion (0-100)
  @DebouncedComputed(debounceMillis: debounceMillis)
  double get fileProgressDebounced => fileProgress;

  /// The debounced bytes per second processing rate
  @DebouncedComputed(debounceMillis: debounceMillis)
  double get bytesPerSecondDebounced {
    // Use the delta between debounced values to calculate speed
    final delta = bytesProcessedDeltaDebounced;
    // Only count positive progress
    final positiveDelta = delta > 0 ? delta : 0;
    return _speedCalculator.calculate(positiveDelta);
  }

  /// The debounced delta of bytes processed since the last update
  @DebouncedComputed(debounceMillis: debounceMillis)
  int get bytesProcessedDeltaDebounced {
    final values = createZipWithPrev<int>(() => bytesProcessedDebounced).value;
    final current = values[0];
    final previous = values[1];
    return current - previous;
  }

  /// Estimated time to completion in milliseconds
  @computed
  int get eta {
    return bytesTotal > 0 && bytesPerSecondDebounced > 0 ? ((bytesTotal - bytesProcessed) * 1000) ~/ bytesPerSecondDebounced : 0;
  }

  /// Debounced estimated time to completion in milliseconds
  @DebouncedComputed(debounceMillis: debounceMillis)
  int get etaDebounced => eta;

  /// Whether the operation is running
  bool get isRunning => status == ProgressStatus.running;

  /// Whether the operation has finished successfully
  bool get isFinished => status == ProgressStatus.finished;

  /// Starts the operation
  @action
  void start() {
    subtractBytesProcessed(bytesProcessed);
    subtractFilesProcessed(filesProcessed);
    clearError();
    status = ProgressStatus.running;
  }

  /// Subtracts the given number of bytes from the processed count
  @action
  void subtractBytesProcessed(int subtract) {
    if (subtract > 0) {
      bytesProcessed -= subtract;
      parent?.subtractBytesProcessed(subtract);
    }
  }

  /// Subtracts the given number of files from the processed count
  @action
  void subtractFilesProcessed(int subtract) {
    if (subtract > 0) {
      filesProcessed -= subtract;
      parent?.subtractFilesProcessed(subtract);
    }
  }

  /// Marks the operation as finished
  @action
  void finish({int addFilesProcessed = 0, int addBytesProcessed = 0}) {
    status = ProgressStatus.finished;
    if (addFilesProcessed > 0) {
      this.addFilesProcessed(addFilesProcessed);
    }
    if (addBytesProcessed > 0) {
      this.addBytesProcessed(addBytesProcessed);
    }
  }

  /// Sets the total number of bytes to process
  @action
  void setBytesTotal(int total) {
    bytesTotal = total;
  }

  /// Adds the given number of bytes to the processed count
  @action
  void addBytesProcessed(int bytes) {
    bytesProcessed += bytes;
    parent?.addBytesProcessed(bytes);
  }

  /// Sets the total number of files to process
  @action
  void setFilesTotal(int total) {
    filesTotal = total;
  }

  /// Adds the given number of files to the processed count
  @action
  void addFilesProcessed(int files) {
    filesProcessed += files;
    parent?.addFilesProcessed(files);
  }

  @action
  void setStatus(ProgressStatus status, [Object? error]) {
    this.status = status;
    if (error != null) {
      this.error = error;
    }
  }

  /// Clears any error
  @action
  void clearError() {
    error = null;
  }

  /// Adds the given number of bytes to the total count
  @action
  void addBytesTotal(int bytesTotal) {
    this.bytesTotal += bytesTotal;
    parent?.addBytesTotal(bytesTotal);
  }

  /// Adds the given number of files to the total count
  @action
  void addFilesTotal(int filesTotal) {
    this.filesTotal += filesTotal;
    parent?.addFilesTotal(filesTotal);
  }

  /// Adds bytes to total and files processed in a single operation
  @action
  void addBytesTotalFilesProcessed(int bytesTotal, int filesProcessed) {
    this.bytesTotal += bytesTotal;
    this.filesProcessed += filesProcessed;
    parent?.addBytesTotalFilesProcessed(bytesTotal, filesProcessed);
  }

  /// Adds the given amount to the runtime
  @action
  void addRuntime(int amount) {
    runtime += amount;
  }

  /// Adds processed bytes and files in a single operation
  @action
  void addProcessed(int bytesProcessed, int filesProcessed) {
    this.bytesProcessed += bytesProcessed;
    this.filesProcessed += filesProcessed;
    parent?.addProcessed(bytesProcessed, filesProcessed);
  }

  /// Adds totals from another progress object
  @action
  void addTotals(Progress progress) {
    bytesTotal += progress.bytesTotal;
    filesTotal += progress.filesTotal;
    parent?.addTotals(progress);
  }

  /// Subtracts totals from another progress object
  @action
  void subtractProgress(Progress progress) {
    bytesTotal -= progress.bytesTotal;
    filesTotal -= progress.filesTotal;
    parent?.subtractProgress(progress);
  }

  /// Resets the speed calculator
  void resetSpeed() {
    _speedCalculator.reset();
  }

  /// Resets the progress to initial state
  @action
  void reset() {
    status = ProgressStatus.initialized;
    runtime = 0;
    bytesTotal = 0;
    bytesProcessed = 0;
    filesTotal = 0;
    filesProcessed = 0;
    error = null;
    resetSpeed();
  }

  /// Sets the parent progress to report to
  void setParent(Progress parent) {
    this.parent = parent;
  }

  /// Adds a child progress to this progress
  @action
  void addChild(ProgressBase child) {
    if (child is Progress) {
      child.parent = this as Progress;

      // Update totals
      addBytesTotal(child.bytesTotal);
      addFilesTotal(child.filesTotal);
    }
  }

  @override
  String toString() {
    return '[${status.name}]';
  }
}
