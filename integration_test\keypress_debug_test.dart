import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:qfiler/presentation/domain/side.dart';

import 'base_integration_test.dart';

class KeypressDebugTest extends BaseIntegrationTest with FocusTestMixin {
  
}

void main() {
  BaseIntegrationTest.setUpIntegrationTests();

  testWidgets('Debug first keypress absorption issue', (WidgetTester tester) async {
    final test = KeypressDebugTest();
    
    try {
      await test.setUp(testName: 'keypress_debug');
      await test.pumpApp(tester);

      test.debugCurrentState(label: 'INITIAL');

      // Verify left pane is source
      expect(test.rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

      // Wait for focus to settle
      await test.waitForFocusToSettle(tester);
      
      test.debugCurrentState(label: 'AFTER_SETTLE');

      // Test multiple arrow key presses to see which one works
      final initialIndex = test.rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;
      
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] Initial focused index: $initialIndex');
        print('[KEYPRESS_DEBUG] File count: ${test.rootStore.allPanesStore.left.directoryViewStore.files.length}');
      }

      // First arrow key press
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] Sending FIRST arrow down...');
      }
      await test.sendKeyAndWait(tester, LogicalKeyboardKey.arrowDown, waitTime: const Duration(milliseconds: 500));
      
      final afterFirstPress = test.rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] After FIRST arrow down: $afterFirstPress (changed: ${afterFirstPress != initialIndex})');
      }

      // Second arrow key press
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] Sending SECOND arrow down...');
      }
      await test.sendKeyAndWait(tester, LogicalKeyboardKey.arrowDown, waitTime: const Duration(milliseconds: 500));
      
      final afterSecondPress = test.rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] After SECOND arrow down: $afterSecondPress (changed: ${afterSecondPress != afterFirstPress})');
      }

      // Third arrow key press
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] Sending THIRD arrow down...');
      }
      await test.sendKeyAndWait(tester, LogicalKeyboardKey.arrowDown, waitTime: const Duration(milliseconds: 500));
      
      final afterThirdPress = test.rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] After THIRD arrow down: $afterThirdPress (changed: ${afterThirdPress != afterSecondPress})');
      }

      // Test Tab switching
      if (kDebugMode) {
        print('[KEYPRESS_DEBUG] Testing Tab key...');
      }
      await test.verifyTabSwitching(tester, debugLabel: 'TAB_TEST');

    } finally {
      await test.tearDown();
    }
  });

  testWidgets('Test with manual focus request', (WidgetTester tester) async {
    final test = KeypressDebugTest();
    
    try {
      await test.setUp(testName: 'manual_focus_test');
      await test.pumpApp(tester);

      // Manually request focus after app is loaded
      if (kDebugMode) {
        print('[MANUAL_FOCUS] Manually requesting focus for left FileList...');
      }
      
      // Wait longer for everything to settle
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Try to find and manually focus the FileList
      // This is a workaround to see if manual focus helps
      await tester.pump(const Duration(milliseconds: 100));
      
      test.debugCurrentState(label: 'AFTER_MANUAL_FOCUS');

      // Now test arrow keys
      await test.verifyArrowKeysWork(tester, isLeftPane: true, debugLabel: 'MANUAL_FOCUS_TEST');

    } finally {
      await test.tearDown();
    }
  });
}
