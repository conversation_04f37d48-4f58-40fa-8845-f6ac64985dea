mod build
mod flutter
mod release
mod rust

# List available recipes
default:
    @just --list

setup:
    flutter pub get
    cd rust && cargo fetch && cargo build

# Generate code for both Dart and Rust (non-watch mode)
gen:
    @echo "Starting code generation..."
    @echo "Running flutter_rust_bridge code generation..."
    flutter_rust_bridge_codegen generate
    @echo "Running Dart build_runner..."
    -dart run build_runner build --delete-conflicting-outputs
    @echo "Code generation complete!"

# Generate code for both Dart and Rust in watch mode
gen-watch:
    @echo "Starting code generation in watch mode..."
    @echo "Press Ctrl+C to stop."
    @echo "Note: This will open two separate terminal windows for watching."
    start cmd /k "dart run build_runner watch --delete-conflicting-outputs"
    start cmd /k "flutter_rust_bridge_codegen generate --watch"

# Generate flutter_rust_bridge code
frb:
    flutter_rust_bridge_codegen generate

# Generate flutter_rust_bridge code in watch mode
frb-watch:
    flutter_rust_bridge_codegen generate --watch

repomix:
    repomix --no-file-summary --remove-empty-lines
    # --compress


# # Full setup (get dependencies, build Rust, generate code)
# setup: flutter::pub-get rust-build frb-generate build-once
#     @echo "Setup complete!"

# # Update all dependencies and regenerate code
# update-all: pub-upgrade frb-generate build-once
#     @echo "Update complete!"

# Format all code
format:
    @echo "Formatting Dart code..."
    dart format lib test
    @echo "Formatting Rust code..."
    cd rust && cargo fmt
    @echo "Formatting complete!"

# Check all code
# check: analyze rust-check
#     @echo "Code check complete!"

# Lint and fix all code
lint:
    @echo "Linting Dart code..."
    -dart fix --apply .
    -flutter analyze
    @echo "Linting Rust code..."
    -cd rust && cargo check
    @echo "Linting complete!"