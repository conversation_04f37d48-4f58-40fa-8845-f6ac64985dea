import 'package:mobx/mobx.dart';

import '../app/domain/path.dart';
import '../app/domain/user_choice.dart';
import '../app/file_system/file_repository.dart';
import '../app/file_system/file_system.dart';
import '../app/file_system/real/path_repository.dart';
import '../app/file_system/rust/rust_file_system_adapter.dart';
import '../app/notification/notification_repository.dart';
import '../app/notification/notification_store.dart';
import '../app/persistence/data_repository.dart';
import '../app/settings/settings_repository.dart';
import '../app/task/file_op_executor.dart';
import '../app/task/pending_file_store.dart';
import '../app/task/task.dart';
import '../app/task/task_planner.dart';
import '../app/task/task_store.dart';
import 'command/command_context_repository.dart';
import 'command/command_dispatcher.dart';
import 'command/command_repository.dart';
import 'command/keybind_manager.dart';
import 'command/keybind_repository.dart';
import 'directory_view/directory_view_store.dart';
import 'domain/side.dart';
import 'history/history_store.dart';
import 'pane/all_panes_store.dart';
import 'pane/pane_store.dart';
import 'rename/rename_store.dart';

part '.gen/root_store.g.dart';

class RootStore extends RootStoreBase with _$RootStore {
  RootStore({
    required super.fileRepository,
    required super.allPanesStore,
    required super.settingsRepository,
    required super.commandRepository,
    required super.keyBindRepository,
    required super.commandContextRepository,
    required super.keyBindManager,
    required super.commandDispatcher,
    required super.renameStore,
  });

  static Future<RootStore> create() async {
    final fileSystem = RustFileSystemAdapter();
    final pendingOperationRepository = PendingOperationRepository();
    final fileRepository = FileRepository(PathRepository(fileSystem), pendingOperationRepository);

    final notificationRepository = NotificationRepository();
    final notificationStore = NotificationStore();
    final executor = FileOpExecutor(
      fileSystem,
      pendingOperationRepository,
      _createShowConfirmOverwriteDialog(),
      _createShowFileOpErrorDialog(),
    );

    final jobPlanner = JobPlanner(fileRepository: fileRepository, pendingOperationRepository: pendingOperationRepository);

    final jobStore = JobStore(executor, notificationStore, jobPlanner);

    final dataRepository = createDataRepository(fileSystem, notificationRepository);
    final settingsRepository = await SettingsRepository.create(dataRepository);
    final commandRepository = CommandRepository.create();
    final keyBindRepository = await KeyBindRepository.create(dataRepository);
    final commandContextRepository = CommandContextRepository();

    final renameStore = RenameStore(jobStore, notificationStore, commandContextRepository);
    // Set up the rename sync listener to synchronize rename operations across panes
    renameStore.setupRenameSyncListener();

    final left = await createPaneStore(settingsRepository, fileRepository, dataRepository, notificationStore, renameStore, Side.left);
    final right = await createPaneStore(settingsRepository, fileRepository, dataRepository, notificationStore, renameStore, Side.right);

    // Create AllPanesStore first
    final allPanesStore = AllPanesStore(left: left, right: right);

    // Create CommandDispatcher
    final commandDispatcher = CommandDispatcher(
      allPanesStore: allPanesStore,
      commandContextRepository: commandContextRepository,
      jobStore: jobStore,
      notificationStore: notificationStore,
    );

    // Create KeyBindManager with the repositories
    final keyBindManager = KeyBindManager(
      keyBindRepository,
      commandRepository,
      commandContextRepository,
      commandDispatcher,
    );

    // Set the AllPanesStore reference in the pane stores
    left.setAllPanesStore(allPanesStore);
    right.setAllPanesStore(allPanesStore);

    return RootStore(
      fileRepository: fileRepository,
      allPanesStore: allPanesStore,
      settingsRepository: settingsRepository,
      commandRepository: commandRepository,
      keyBindRepository: keyBindRepository,
      commandContextRepository: commandContextRepository,
      keyBindManager: keyBindManager,
      commandDispatcher: commandDispatcher,
      renameStore: renameStore,
    );
  }

  static DataRepository createDataRepository(FileSystem fileSystem, NotificationRepository notificationRepository) {
    // TODO: Take userDataDir from somewhere else
    return DataRepository(DataRepositoryConfig(userDataDir: RawPath.currentWorkingDir.child("data")), fileSystem, notificationRepository);
  }

  static Future<PaneStore> createPaneStore(
    SettingsRepository settingsRepository,
    FileRepository fileRepository,
    DataRepository dataRepository,
    NotificationStore notificationStore,
    RenameStore renameStore,
    Side side,
  ) async {
    final historyStore = await HistoryStore.create(dataRepository, side);

    final directoryViewStore = DirectoryViewStore(historyStore, fileRepository, renameStore, side.name);
    final paneStore = PaneStore(historyStore, directoryViewStore, side);
    return paneStore;
  }

  // Helper method to create ShowConfirmOverwriteDialog implementation
  static ShowConfirmOverwriteDialog _createShowConfirmOverwriteDialog() {
    return _SimpleShowConfirmOverwriteDialog();
  }

  // Helper method to create ShowFileOpErrorDialog implementation
  static ShowFileOpErrorDialog _createShowFileOpErrorDialog() {
    return _SimpleShowFileOpErrorDialog();
  }
}

// Simple implementation of ShowConfirmOverwriteDialog
class _SimpleShowConfirmOverwriteDialog implements ShowConfirmOverwriteDialog {
  @override
  Future<OverwriteChoice> call({required Operation op, required bool selectOverwriteAll}) async {
    // For now, always cancel
    return OverwriteChoice(overwriteMode: OverwriteMode.cancel, all: selectOverwriteAll);
  }
}

// Simple implementation of ShowFileOpErrorDialog
class _SimpleShowFileOpErrorDialog implements ShowFileOpErrorDialog {
  @override
  Future<OperationErrorChoice> call({required Operation op, required Object error}) async {
    // For now, always cancel
    return OperationErrorChoice(mode: OperationErrorMode.cancel, all: true);
  }
}

abstract class RootStoreBase with Store {
  final FileRepository fileRepository;
  final AllPanesStore allPanesStore;
  final SettingsRepository settingsRepository;

  final CommandRepository commandRepository;
  final KeyBindRepository keyBindRepository;
  final CommandContextRepository commandContextRepository;
  final KeyBindManager keyBindManager;
  final CommandDispatcher commandDispatcher;
  final RenameStore renameStore;

  const RootStoreBase({
    required this.fileRepository,
    required this.allPanesStore,
    required this.settingsRepository,
    required this.commandRepository,
    required this.keyBindRepository,
    required this.commandContextRepository,
    required this.keyBindManager,
    required this.commandDispatcher,
    required this.renameStore,
  });
}
