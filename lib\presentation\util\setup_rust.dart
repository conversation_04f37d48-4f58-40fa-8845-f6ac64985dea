import 'package:flutter/foundation.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:logging/logging.dart';

import '../../app/util/log_colorizer.dart';
import '../../rust/.gen/api/logging.dart';
import '../../rust/.gen/frb_generated.dart';
import '../../rust/.gen/helpers/log_utils.dart';

Future<void> setupRust({String? dylibPath}) async {
  await RustLib.init(externalLibrary: dylibPath != null ? ExternalLibrary.open(dylibPath) : null);

  if (kDebugMode) {
    setupLogging().listen((logEntry) {
      final record = LogRecord(
        switch (logEntry.logLevel) {
          LogLevel.trace => Level.FINEST,
          LogLevel.debug => Level.FINE,
          LogLevel.info => Level.INFO,
          LogLevel.warn => Level.WARNING,
          LogLevel.error => Level.SEVERE,
        },
        logEntry.msg,
        logEntry.lbl,
      );
      String formattedMessage = LogColorizer.colorizeRecord(record);
      debugPrint(formattedMessage);
    });
  }
}