import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../app/util/log_utils.dart';
import '../util/context_extensions.dart';

/// Widget that handles keyboard events and dispatches commands
class KeyboardHandler extends HookWidget {
  const KeyboardHandler({
    super.key,
    required this.child,
    required this.debugLabel,
    this.focusNode,
    this.autofocus = false,
    this.canRequestFocus = true,
    this.consumeIfUnhandled = false,
  });

  final String debugLabel;
  final Widget child;
  final FocusNode? focusNode;
  final bool autofocus;
  final bool canRequestFocus;
  final bool consumeIfUnhandled;

  @override
  Widget build(BuildContext context) {
    KeyEventResult handleKeyEvent(FocusNode node, KeyEvent event) {
      if (event is! KeyDownEvent) return KeyEventResult.ignored;
      if (kDebugMode) {
        logger.fine('[$debugLabel] Handling key event: ${event.logicalKey.keyLabel}');
      }

      final result = context.keyBindManager.handle(event);
      if (result != KeyEventResult.ignored) {
        return result;
      }
      if (consumeIfUnhandled) {
        return KeyEventResult.handled;
      }
      return KeyEventResult.ignored;
    }

    return Focus(
      autofocus: autofocus,
      canRequestFocus: canRequestFocus,
      onKeyEvent: handleKeyEvent,
      debugLabel: debugLabel,
      focusNode: focusNode,
      child: child,
    );
  }

  static final logger = loggerFor(KeyboardHandler, Level.INFO);
}
