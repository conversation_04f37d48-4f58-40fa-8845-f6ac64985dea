import 'dart:math';

import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';

void addExtraScrollSpeed(ScrollController controller, {required double extraScrollSpeed}) {
  if (!controller.hasClients) {
    return;
  }

  ScrollDirection scrollDirection = controller.position.userScrollDirection;
  if (scrollDirection != ScrollDirection.idle) {
    double scrollEnd = controller.offset + (scrollDirection == ScrollDirection.reverse ? extraScrollSpeed : -extraScrollSpeed);
    scrollEnd = min(controller.position.maxScrollExtent, max(controller.position.minScrollExtent, scrollEnd));
    controller.jumpTo(scrollEnd);
  }
}
