# Project Overview
- QFiler is a dual-pane file explorer built with Flutter for desktop platforms
- Primary focus is on Windows platform with future multi-platform support
- The application supports extensive keyboard and mouse navigation
- File operations can be queued to operate asynchronously with preview functionality
- This is a rewrite of an existing Electron application (found in the `legacy` directory)

# Code Structure
- Main Flutter code is in the `lib` directory
- UI components and presentation logic are in `lib/presentation`
- Business logic and domain models are in `lib/app`
- Legacy implementation in Electron/TypeScript/React is in the `legacy` directory
- Most UI components and business logic can be found in `legacy/renderer`

# Core Components
- The dual-pane interface is managed by `AllPanesStore` with left and right panes
- Each pane is represented by a `PaneStore` that manages its state
- Directory navigation and file operations are handled in `lib/app/file_system`
- User settings and preferences are managed in `lib/app/settings`
- File operations are managed through an asynchronous queue in `lib/app/task`

# Development Guidelines
- Use Flutter best practices for desktop application development
- Follow MobX patterns for state management
- Use flutter_hooks and HookWidget for stateful components
- Access dependencies through context extensions instead of Provider.of
- Create instances in the root store and inject them where needed
- NEVER use static instances or singletons with static accessors (e.g., RootStore.instance)
- Always pass dependencies through constructors
- Use DataRepository for reading and writing config files
- Follow naming convention: Stores hold UI state, Repositories hold business logic state (not tied to UI)
- All BuildContext extensions should be centralized in lib/presentation/extensions/context_extensions.dart
- Use logging at various levels instead of debug prints
  - Protect logging with if (kDebugMode) checks
  - Use info level for interesting notices
  - Use fine/finer/finest levels for more verbose logging
  - Use warning/severe for errors
  - When logging in the context of a directory, prefix log messages with the directory path
  - When logging file operations, include the file path in square brackets
  - It's ok to use debugPrint for temporary prints while debugging, but anything more permanent should go through logging
- Do not refactor code that you did not write or do not understand unless explicitly requested
- Reference the legacy implementation in `legacy` when implementing features
- Most of the logic already exists and works in the legacy implementation
- New features should be rewritten from TypeScript/React to Dart/Flutter

### Legacy Implementation (Electron/TypeScript)

#### Core Directories:
- `legacy/renderer/`: Main UI and business logic
  - `legacy/renderer/components/`: React UI components
  - `legacy/renderer/fileview/`: File viewing and manipulation logic
  - `legacy/renderer/filesystem/`: File system operations
  - `legacy/renderer/history/`: Navigation history tracking
  - `legacy/renderer/task/`: Task queue implementation

#### Key Files:

##### Directory Navigation & View
- `legacy/renderer/fileview/store/DirectoryViewStore.ts`: Core store for directory navigation
  - Implements directory change handling and focus management
  - Handles file listing and selection
  - Reference for Flutter implementation patterns

- `legacy/renderer/components/DirectoryView/DirectoryView.tsx`: UI component for directory view
  - React component for rendering file lists
  - Integrates with directory store

- `legacy/renderer/components/FileList/FileList.tsx`: File list component
  - Handles rendering files with proper styling
  - Manages keyboard focus

##### Pane Management
- `legacy/renderer/fileview/store/PaneStore.ts`: Pane state management
  - Manages tab state and pane focus
  - Handles history tracking within panes

## Implementation Reference Guide

When implementing new features:
1. Check the legacy implementation first for reference
2. Follow the MobX pattern for state management:
   - Use `@observable` for state properties
   - Use `@action` for methods that modify state
   - Use `@computed` for derived state
   - Use `Observer` widgets to react to state changes