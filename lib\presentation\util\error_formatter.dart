import '../../../rust/.gen/api/error.dart';

/// Utility class for formatting error messages in a user-friendly way
class ErrorFormatter {
  /// Formats an error for display to the user
  ///
  /// For FileSystemError, extracts only the meaningful message part.
  /// For other exceptions, uses the standard toString() method.
  static String formatError(Object error) {
    if (error is FileSystemError) {
      return switch (error) {
        FileSystemError_NotFound(:final message) => message,
        FileSystemError_PermissionDenied(:final message) => message,
        FileSystemError_ConnectionRefused(:final message) => message,
        FileSystemError_ConnectionReset(:final message) => message,
        FileSystemError_HostUnreachable(:final message) => message,
        FileSystemError_NetworkUnreachable(:final message) => message,
        FileSystemError_ConnectionAborted(:final message) => message,
        FileSystemError_NotConnected(:final message) => message,
        FileSystemError_AddrInUse(:final message) => message,
        FileSystemError_AddrNotAvailable(:final message) => message,
        FileSystemError_NetworkDown(:final message) => message,
        FileSystemError_BrokenPipe(:final message) => message,
        FileSystemError_AlreadyExists(:final message) => message,
        FileSystemError_WouldBlock(:final message) => message,
        FileSystemError_NotADirectory(:final message) => message,
        FileSystemError_IsADirectory(:final message) => message,
        FileSystemError_DirectoryNotEmpty(:final message) => message,
        FileSystemError_ReadOnlyFilesystem(:final message) => message,
        FileSystemError_FilesystemLoop(:final message) => message,
        FileSystemError_StaleNetworkFileHandle(:final message) => message,
        FileSystemError_InvalidInput(:final message) => message,
        FileSystemError_InvalidData(:final message) => message,
        FileSystemError_TimedOut(:final message) => message,
        FileSystemError_WriteZero(:final message) => message,
        FileSystemError_StorageFull(:final message) => message,
        FileSystemError_NotSeekable(:final message) => message,
        FileSystemError_FileTooLarge(:final message) => message,
        FileSystemError_ResourceBusy(:final message) => message,
        FileSystemError_ExecutableFileBusy(:final message) => message,
        FileSystemError_Deadlock(:final message) => message,
        FileSystemError_CrossesDevices(:final message) => message,
        FileSystemError_TooManyLinks(:final message) => message,
        FileSystemError_InvalidFilename(:final message) => message,
        FileSystemError_ArgumentListTooLong(:final message) => message,
        FileSystemError_Interrupted(:final message) => message,
        FileSystemError_Unsupported(:final message) => message,
        FileSystemError_UnexpectedEof(:final message) => message,
        FileSystemError_OutOfMemory(:final message) => message,
        FileSystemError_InProgress(:final message) => message,
        FileSystemError_QuotaExceeded(:final message) => message,
        FileSystemError_Other(:final message) => message,
        FileSystemError_Cancelled() => "Cancelled",
        FileSystemError_Uncategorized(:final message) => message,
        // These two have field0 instead of message
        FileSystemError_OperationControlError(:final message) => message,
        FileSystemError_Unknown(:final field0) => field0,
      };
    }

    return error.toString();
  }
}