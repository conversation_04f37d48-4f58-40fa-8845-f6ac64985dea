# List available recipes
default:
    @just --list build

# Run Flutter app with Rust automatically built
run: rust
    flutter run

# Clean Rust build artifacts
clean-rust:
    cd rust && cargo clean

# Clean Flutter build artifacts
clean-flutter:
    flutter clean

# Full clean (Flutter and Rust)
clean: clean-flutter clean-rust
    rm -rf .dart_tool

# Build Rust library for current platform
rust:
    cd rust && cargo build

# Build for all platforms (requires appropriate setup)
all: rust
    flutter build windows
    flutter build macos
    flutter build linux
    flutter build apk
    flutter build ios --no-codesign

# Build for Windows with Rust
windows:
    flutter build windows

# Build for macOS with Rust
macos: rust
    flutter build macos

# Build for Linux with Rust
linux: rust
    flutter build linux

# Build for Android with Rust
android: rust
    flutter build apk

# Build for iOS with Rust
ios: rust
    flutter build ios --no-codesign
