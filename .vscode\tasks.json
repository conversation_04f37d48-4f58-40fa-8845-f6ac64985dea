{
  "version": "2.0.0",
  "tasks": [
    // build runner's build task
    {
      "label": "build_runner-build", // this label will be useful later
      "type": "flutter",
      "command": "flutter",
      "args": [
        "pub",
        "run",
        "build_runner",
        "build",
        "lib/", // only scan lib dir
        "--delete-conflicting-outputs", // do not ask to delete conflicting files
      ],
      "presentation": {
        "echo": true, // write logs
        "reveal": "silent", // i don't wanna see the terminal window
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false // i wanna see logs when I want
      },
      "problemMatcher": [
        "$dart-build_runner"
      ],
      "group": "build",
      "detail": ""
    },
    // build runner's watch task
    {
      "label": "build_runner-watch",
      "type": "flutter",
      "command": "flutter",
      "args": [
        "pub",
        "run",
        "build_runner",
        "watch", // the same stuff but watching this time
        "lib/",
        "--delete-conflicting-outputs",
      ],
      "presentation": {
        "echo": true,
        "reveal": "always", // show output for watch tasks
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [
        "$dart-build_runner"
      ],
      "group": "build",
      "detail": ""
    },
  ]
}