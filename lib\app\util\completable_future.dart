import 'dart:async';

import 'package:mobx/mobx.dart';

part '.gen/completable_future.g.dart';

class CompletableFuture<T> = CompletableFutureBase<T> with _$CompletableFuture;

abstract class CompletableFutureBase<T> with Store implements Future<T> {
  late Future<T> _future;

  @readonly
  bool _isCompleted = false;

  Completer<T> _completer = Completer();

  CompletableFutureBase() {
    _future = _completer.future;
  }

  /// If the future was already completed, will be recreated internally as pending.
  /// Any listeners of the old future will not be notified.
  @action
  void setPending() {
    if (_completer.isCompleted) {
      _completer = Completer();
      _future = _completer.future;
      _isCompleted = false;
    }
  }

  @action
  void complete(FutureOr<T> value) {
    // Allow completing an already completed future.
    setPending();
    _completer.complete(value);
    _isCompleted = true;
  }

  @action
  void completeError(Object error, [StackTrace? stackTrace]) {
    _completer.completeError(error, stackTrace);
    _isCompleted = true;
  }

  @override
  Future<R> then<R>(FutureOr<R> Function(T value) onValue, {Function? onError}) => _future.then(onValue, onError: onError);

  @override
  Future<T> catchError(Function onError, {bool Function(Object error)? test}) => _future.catchError(onError, test: test);

  @override
  Stream<T> asStream() => _future.asStream();

  @override
  Future<T> whenComplete(FutureOr<void> Function() action) => _future.whenComplete(action);

  @override
  Future<T> timeout(Duration timeLimit, {FutureOr<T> Function()? onTimeout}) => _future.timeout(timeLimit, onTimeout: onTimeout);
}
