import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  group('Focus Tests', () {
    testWidgets('Left pane should be focused and respond to arrow keys after app loads', (WidgetTester tester) async {
      final setup = await createTestSetup(testName: 'left_pane_focus_test');

      try {
        // Pump the app
        await pumpApp(tester, setup.rootStore);

        debugCurrentState(setup.rootStore, label: 'INITIAL');

        // Verify left pane is source
        expect(setup.rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

        // Test that arrow keys work immediately (no first keypress absorption)
        await verifyArrowKeysWork(tester, setup.rootStore, isLeftPane: true, debugLabel: 'LEFT_PANE_ARROW_TEST');

      } finally {
        await cleanupTestSetup(setup);
      }
    });

    testWidgets('Tab key should switch focused pane', (WidgetTester tester) async {
      final setup = await createTestSetup(testName: 'tab_switching_test');

      try {
        // Pump the app
        await pumpApp(tester, setup.rootStore);

        debugCurrentState(setup.rootStore, label: 'INITIAL');

        // Test tab switching between panes
        await verifyTabSwitching(tester, setup.rootStore, debugLabel: 'TAB_SWITCHING_TEST');

      } finally {
        await cleanupTestSetup(setup);
      }
    });
  });
}
