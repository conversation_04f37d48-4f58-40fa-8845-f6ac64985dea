import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/root_store.dart';
import 'package:qfiler/presentation/util/setup.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  testWidgets('Simple focus test - left pane should be source and respond to keys', (WidgetTester tester) async {
    // Create temporary directories for testing
    final tempBaseDir = await io.Directory.systemTemp.createTemp('qfiler_simple_focus_test_');
    final leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
    final rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();

    try {
      // Create test files
      await io.File(p.join(leftTempDir.path, 'file1.txt')).writeAsString('File 1');
      await io.File(p.join(leftTempDir.path, 'file2.txt')).writeAsString('File 2');
      await io.File(p.join(rightTempDir.path, 'file3.txt')).writeAsString('File 3');

      // Create RootStore and set directories
      final rootStore = await RootStore.create();
      rootStore.allPanesStore.left.historyStore.addWithPath(RawPath(leftTempDir.path));
      rootStore.allPanesStore.right.historyStore.addWithPath(RawPath(rightTempDir.path));

      // Pump the app
      await tester.pumpWidget(App(rootStore));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Verify left pane is source
      expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

      // Test that arrow keys work (indicating focus is working)
      final initialIndex = rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;

      // Press down arrow
      await tester.sendKeyEvent(LogicalKeyboardKey.arrowDown);
      await tester.pumpAndSettle(const Duration(milliseconds: 300));

      final newIndex = rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex;

      if (kDebugMode) {
        print('After arrow down - Initial index: $initialIndex, New index: $newIndex');
      }

      // If there are multiple files, the index should change
      final fileCount = rootStore.allPanesStore.left.directoryViewStore.files.length;
      if (fileCount > 1) {
        expect(newIndex, isNot(equals(initialIndex)), reason: 'Arrow key should work if FileList has focus');
      }

      // Test Tab key switching
      await tester.sendKeyEvent(LogicalKeyboardKey.tab);
      await tester.pumpAndSettle(const Duration(milliseconds: 500));

      if (kDebugMode) {
        print('After Tab - Source pane: ${rootStore.allPanesStore.sourcePaneSide}');
      }

      expect(rootStore.allPanesStore.sourcePaneSide, equals(Side.right));
    } finally {
      // Clean up
      if (await tempBaseDir.exists()) {
        await tempBaseDir.delete(recursive: true);
      }
    }
  });
}
