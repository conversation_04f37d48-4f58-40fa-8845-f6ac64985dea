import 'dart:async';

/// Configuration for a buffered timeout queue
class BufferedTimeoutQueueConfig {
  /// The timeout in milliseconds before processing the queue
  final int timeoutMillis;

  /// The maximum batch size to process at once
  final int maxBatchSize;

  /// Creates a new buffered timeout queue configuration
  const BufferedTimeoutQueueConfig({
    required this.timeoutMillis,
    required this.maxBatchSize,
  });
}

/// A queue that processes items in batches after a timeout or when the batch size is reached
class BufferedTimeoutQueue<T> {
  /// The configuration for this queue
  final BufferedTimeoutQueueConfig config;

  /// The function to process a batch of items
  final void Function(List<T> batch) processBatch;

  /// The current batch of items
  final List<T> _batch = [];

  /// The timer for processing the batch after a timeout
  Timer? _timer;

  /// Creates a new buffered timeout queue
  BufferedTimeoutQueue(this.config, this.processBatch);

  /// Adds an item to the queue
  void add(T item) {
    _batch.add(item);
    _scheduleProcessing();
  }

  /// Adds multiple items to the queue
  void addAll(Iterable<T> items) {
    _batch.addAll(items);
    _scheduleProcessing();
  }

  /// Schedules processing of the batch
  void _scheduleProcessing() {
    // If the batch is full, process it immediately
    if (_batch.length >= config.maxBatchSize) {
      _processNow();
      return;
    }

    // Otherwise, schedule processing after the timeout
    _timer ??= Timer(Duration(milliseconds: config.timeoutMillis), _processNow);
  }

  /// Processes the batch immediately
  void _processNow() {
    _cancelTimer();

    if (_batch.isEmpty) {
      return;
    }

    // Process the batch
    final batchToProcess = List<T>.from(_batch);
    _batch.clear();

    processBatch(batchToProcess);
  }

  /// Cancels the timer
  void _cancelTimer() {
    _timer?.cancel();
    _timer = null;
  }

  /// Flushes the queue, processing all pending items
  void flush() {
    _processNow();
  }

  /// Disposes the queue
  void dispose() {
    _cancelTimer();
    _batch.clear();
  }
}
