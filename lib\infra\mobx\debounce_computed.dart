import 'dart:async';

import 'package:mobx/mobx.dart';

/// Creates a debounced computed value that only updates after a specified delay.
///
/// The [getter] function provides the value to be computed.
/// The [debounceDuration] specifies how long to wait after changes before recalculating.
/// An optional [name] can be provided for debugging purposes.
///
/// Returns a [Computed] value that will be recalculated with debouncing.
Computed<T> debounceComputed<T>(T Function() getter, {required Duration debounceDuration, String? name}) {
  final atom = Atom(name: name ?? 'DebounceAtom');
  T? cachedValue;
  Timer? timer;

  void clearValue() {
    cachedValue = null;
    atom.reportChanged(); // Ping subscribers to update
  }

  return Computed(() {
    if (cachedValue != null) {
      // Don't calculate until the atom pings us
      atom.reportObserved();
    } else {
      // Calculate and cache the result
      cachedValue = getter();

      // Cancel any existing timer
      timer?.cancel();

      // Batch subsequent runs for the next duration
      timer = Timer(debounceDuration, clearValue);
    }
    return cachedValue as T;
  }, name: name ?? 'DebouncedComputed');
}

/// Extension method for creating a debounced computed from a millisecond value.
///
/// This allows for a more concise syntax when specifying the debounce duration.
extension DebounceDurationExtension on int {
  /// Creates a debounced computed with this integer as the millisecond duration.
  Computed<T> debouncedComputed<T>(T Function() getter, {String? name}) {
    return debounceComputed(
      getter,
      debounceDuration: Duration(milliseconds: this),
      name: name,
    );
  }
}

/// Mixin that provides helper methods for working with debounced computed properties.
mixin DebouncedComputedMixin {
  final Map<String, Computed<dynamic>> _debouncedComputedCache = {};

  /// Gets or creates a debounced computed value for the given getter function.
  ///
  /// The [propertyName] is used as a key to cache the computed value.
  /// The [getter] function provides the value to be computed.
  /// The [debounceMillis] specifies how long to wait after changes before recalculating.
  ///
  /// Returns the value of the debounced computed property.
  T getDebouncedComputed<T>(
    String propertyName,
    T Function() getter,
    int debounceMillis,
  ) {
    final key = '_${propertyName}_debounced';

    if (!_debouncedComputedCache.containsKey(key)) {
      _debouncedComputedCache[key] = debounceComputed(
        getter,
        debounceDuration: Duration(milliseconds: debounceMillis),
        name: key,
      );
    }

    return _debouncedComputedCache[key]!.value as T;
  }

  /// Clears all cached debounced computed values.
  ///
  /// This can be useful when the object is being disposed or reset.
  void clearDebouncedComputedCache() {
    _debouncedComputedCache.clear();
  }
}
