import 'package:dartx/dartx.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../context_extensions.dart';
import 'horizontal_scroll_buttons.dart';

typedef BreadcrumbBuilder<T> = TextSpan Function(BuildContext context, T item, int index);
typedef BreadcrumbCollapsedElementBuilder<T> = TextSpan Function(BuildContext context, void Function() collapse);

class Breadcrumbs<T> extends HookWidget {
  final List<T> items;
  final int itemCount;

  final BreadcrumbBuilder<T> itemBuilder;
  final String separator;
  final String collapsedElementReplacement;
  final BreadcrumbCollapsedElementBuilder<T>? collapsedElementBuilder;

  final TextStyle? style;
  final TextAlign textAlign;

  final TextSpan _separator;

  // Either collapsedElementBuilder or collapsedElementReplacement must be set.
  Breadcrumbs({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.separator,
    this.collapsedElementReplacement = '...',
    this.collapsedElementBuilder,
    this.style,
    this.textAlign = TextAlign.left,
  })  : itemCount = items.length,
        _separator = TextSpan(
          text: separator,
          style: style,
        );

  @override
  Widget build(BuildContext context) {
    final isExpanded = useState(false);
    final canExpand = useState(false);

    void expand() {
      if (canExpand.value) {
        isExpanded.value = true;
      }
    }

    void collapse() {
      if (canExpand.value) {
        isExpanded.value = false;
      }
    }

    if (isExpanded.value) {
      return GestureDetector(
        onTap: collapse,
        child: HorizontalScrollButtons(
          child: RichText(text: _buildBreadcrumbs(context, expand)),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final removedIndices = <int>{};

        var addedCollapsedElement = false;
        var exceeds = true;
        late TextSpan breadcrumbs;
        int passes = 0;

        while (exceeds && passes < itemCount) {
          breadcrumbs = _buildBreadcrumbs(context, expand, removedIndices);

          // TODO: This can be optimized to render like we do the collapsible label.
          final textPainter = TextPainter(
            maxLines: 1,
            textAlign: textAlign,
            textDirection: context.textDirection,
            text: breadcrumbs,
          );
          textPainter.layout(maxWidth: constraints.maxWidth);

          // Whether the text overflowed or not
          exceeds = textPainter.didExceedMaxLines;

          if (exceeds && !isExpanded.value) {
            addedCollapsedElement = true;

            final indices = List.generate(itemCount, (index) => index);
            indices.removeWhere((element) => removedIndices.contains(element));

            var toRemove = indices[(indices.length / 2).floor()];

            if (toRemove == indices.last) {
              toRemove -= 1;
            }

            removedIndices.add(toRemove);
          }

          passes++;
        }

        if (addedCollapsedElement) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (passes == itemCount) {
              // Can't collapse
              canExpand.value = false;
              isExpanded.value = true;
            } else {
              canExpand.value = true;
              isExpanded.value = false;
            }
          });
        }

        return RichText(
          text: breadcrumbs,
          maxLines: 1,
          textAlign: textAlign,
        );
      },
    );
  }

  TextSpan _buildBreadcrumbs(BuildContext context, void Function() expand, [Set<int>? removedElementIndices]) {
    final spans = _buildTextSpans(context);
    final children = <TextSpan>[];

    bool hiddenElementsReplacementInserted = false;
    for (var index = 0; index < itemCount; index++) {
      if (removedElementIndices == null || !removedElementIndices.contains(index)) {
        children.add(spans[index]);
        if (index < itemCount - 1) {
          children.add(_separator);
        }
      } else {
        if (!hiddenElementsReplacementInserted) {
          children.add(_buildCollapsedElementReplacement(context, expand));
          children.add(_separator);
          hiddenElementsReplacementInserted = true;
        }
      }
    }

    return TextSpan(
      children: children,
      style: style,
    );
  }

  List<TextSpan> _buildTextSpans(BuildContext context) => items.mapIndexed((index, item) => itemBuilder(context, item, index)).toList();

  TextSpan _buildCollapsedElementReplacement(BuildContext context, void Function() expand) =>
      collapsedElementBuilder?.call(context, expand) ??
      TextSpan(
        text: collapsedElementReplacement,
        style: style,
        recognizer: TapGestureRecognizer()..onTap = expand,
      );
}
