import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/root_store.dart';
import 'package:qfiler/presentation/util/setup.dart';

/// Base class for QFiler integration tests that provides common setup and teardown
abstract class BaseIntegrationTest {
  static bool _isSetUp = false;

  /// Temporary base directory for this test
  late final io.Directory tempBaseDir;

  /// Left pane temporary directory
  late final io.Directory leftTempDir;

  /// Right pane temporary directory
  late final io.Directory rightTempDir;

  /// Data repository temporary directory
  late final io.Directory dataTempDir;

  /// The root store instance for this test
  late final RootStore rootStore;

  /// Set up the integration test environment
  static void setUpIntegrationTests() {
    if (!_isSetUp) {
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
      setUpAll(() => setupApp());
      _isSetUp = true;
    }
  }

  /// Create temporary directories and root store for the test
  Future<void> setUp({
    String testName = 'qfiler_integration_test',
    List<String>? leftFiles,
    List<String>? rightFiles,
  }) async {
    // Create temporary directories
    tempBaseDir = await io.Directory.systemTemp.createTemp('${testName}_');
    leftTempDir = await io.Directory(p.join(tempBaseDir.path, 'left')).create();
    rightTempDir = await io.Directory(p.join(tempBaseDir.path, 'right')).create();
    dataTempDir = await io.Directory(p.join(tempBaseDir.path, 'data')).create();

    // Create test files
    await _createTestFiles(leftTempDir, leftFiles ?? ['file1.txt', 'file2.txt']);
    await _createTestFiles(rightTempDir, rightFiles ?? ['file3.txt']);

    // Create RootStore and set directories
    rootStore = await RootStore.create();
    rootStore.allPanesStore.left.historyStore.addWithPath(RawPath(leftTempDir.path));
    rootStore.allPanesStore.right.historyStore.addWithPath(RawPath(rightTempDir.path));
  }

  /// Pump the app and wait for it to settle
  Future<void> pumpApp(WidgetTester tester, {Duration? settleTimeout}) async {
    await tester.pumpWidget(App(rootStore));
    await tester.pumpAndSettle(settleTimeout ?? const Duration(seconds: 3));
  }

  /// Clean up temporary directories
  Future<void> tearDown() async {
    if (await tempBaseDir.exists()) {
      await tempBaseDir.delete(recursive: true);
    }
  }

  /// Create test files in the specified directory
  Future<void> _createTestFiles(io.Directory dir, List<String> fileNames) async {
    for (int i = 0; i < fileNames.length; i++) {
      final fileName = fileNames[i];
      await io.File(p.join(dir.path, fileName)).writeAsString('Test file content $i');
    }
  }

  /// Debug helper to print current state
  void debugCurrentState({String? label}) {
    if (kDebugMode) {
      final prefix = label != null ? '[$label] ' : '';
      print('$prefix=== DEBUG STATE ===');
      print('${prefix}Left pane is source: ${rootStore.allPanesStore.left.isSource}');
      print('${prefix}Right pane is source: ${rootStore.allPanesStore.right.isSource}');
      print('${prefix}Source pane side: ${rootStore.allPanesStore.sourcePaneSide}');
      print('${prefix}Left pane files: ${rootStore.allPanesStore.left.directoryViewStore.files.length}');
      print('${prefix}Left pane focused index: ${rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex}');
      print('${prefix}Right pane files: ${rootStore.allPanesStore.right.directoryViewStore.files.length}');
      print('${prefix}Right pane focused index: ${rootStore.allPanesStore.right.directoryViewStore.focusedRowIndex}');
    }
  }

  /// Wait for a short period to allow focus to settle
  Future<void> waitForFocusToSettle(WidgetTester tester) async {
    await tester.pumpAndSettle(const Duration(milliseconds: 100));
  }

  /// Send a key event and wait for it to be processed
  Future<void> sendKeyAndWait(WidgetTester tester, LogicalKeyboardKey key, {Duration? waitTime}) async {
    await tester.sendKeyEvent(key);
    await tester.pumpAndSettle(waitTime ?? const Duration(milliseconds: 300));
  }
}

/// Mixin for tests that need to verify focus behavior
mixin FocusTestMixin on BaseIntegrationTest {
  /// Verify that arrow keys work in the specified pane
  Future<void> verifyArrowKeysWork(WidgetTester tester, {required bool isLeftPane, String? debugLabel}) async {
    final paneStore = isLeftPane ? rootStore.allPanesStore.left : rootStore.allPanesStore.right;
    final initialIndex = paneStore.directoryViewStore.focusedRowIndex;
    final fileCount = paneStore.directoryViewStore.files.length;

    if (kDebugMode && debugLabel != null) {
      print('[$debugLabel] Testing arrow keys - Initial index: $initialIndex, File count: $fileCount');
    }

    // Send arrow down key
    await sendKeyAndWait(tester, LogicalKeyboardKey.arrowDown);

    final newIndex = paneStore.directoryViewStore.focusedRowIndex;

    if (kDebugMode && debugLabel != null) {
      print('[$debugLabel] After arrow down - New index: $newIndex');
    }

    // If there are multiple files, the index should change
    if (fileCount > 1) {
      expect(newIndex, isNot(equals(initialIndex)),
             reason: 'Arrow key should change focused row when ${isLeftPane ? 'left' : 'right'} pane has focus');
    } else {
      expect(newIndex, equals(initialIndex),
             reason: 'With only one file, focused index should remain the same');
    }
  }

  /// Verify that tab key switches between panes
  Future<void> verifyTabSwitching(WidgetTester tester, {String? debugLabel}) async {
    // Should start with left pane as source
    expect(rootStore.allPanesStore.sourcePaneSide.name, equals('left'));

    if (kDebugMode && debugLabel != null) {
      print('[$debugLabel] Before Tab - Source: ${rootStore.allPanesStore.sourcePaneSide}');
    }

    // Press Tab to switch to right pane
    await sendKeyAndWait(tester, LogicalKeyboardKey.tab);

    if (kDebugMode && debugLabel != null) {
      print('[$debugLabel] After Tab - Source: ${rootStore.allPanesStore.sourcePaneSide}');
    }

    expect(rootStore.allPanesStore.sourcePaneSide.name, equals('right'));

    // Press Tab again to switch back to left pane
    await sendKeyAndWait(tester, LogicalKeyboardKey.tab);

    if (kDebugMode && debugLabel != null) {
      print('[$debugLabel] After second Tab - Source: ${rootStore.allPanesStore.sourcePaneSide}');
    }

    expect(rootStore.allPanesStore.sourcePaneSide.name, equals('left'));
  }
}
