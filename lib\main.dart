import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:mobx/mobx.dart';
import 'package:window_manager/window_manager.dart';

import './presentation/app.dart';
import './presentation/root_store.dart';
import 'app/util/log_colorizer.dart';
import 'presentation/util/setup_rust.dart';

final logger = Logger("main");

Future<void> main() async {
  // await myErrorsHandler.initialize();
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    logger.shout("Unhandled Flutter error:", details.exception, details.stack);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    logger.shout("Unhandled Flutter Platform error:", error, stack);
    return true;
  };

  mainContext.config = mainContext.config.clone(disableErrorBoundaries: kDebugMode);

  // Configure colorized logging
  if (kDebugMode) {
    hierarchicalLoggingEnabled = true;
    Logger.root.level = Level.ALL;

    // Use LogColorizer to format log messages
    Logger.root.onRecord.listen((record) {
      String formattedMessage = LogColorizer.colorizeRecord(record);
      debugPrint(formattedMessage);
    });
  } else {
    Logger.root.level = Level.OFF;
  }

  // This has to be called before CombineWorker().initialize()
  WidgetsFlutterBinding.ensureInitialized();
  // await CombineWorker().initialize();

  // Initialize window_manager
  await windowManager.ensureInitialized();

  await setupRust();

  final rootStore = await RootStore.create();

  // Apply window settings if available
  final windowSettings = rootStore.settingsRepository.settings.windowSettings;

  // Set window options with default size and position
  WindowOptions windowOptions = WindowOptions(
    size: Size(
      windowSettings.width?.toDouble() ?? 1280,
      windowSettings.height?.toDouble() ?? 720,
    ),
    center: windowSettings.x == null || windowSettings.y == null,
    backgroundColor: Colors.transparent,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
  );

  await windowManager.waitUntilReadyToShow(windowOptions, () async {
    // Set position if available
    if (windowSettings.x != null && windowSettings.y != null) {
      await windowManager.setPosition(
        Offset(
          windowSettings.x!.toDouble(),
          windowSettings.y!.toDouble(),
        ),
      );
    }

    // Set maximized state if needed
    if (windowSettings.isMaximized) {
      await windowManager.maximize();
    } else {
      await windowManager.show();
    }
  });

  // Create a window listener to save size and position
  windowManager.addListener(AppWindowListener(rootStore));

  runApp(App(rootStore));
}

/// Custom window listener to handle window events
class AppWindowListener extends WindowListener {
  final RootStore rootStore;

  AppWindowListener(this.rootStore);

  @override
  void onWindowResized() {
    _saveWindowSettings();
  }

  @override
  void onWindowMoved() {
    _saveWindowSettings();
  }

  @override
  void onWindowMaximize() {
    rootStore.settingsRepository.updateWindowSettings(
      (settings) => settings.copyWith(isMaximized: true),
    );
  }

  @override
  void onWindowUnmaximize() {
    rootStore.settingsRepository.updateWindowSettings(
      (settings) => settings.copyWith(isMaximized: false),
    );
  }

  /// Save the current window size and position to settings
  Future<void> _saveWindowSettings() async {
    if (await windowManager.isMaximized()) {
      return; // Don't save size/position when maximized
    }

    final size = await windowManager.getSize();
    final position = await windowManager.getPosition();

    rootStore.settingsRepository.updateWindowSettings(
      (settings) => settings.copyWith(
        width: size.width.toInt(),
        height: size.height.toInt(),
        x: position.dx.toInt(),
        y: position.dy.toInt(),
        isMaximized: false,
      ),
    );
  }
}
