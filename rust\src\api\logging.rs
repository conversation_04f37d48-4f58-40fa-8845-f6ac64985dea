use crate::{api::error::FileSystemError, helpers::log_utils};
use crate::frb_generated::StreamSink;
use flutter_rust_bridge::frb;
use log::LevelFilter;
pub use log_utils::{LogEntry, LogLevel};
use std::{
    path::Path,
    sync::atomic::{AtomicBool, Ordering},
};

const DEFAULT_LOG_LEVEL: LevelFilter = LevelFilter::Info;

/// create a logger label from a src file path
/// ```
/// let lbl = flutter_logger::get_lbl(file!());
/// ```
/// # Panics
/// panics if an invalid path is given
#[must_use]
fn get_lbl(path: &str) -> &str {
    let filename = Path::new(path).file_name().unwrap().to_str().unwrap();
    &filename[..filename.len() - 3]
}

const LOGGER: FlutterLogger = FlutterLogger {};
static IS_INITIALIZED: AtomicBool = AtomicBool::new(false);

/// initialize the Logger with a stream that sends `LogEntries` to dart/flutter Errors
/// return an error if a logger was already set
pub fn setup_rust_logging(sink: StreamSink<LogEntry>) -> Result<(), FileSystemError> {
    if !IS_INITIALIZED.swap(true, Ordering::Relaxed) {
        log::set_logger(&LOGGER)?;
    }
    log::set_max_level(DEFAULT_LOG_LEVEL);
    crate::helpers::log_utils::init(sink);
    Ok(())
}

#[frb(opaque)]
struct FlutterLogger {}
impl log::Log for FlutterLogger {
    fn enabled(&self, metadata: &log::Metadata) -> bool {
        metadata.level() <= log::max_level()
    }

    fn log(&self, record: &log::Record) {
        if !self.enabled(record.metadata()) {
            return;
        }
        crate::helpers::log_utils::log(
            record.level(),
            record.file().map_or("unknown", get_lbl),
            &std::fmt::format(record.args().to_owned()),
        );
    }

    fn flush(&self) {}
}

impl log_utils::LogSink for StreamSink<LogEntry> {
    fn send(&self, entry: LogEntry) {
        self.add(entry).unwrap();
    }
}
