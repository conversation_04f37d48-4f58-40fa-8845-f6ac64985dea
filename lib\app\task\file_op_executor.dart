import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';

import '../domain/user_choice.dart';
import '../file_system/file_system.dart';
import '../util/duration_utils.dart';
import '../util/log_utils.dart';
import 'pending_file_store.dart';
import 'task.dart';

part '.gen/file_op_executor.g.dart';

class FileOpExecutor = FileOpExecutorBase with _$FileOpExecutor;

abstract class FileOpExecutorBase with Store {
  FileOpExecutorBase(
    this._fs,
    this._pendingOperationRepository,
    this._showConfirmOverwriteDialog,
    this._showFileOpErrorDialog,
  );

  /// The file system to use for operations
  final FileSystem _fs;

  final PendingOperationRepository _pendingOperationRepository;

  /// The dialog to show for confirming overwrite operations
  final ShowConfirmOverwriteDialog _showConfirmOverwriteDialog;

  /// The dialog to show for handling file operation errors
  final ShowFileOpErrorDialog _showFileOpErrorDialog;

  /// Main execute method
  Future<void> execute(Operation op) async {
    if (kDebugMode) {
      logger.finest('execute($op)');
    }

    final Stopwatch? stopwatch = kDebugMode ? (Stopwatch()..start()) : null;
    OpResult result = const OpResult(shouldRetry: false, success: false, statusMessage: '');
    final context = OpContext();

    try {
      result = await _execute(op, context);
    } on CancellationError {
      // Swallow cancellation exceptions, they're not errors
      if (kDebugMode) {
        logger.info('execute($op): Cancelled');
      }
      result = const OpResult(
        shouldRetry: false,
        success: false,
        statusMessage: 'Operation cancelled',
      );
    } catch (e, stack) {
      // This really shouldn't happen.
      if (kDebugMode) {
        logger.severe('execute($op): Error', e, stack);
      }
      result = const OpResult(
        shouldRetry: false,
        success: false,
        statusMessage: 'Operation failed',
      );
    } finally {
      if (kDebugMode) {
        stopwatch!.stop();
        logger.finest('execute($op): ${result.statusMessage} [${stopwatch.elapsed.humanReadable}]');
      }
    }
  }

  Future<OpResult> _execute(Operation op, OpContext context) {
    return switch (op) {
      CopyOperation() => _copy(op, context, deleteAfterCopy: false),
      MoveOperation() => _move(op, context),
      DeleteOperation() => _delete(op, context),
      CreateFileOperation() => _create(op, context),
      RenameOperation() => _rename(op, context),
      _ => throw ArgumentError('Unknown operation type: ${op.runtimeType}'),
    };
  }

  Future<OpResult> _copy(SourceDestOperation op, OpContext context, {required bool deleteAfterCopy}) {
    if (op.source.isDirectory) {
      return _copyDirectory(op, context, deleteAfterCopy: deleteAfterCopy);
    } else {
      return _copyFile(op, context, deleteAfterCopy: deleteAfterCopy);
    }
  }

  Future<OpResult> _copyDirectory(SourceDestOperation op, OpContext context, {required bool deleteAfterCopy}) {
    return _executeOp(op, context, () async {
      final children = op.children;
      await _createDestDir(op, context);

      OpResult childrenResult = const OpResult(shouldRetry: false, success: true, statusMessage: 'All children copied successfully');

      for (final childOp in children ?? const <SourceDestOperation>[]) {
        final result = await _copy(childOp, context, deleteAfterCopy: deleteAfterCopy);
        if (!result.success && childrenResult.success) {
          childrenResult = result;
        }
      }

      await _copyTimestampsToDest(op);

      if (deleteAfterCopy) {
        // Only delete the source directory if all children completed successfully
        if (childrenResult.success) {
          await _fs.rmdir(op.source.path, recursive: false);
        } else {
          if (kDebugMode) {
            logger.info('_copyDirectory($op) Not all children completed successfully, not deleting directory ${op.source}');
          }
          throw PartialSuccessError('Child operations partial success');
        }
      }
    });
  }

  Future<OpResult> _copyFile(SourceDestOperation op, OpContext context, {required bool deleteAfterCopy}) {
    if (kDebugMode) {
      logger.finest('_copyFile(${op.source.path} to ${op.destination})');
    }
    return _executeOp(op, context, () async {
      final options = context.getCopyOptions(op);

      // Copy small files directly (without reporting progress) to avoid slowing down the UI
      if (op.progress.isQuick) {
        await _copyFileDirectly(op, options);
      } else {
        await _copyFileWithProgress(op, options);
      }
      await _copyTimestampsToDest(op);

      if (deleteAfterCopy) {
        await _fs.deleteFile(op.source.path);
      }

      op.progress.finish(addFilesProcessed: 1);
    });
  }

  /// Copies a file directly without progress reporting
  Future<void> _copyFileDirectly(SourceDestOperation op, CopyOptions options) async {
    final file = op.source;
    final dest = op.destination;
    final progress = op.progress;

    await _fs.copyFile(file.path, dest, options);
    progress.addBytesProcessed(file.size ?? 0);
  }

  /// Copies a file with progress reporting
  Future<void> _copyFileWithProgress(SourceDestOperation op, CopyOptions options) async {
    final file = op.source;
    final dest = op.destination;
    final task = op.task;
    final progress = op.progress;

    try {
      final copyFileWithProgress = await _fs.copyFileWithProgress(file.path, dest, options, (progressReport) {
        progress.addBytesProcessed(progressReport.bytesCopied.toInt());
      });

      // Allow task to pause/cancel the operation
      task.setCurrentPausableOperation(copyFileWithProgress);

      await copyFileWithProgress.finished();
    } finally {
      task.setCurrentPausableOperation(null);
    }
  }

  Future<OpResult> _move(MoveOperation op, OpContext context) async {
    // We do things a bit weird here, but this is in order to avoid nesting _executeOp calls of the same op.
    // First we try to rename the file to dest directly, outside of an _executeOp block.
    // If it succeeds, we then run an empty _executeOp block to update the progress and remove it as a pending op.
    // If it fails, we fall back to copy-and-delete, which has its own _executeOp block.

    final file = op.source;
    final dest = op.destination;
    try {
      // First, try renaming the file to dest directly
      await _fs.rename(file.path, dest);

      // Rename succeeded, run an empty _executeOp block to update progress and remove it as a pending op
      return _executeOp(op, context, () async {});
    } on FileSystemError_AlreadyExists catch (e) {
      // Handle file already exists errors
      if (kDebugMode) {
        logger.fine('Failed to rename (file exists), falling back to copy-and-delete...', e);
      }
      return _copy(op, context, deleteAfterCopy: true);
    } on FileSystemError_CrossesDevices catch (e) {
      // Handle cross-device move errors
      if (kDebugMode) {
        logger.fine('Failed to rename (different device), falling back to copy-and-delete...', e);
      }
      return _copy(op, context, deleteAfterCopy: true);
    } catch (e) {
      // Throw e inside an _executeOp block in order to have our standard error handling.
      // Capture the error and rethrow it inside the _executeOp block
      final error = e;
      return _executeOp(op, context, () async {
        throw error; // Throwing the original error for proper error handling
      });
    }
  }

  Future<OpResult> _delete(DeleteOperation op, OpContext context) {
    if (op.source.isDirectory) {
      return _deleteDirectory(op, context);
    } else {
      return _deleteFile(op, context);
    }
  }

  Future<OpResult> _deleteDirectory(DeleteOperation op, OpContext context) {
    return _executeOp(op, context, () async {
      final children = op.children ?? const [];

      OpResult childrenResult = const OpResult(shouldRetry: false, success: true, statusMessage: 'All children deleted successfully');

      for (final childOp in children) {
        final result = await _delete(childOp, context);
        if (!result.success && childrenResult.success) {
          childrenResult = result;
        }
      }

      // Only delete the source directory if all children completed successfully
      if (childrenResult.success) {
        await _fs.rmdir(op.source.path, recursive: false);
      } else {
        if (kDebugMode) {
          logger.info('_deleteDirectory($op): Not all children completed successfully, not deleting directory ${op.source}');
        }
        throw PartialSuccessError('Child operations partial success');
      }
    });
  }

  Future<OpResult> _deleteFile(DeleteOperation op, OpContext context) {
    return _executeOp(op, context, () async {
      await _fs.deleteFile(op.source.path);
      op.progress.addBytesProcessed(op.source.size ?? 0);
    });
  }

  Future<OpResult> _create(CreateFileOperation op, OpContext context) {
    return _executeOp(op, context, () async {
      throw UnimplementedError('Create operation is not implemented yet');
    });
  }

  Future<OpResult> _rename(RenameOperation op, OpContext context) {
    return _executeOp(op, context, () async {
      await _fs.rename(op.source.path, op.destination);
      op.progress.finish();
    });
  }

  /// Copies the timestamps from the source file to the destination file
  Future<void> _copyTimestampsToDest(SourceDestOperation op) async {
    final file = op.source;
    final dest = op.destination;

    await _fs.setTimestamps(dest, createTime: file.createTime, modifyTime: file.updateTime, accessTime: file.accessTime);
  }

  /// Creates a destination directory
  Future<void> _createDestDir(SourceDestOperation op, OpContext context) async {
    await _fs.mkdir(op.destination, recursive: false);
  }

  /// Executes an operation with proper error handling, retries, and status updates
  /// Returns true if the operation was successful, false otherwise
  Future<OpResult> _executeOp(Operation op, OpContext context, Future<void> Function() action) async {
    if (kDebugMode) {
      logger.finest('_executeOp($op)');
    }
    final stopwatch = kDebugMode ? (Stopwatch()..start()) : null;

    OpResult result = const OpResult(
      shouldRetry: true,
      success: false,
      statusMessage: "",
    );

    try {
      assert(op.status == OperationStatus.waiting, 'Operation ${op.id} is not in waiting state');
      op.setStatus(OperationStatus.executing);

      int retryCount = 0;

      while (result.shouldRetry) {
        if (kDebugMode && retryCount > 0) {
          logger.finest('_executeOp($op):Retry attempt ${++retryCount}');
        }
        try {
          await action();

          op.setStatus(OperationStatus.completed);

          result = const OpResult(
            shouldRetry: false,
            success: true,
            statusMessage: "Completed Successfully",
          );
        } on CancellationError catch (e) {
          op.setStatus(OperationStatus.cancelled, error: e);
          rethrow;
        } on SkipFileError catch (e) {
          op.setStatus(OperationStatus.skipped, error: e);
          result = const OpResult(
            shouldRetry: false,
            success: false,
            statusMessage: "Skipped",
          );
        } on PartialSuccessError catch (e) {
          op.setStatus(OperationStatus.completed, error: e);
          result = const OpResult(
            shouldRetry: false,
            success: false,
            statusMessage: "Children operations partial success",
          );
        } on FileSystemError_AlreadyExists catch (e) {
          final overwriteMode = await _confirmOverwrite(op);

          // Update context with the confirmed overwrite mode
          context.setOverwriteMode(overwriteMode);

          switch (overwriteMode) {
            case OverwriteMode.overwrite:
              result = const OpResult(
                shouldRetry: true,
                success: false,
                statusMessage: "Retrying after overwrite confirmation",
              );
            case OverwriteMode.skip:
              op.setStatus(OperationStatus.skipped, error: e);
              result = const OpResult(
                shouldRetry: false,
                success: false,
                statusMessage: "Skipped after overwrite confirmation",
              );
            case OverwriteMode.cancel:
              throw _cancelOp(op, error: e);
          }
        } catch (e) {
          OperationErrorMode errorMode = await _confirmError(op, e);

          switch (errorMode) {
            case OperationErrorMode.retry:
              if (kDebugMode) {
                logger.info('_executeOp($op): Retrying...');
              }
              result = const OpResult(
                shouldRetry: true,
                success: false,
                statusMessage: "Retrying on error",
              );
            case OperationErrorMode.skip:
              op.setStatus(OperationStatus.skipped, error: e);
              result = const OpResult(
                shouldRetry: false,
                success: false,
                statusMessage: "Skipped on error",
              );
            case OperationErrorMode.cancel:
              throw _cancelOp(op, error: e);
          }
        }
      }
    } finally {
      if (kDebugMode) {
        stopwatch!.stop();
        logger.finest('_executeOp($op): ${result.statusMessage} [${stopwatch.elapsed.humanReadable}]');
      }
      _pendingOperationRepository.removeOp(op);
    }
    return result;
  }

  /// Confirms whether to overwrite a file by showing a dialog
  Future<OverwriteMode> _confirmOverwrite(Operation op) async {
    // Check if we already have an overwrite mode for all operations
    final overwriteModeForAll = op.task.overwriteModeForAll;
    if (overwriteModeForAll != null) {
      if (kDebugMode) {
        logger.info('_confirmOverwrite($op): Using overwriteModeForAll=$overwriteModeForAll');
      }
      return overwriteModeForAll;
    }

    // FIXME: Why is previousOverwriteAllSelected needed at all??
    final selectOverwriteAll = op.task.previousOverwriteAllSelected;
    final choice = await _showConfirmOverwriteDialog(op: op, selectOverwriteAll: selectOverwriteAll);

    if (kDebugMode) {
      logger.info('_confirmOverwrite($op): Confirm overwrite choice: ${choice.overwriteMode} ${choice.all ? 'all' : ''}');
    }

    // Set the overwrite mode for this operation and potentially all operations
    if (choice.all) {
      op.task.setOverwriteModeForAll(choice.overwriteMode);
    }

    return choice.overwriteMode;
  }

  Future<OperationErrorMode> _confirmError(Operation op, Object error) async {
    OperationErrorMode errorMode;
    final errorModeForAll = op.task.errorModeForAll;
    if (errorModeForAll != null) {
      // Use the existing error mode for all operations
      errorMode = errorModeForAll;
      if (kDebugMode) {
        logger.info('_confirmError($op) Using errorModeForAll: $errorMode');
      }
    } else {
      final OperationErrorChoice choice = await _showFileOpErrorDialog(op: op, error: error);
      errorMode = choice.mode;
      if (choice.all) {
        op.task.setErrorModeForAll(errorMode);
      }
    }
    return errorMode;
  }

  @action
  CancellationError _cancelOp(Operation op, {Object? error}) {
    op.setStatus(OperationStatus.cancelled, error: error);
    op.task.cancel();
    return CancellationError();
  }

  static final logger = loggerFor(FileOpExecutor);
}

/// Interface for showing a confirmation dialog for overwriting files
abstract class ShowConfirmOverwriteDialog {
  /// Shows a dialog to confirm overwriting a file
  Future<OverwriteChoice> call({required Operation op, required bool selectOverwriteAll});
}

/// Interface for showing an error dialog for file operations
abstract class ShowFileOpErrorDialog {
  /// Shows a dialog to handle file operation errors
  Future<OperationErrorChoice> call({required Operation op, required Object error});
}

/// Error thrown when a file should be skipped
class SkipFileError implements Exception {
  final String reason;

  SkipFileError([this.reason = 'Operation skipped']);

  @override
  String toString() => reason;
}

/// Context for an operation execution, containing state that may change during execution
// FIXME: This class doesn't work as expected. It keeps per-op state, but is shared among all ops.
class OpContext {
  OverwriteMode? _overwriteMode;
  CopyOptions? _copyOptions;

  /// Gets the effective overwrite mode, using task-wide settings if no specific mode is set
  OverwriteMode? getOverwriteMode(Operation op) {
    return _overwriteMode ?? op.task.overwriteModeForAll;
  }

  /// Sets the overwrite mode for this operation
  void setOverwriteMode(OverwriteMode mode) {
    _overwriteMode = mode;

    final current = _copyOptions ??
        const CopyOptions(
          overwriteIfExists: false,
          preserveTimestamps: true,
          preserveMetadata: true,
        );
    _copyOptions = current.copyWith(
      overwriteIfExists: mode == OverwriteMode.overwrite,
    );
  }

  /// Gets the copy options, creating default ones if not set
  CopyOptions getCopyOptions(Operation op) {
    return _copyOptions ??
        CopyOptions(
          overwriteIfExists: getOverwriteMode(op) == OverwriteMode.overwrite,
          preserveTimestamps: true,
          preserveMetadata: false,
        );
  }
}

/// General result for operation execution and error handling
class OpResult {
  final bool shouldRetry;
  final bool success;
  final String statusMessage;

  const OpResult({
    required this.shouldRetry,
    required this.success,
    required this.statusMessage,
  });
}

/// Error thrown when an operation partially succeeded
class PartialSuccessError implements Exception {
  final String message;

  PartialSuccessError([this.message = 'Operation partially succeeded']);

  @override
  String toString() => message;
}
