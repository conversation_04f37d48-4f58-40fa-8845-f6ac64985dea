import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../app/settings/settings_repository.dart';
import '../command/command_context_repository.dart';
import '../command/command_dispatcher.dart';
import '../command/command_repository.dart';
import '../command/keybind_manager.dart';
import '../command/keybind_repository.dart';
import '../directory_view/directory_view_store.dart';
import '../history/history_store.dart';
import '../pane/all_panes_store.dart';
import '../pane/pane_store.dart';
import '../rename/rename_store.dart';
import '../root_store.dart';

/// Extensions on BuildContext to easily access dependencies
extension ContextExtensions on BuildContext {
  /// Get the RootStore from the context
  RootStore get rootStore => Provider.of<RootStore>(this, listen: false);

  /// Get the CommandRepository from the context
  CommandRepository get commandRepository => Provider.of<CommandRepository>(this, listen: false);

  /// Get the KeyBindManager from the context
  KeyBindManager get keyBindManager => Provider.of<KeyBindManager>(this, listen: false);

  /// Get the CommandDispatcher from the context
  CommandDispatcher get commandDispatcher => Provider.of<CommandDispatcher>(this, listen: false);

  /// Get the AllPanesStore from the context
  AllPanesStore get allPanesStore => Provider.of<AllPanesStore>(this, listen: false);

  /// Get the PaneStore from the context
  PaneStore get paneStore => Provider.of<PaneStore>(this, listen: false);

  /// Get the DirectoryViewStore from the context
  DirectoryViewStore get directoryViewStore => Provider.of<DirectoryViewStore>(this, listen: false);

  /// Get the HistoryStore from the context
  HistoryStore get historyStore => Provider.of<PaneStore>(this, listen: false).historyStore;

  /// Get the KeyBindRepository from the context
  KeyBindRepository get keyBindRepository => Provider.of<KeyBindRepository>(this, listen: false);

  CommandContextRepository get commandContextRepository => rootStore.commandContextRepository;

  /// Get the RenameStore from the context
  RenameStore get renameStore => Provider.of<RenameStore>(this, listen: false);

  /// Get the SettingsRepository from the context (previously SettingsStore)
  SettingsRepository get settingsRepository => rootStore.settingsRepository;

  /// Get the overlay state from the context
  OverlayState get overlay => Overlay.of(this);

  /// Get the text direction from the context
  TextDirection get textDirection => Directionality.of(this);
}
