import 'package:collection/collection.dart';

extension StringExtension on String {
  bool equalsIgnoreCase(String other) => equalsIgnoreAsciiCase(this, other);

  // TODO: Implement these more efficiently
  int compareToIgnoreCase(String other) => toLowerCase().compareTo(other.toLowerCase());

  bool containsIgnoreCase(String other) => toLowerCase().contains(other.toLowerCase());

  bool startsWithIgnoreCase(String other) => toLowerCase().startsWith(other.toLowerCase());

  // Same as startsWith, but checks the string from the end to the start.
  // In certain cases, this is more likely to break early.
  bool startsWithReversed(String other) {
    if (other.length > length) return false;
    for (var i = other.length - 1; i >= 0; i--) {
      if (other.codeUnitAt(i) != codeUnitAt(i)) {
        return false;
      }
    }
    return true;
  }

  // Same as endsWith, but checks the string from the end to the start.
  // In certain cases, this is more likely to break early.
  bool endsWithReversed(String other) {
    if (other.length > length) return false;
    for (var i = other.length - 1; i >= 0; i--) {
      if (other.codeUnitAt(i) != codeUnitAt(length - other.length + i)) {
        return false;
      }
    }
    return true;
  }

  int longestCommonSuffixLength(String other) {
    var i = 0;
    while (i < length && i < other.length && codeUnitAt(length - i - 1) == other.codeUnitAt(other.length - i - 1)) {
      i++;
    }
    return i;
  }
}
