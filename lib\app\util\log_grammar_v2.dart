import 'package:petitparser/petitparser.dart';

// (TaggedSegment and buildFinalString remain the same)
class TaggedSegment {
  final String type;
  final String text;
  TaggedSegment(this.type, this.text);
  @override
  String toString() => "TaggedSegment('$type', '$text')";
}

String buildFinalString(List<dynamic> segmentsList) {
  final segments = segmentsList.whereType<TaggedSegment>().toList();
  final buffer = StringBuffer();
  final parenStack = <int>[];
  final bracketStack = <int>[];
  final braceStack = <int>[];
  int currentParenLevel = 0;
  int currentBracketLevel = 0;
  int currentBraceLevel = 0;

  for (int i = 0; i < segments.length; i++) {
    final segment = segments[i];
    String tagType = segment.type;
    String openTag = '', closeTag = '';
    if (segment.type == 'plaintext') {
      buffer.write(segment.text);
      continue;
    }
    if (segment.type.startsWith('paren')) {
      if (segment.type.endsWith('Open')) {
        parenStack.add(currentParenLevel);
        tagType = 'paren_${currentParenLevel++}';
      } else {
        if (parenStack.isNotEmpty) {
          currentParenLevel--;
          tagType = 'paren_${parenStack.removeLast()}';
        } else {
          tagType = 'paren_0';
        }
      }
    } else if (segment.type.startsWith('bracket')) {
      if (segment.type.endsWith('Open')) {
        bracketStack.add(currentBracketLevel);
        tagType = 'bracket_${currentBracketLevel++}';
      } else {
        if (bracketStack.isNotEmpty) {
          currentBracketLevel--;
          tagType = 'bracket_${bracketStack.removeLast()}';
        } else {
          tagType = 'bracket_0';
        }
      }
    } else if (segment.type.startsWith('brace')) {
      if (segment.type.endsWith('Open')) {
        braceStack.add(currentBraceLevel);
        tagType = 'brace_${currentBraceLevel++}';
      } else {
        if (braceStack.isNotEmpty) {
          currentBraceLevel--;
          tagType = 'brace_${braceStack.removeLast()}';
        } else {
          tagType = 'brace_0';
        }
      }
    }
    openTag = '[c:$tagType]';
    closeTag = '[/c:$tagType]';
    buffer.write(openTag);
    buffer.write(segment.text);
    buffer.write(closeTag);
  }
  return buffer.toString();
}

// Forward declarations (using dynamic for SettableParser for now to focus on logic)
final SettableParser _valueElementParser = undefined();
final SettableParser _valueContentForParameterParser = undefined();
final SettableParser _valueContentGenericParser = undefined();
final SettableParser _expression = undefined();

Parser<List<TaggedSegment>> buildSimplifiedLogParser() {
  final idChars = letter() | digit() | char('_') | char('.');
  final identifier = (letter() | char('_')) & (idChars | char(' ')).star();

  final funcName = identifier.flatten('Function name').trim().map<List<TaggedSegment>>((name) => [TaggedSegment('func', name)]);
  final nullLiteral = string('null').flatten('Null literal').map<List<TaggedSegment>>((val) => [TaggedSegment('null', val)]);
  final resultKeywordTextParser = (string('success') | string('partial') | string('removed')).flatten('Result keyword text');

  final eq = char('=').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('symbol', s)]);
  final comma = char(',').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('symbol', s)]);
  final colon = char(':').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('symbol', s)]);
  final arrow = string('->').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('symbol', s)]);

  final openParenParser = char('(').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('parenOpen', s)]);
  final closeParenParser = char(')').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('parenClose', s)]);
  final openBracketParser = char('[').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('bracketOpen', s)]);
  final closeBracketParser = char(']').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('bracketClose', s)]);
  final openBraceParser = char('{').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('braceOpen', s)]);
  final closeBraceParser = char('}').trim().map<List<TaggedSegment>>((s) => [TaggedSegment('braceClose', s)]);

  // Terminator for opaque text *within a parameter value segment*
  // It should stop before any new structure OR the end of the parameter list.
  // The comma is handled by separatedBy.
  final paramValueOpaqueSegmentTerminator = (openBracketParser.cast<Parser<dynamic>>() |
          openBraceParser.cast<Parser<dynamic>>() |
          openParenParser.cast<Parser<dynamic>>() | // For structures like `val (detail)`
          closeParenParser.cast<Parser<dynamic>>() // End of current function's parameter list
      // Comma is NOT here because `separatedBy` handles it.
      );

  final genericValueOpaqueSegmentTerminator = (openBracketParser.cast<Parser<dynamic>>() |
      openBraceParser.cast<Parser<dynamic>>() |
      openParenParser.cast<Parser<dynamic>>() |
      closeBracketParser.cast<Parser<dynamic>>() |
      closeBraceParser.cast<Parser<dynamic>>() |
      closeParenParser.cast<Parser<dynamic>>() | // For values inside other structures like top-level bracket
      comma.cast<Parser<dynamic>>() | // For lists like [a,b] within generic content
      colon.cast<Parser<dynamic>>() |
      arrow.cast<Parser<dynamic>>());

  // Element for content *within a parameter value*
  // This should not try to parse function calls (_expression) aggressively.
  final paramValueElement = ChoiceParser([
    (openBracketParser & _valueContentForParameterParser.optional() & closeBracketParser)
        .map<List<TaggedSegment>>((v) => [...v[0] as List, ...(v[1] as List? ?? []), ...v[2] as List]),
    (openBraceParser & _valueContentForParameterParser.optional() & closeBraceParser)
        .map<List<TaggedSegment>>((v) => [...v[0] as List, ...(v[1] as List? ?? []), ...v[2] as List]),
    (openParenParser & _valueContentForParameterParser.optional() & closeParenParser) // For `val(detail)` type things, not full expressions
        .map<List<TaggedSegment>>((v) => [...v[0] as List, ...(v[1] as List? ?? []), ...v[2] as List]),
    any()
        .plusLazy(paramValueOpaqueSegmentTerminator) // Stops before comma or ) or new struct
        .flatten('Opaque Param Value Element')
        .map<List<TaggedSegment>>((text) => [TaggedSegment('opaque_value_segment', text.trim())]),
  ]);

  _valueContentForParameterParser.set(
    paramValueElement.plus().map(
          (listOfElementLists) => listOfElementLists
              .expand<TaggedSegment>((list) => list)
              .toList()
              .where((ts) => ts.text.isNotEmpty || ts.type.contains('Open') || ts.type.contains('Close'))
              .toList(),
        ),
  );

  // Element for *generic content* (e.g., inside top-level brackets, or results)
  final genericValueElement = ChoiceParser<dynamic>([
    _expression.cast<Parser<List<TaggedSegment>>>(), // Allow full expressions here
    (openBracketParser & _valueContentGenericParser.optional() & closeBracketParser)
        .map<List<TaggedSegment>>((v) => [...v[0] as List, ...(v[1] as List? ?? []), ...v[2] as List]),
    (openBraceParser & _valueContentGenericParser.optional() & closeBraceParser)
        .map<List<TaggedSegment>>((v) => [...v[0] as List, ...(v[1] as List? ?? []), ...v[2] as List]),
    (openParenParser & _valueContentGenericParser.optional() & closeParenParser)
        .map<List<TaggedSegment>>((v) => [...v[0] as List, ...(v[1] as List? ?? []), ...v[2] as List]),
    comma.cast<Parser<List<TaggedSegment>>>() | // Symbols are elements in generic content
        colon.cast<Parser<List<TaggedSegment>>>() |
        arrow.cast<Parser<List<TaggedSegment>>>() |
        nullLiteral.cast<Parser<List<TaggedSegment>>>() | // null can be an element
        any()
            .plusLazy(genericValueOpaqueSegmentTerminator)
            .flatten('Opaque Generic Value Element')
            .map<List<TaggedSegment>>((text) => [TaggedSegment('opaque_value_segment', text.trim())]),
  ]);

  _valueContentGenericParser.set(
    genericValueElement.plus().map(
          (listOfElementLists) => listOfElementLists
              .expand<TaggedSegment>((list) => list as List<TaggedSegment>)
              .toList()
              .where((ts) => ts.text.isNotEmpty || ts.type.contains('Open') || ts.type.contains('Close'))
              .toList(),
        ),
  );

  final paramKey = identifier.flatten('Parameter key').trim().map<List<TaggedSegment>>((key) => [TaggedSegment('paramKey', key)]);
  final resultKeyWord =
      (stringIgnoreCase('result')).flatten('Result key word').trim().map<List<TaggedSegment>>((key) => [TaggedSegment('resultKey', key)]);

  // parameterValue uses _valueContentForParameterParser for its value string
  final parameterValueProducer = _valueContentForParameterParser.optional().map<List<TaggedSegment>>((valueSegmentsNullable) {
    final valueSegments = valueSegmentsNullable ?? <TaggedSegment>[];
    final fullValueText = valueSegments.map((s) => s.text).join(); // Keep internal spaces

    // Check if the joined text itself is a simple result keyword AND it wasn't originally a complex structure
    bool isSimpleResultKeyword = resultKeywordTextParser.parse(fullValueText.trim()).isSuccess &&
        !valueSegments.any((s) => s.type.contains('Open') || s.type.contains('Close') || s.type == 'symbol');

    if (isSimpleResultKeyword) {
      return [TaggedSegment('potential_result_val_whole', fullValueText.trim())];
    }
    // If it was an empty structure like "[]", the text is "[]"
    if (fullValueText.isEmpty && valueSegments.isNotEmpty) {
      return [TaggedSegment('generic_val_whole', valueSegments.map((s) => s.text).join())];
    }
    return [TaggedSegment('generic_val_whole', fullValueText)];
  });

  final justValueParameter = parameterValueProducer.map<List<TaggedSegment>>(
    (segments) => segments.map((s) {
      if (s.type == 'potential_result_val_whole') {
        return TaggedSegment('paramVal', s.text);
      }
      if (s.type == 'generic_val_whole') {
        return TaggedSegment('paramVal', s.text);
      }
      return s; // Should not happen if parameterValueProducer is correct
    }).toList(),
  );

  final keyValueParameter = (paramKey & eq & parameterValueProducer).map<List<TaggedSegment>>((values) {
    final valSegs = (values[2] as List<TaggedSegment>).map((s) {
      if (s.type == 'potential_result_val_whole') {
        return TaggedSegment('paramVal', s.text);
      }
      if (s.type == 'generic_val_whole') {
        return TaggedSegment('paramVal', s.text);
      }
      return s;
    }).toList();
    return [...values[0] as List, ...values[1] as List, ...valSegs];
  });

  final resultParameter = (resultKeyWord & eq & parameterValueProducer).map<List<TaggedSegment>>((values) {
    final valSegs = (values[2] as List<TaggedSegment>).map((s) {
      if (s.type == 'potential_result_val_whole' || s.type == 'generic_val_whole') {
        return TaggedSegment('resultVal', s.text);
      }
      return s;
    }).toList();
    return [...values[0] as List, ...values[1] as List, ...valSegs];
  });

  final parameter = ChoiceParser([resultParameter, keyValueParameter, justValueParameter]).cast<Parser<List<TaggedSegment>>>();

  final parameters = parameter
      .separatedBy(
    comma, // Separator is just the comma parser
    includeSeparators: true,
    optionalSeparatorAtEnd: true,
  )
      .map<List<TaggedSegment>>((listOfValueOrSeparatorLists) {
    return listOfValueOrSeparatorLists.expand<TaggedSegment>((list) => list as List<TaggedSegment>).toList();
  });

  final functionCall = (funcName & openParenParser & parameters.optional() & closeParenParser).map<List<TaggedSegment>>((values) {
    final List<TaggedSegment> result = [...values[0] as List, ...values[1] as List];
    final paramsList = values[2] as List<TaggedSegment>?;
    if (paramsList != null && paramsList.isNotEmpty) {
      result.addAll(paramsList);
    }
    result.addAll(values[3] as List<TaggedSegment>);
    return result;
  });

  _expression.set(functionCall);

  final topLevelBracket = (openBracketParser & _valueContentGenericParser.optional() & closeBracketParser).map<List<dynamic>>((values) {
    final List<dynamic> result = [...values[0] as List];
    final contentSegments = values[1] as List<TaggedSegment>?;
    if (contentSegments != null) {
      for (var seg in contentSegments) {
        if (seg.type == 'opaque_value_segment') {
          final text = seg.text;
          if (resultKeywordTextParser.parse(text).isSuccess) {
            result.add(TaggedSegment('resultVal', text));
          } else if (text.isNotEmpty) result.add(TaggedSegment('plaintext', text));
        } else {
          result.add(seg);
        }
      }
    }
    result.addAll(values[2] as List);
    return result.where((ts) => ts.text.isNotEmpty || ts.type.contains('Open') || ts.type.contains('Close')).toList();
  });

  final standaloneResult = (resultKeyWord & colon & _valueContentGenericParser.optional()).map<List<TaggedSegment>>((values) {
    final List<TaggedSegment> result = [...values[0] as List, ...values[1] as List];
    final contentSegments = values[2] as List<TaggedSegment>?;
    if (contentSegments != null) {
      bool hasInternalStructure = contentSegments.any((s) => s.type != 'opaque_value_segment');
      if (hasInternalStructure) {
        for (var seg in contentSegments) {
          if (seg.type == 'opaque_value_segment') {
            result.add(TaggedSegment('resultVal', seg.text));
          } else {
            result.add(seg);
          }
        }
      } else {
        final joinedText = contentSegments.map((s) => s.text).join().trim();
        if (joinedText.isNotEmpty) {
          result.add(TaggedSegment('resultVal', joinedText));
        }
      }
    }
    return result;
  });

  final knownTermsChoice = ChoiceParser<dynamic>([
    _expression,
    topLevelBracket,
    standaloneResult,
    arrow,
    openParenParser,
    closeParenParser,
    openBracketParser,
    closeBracketParser,
    openBraceParser,
    closeBraceParser,
    nullLiteral,
  ]).cast<Parser<List<TaggedSegment>>>();

  final plaintextSegment = any()
      .plusLazy(knownTermsChoice.cast<Parser<dynamic>>())
      .flatten('Plaintext segment')
      .map<List<TaggedSegment>>((text) => [TaggedSegment('plaintext', text.trim())]); // Trim plaintext

  final logLinePart = ChoiceParser([knownTermsChoice, plaintextSegment]).cast<Parser<List<TaggedSegment>>>();

  final logParser = logLinePart.star().map((listOfLists) {
    return listOfLists.expand<TaggedSegment>((list) => list as List<TaggedSegment>).toList();
  });

  return logParser.end();
}

void main() {
  final parser = buildSimplifiedLogParser();
  final testCases = {
    "processData(files=[file1.txt, file2.txt], options={recursive: true})":
        "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal][file1.txt, file2.txt][/c:paramVal][c:symbol],[/c:symbol][c:paramKey]options[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]{recursive: true}[/c:paramVal][c:paren_0])[/c:paren_0]",
    "complexFunc(param1=[1, 2, [3, 4]], param2=test[5])":
        "[c:func]complexFunc[/c:func][c:paren_0]([/c:paren_0][c:paramKey]param1[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal][1, 2, [3, 4]][/c:paramVal][c:symbol],[/c:symbol][c:paramKey]param2[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]test[5][/c:paramVal][c:paren_0])[/c:paren_0]",
    "_calcFile(0_temp12, doesn't exist)":
        "[c:func]_calcFile[/c:func][c:paren_0]([/c:paren_0][c:paramVal]0_temp12[/c:paramVal][c:symbol],[/c:symbol][c:paramVal]doesn't exist[/c:paramVal][c:paren_0])[/c:paren_0]",
    "[FileOpExecutor] execute(RenameOperation([D:\\Temp\\0_temp1] -> [D:\\Temp\\0_temp12])): Completed Successfully [34ms]":
        "[c:bracket_0][[/c:bracket_0][c:plaintext]FileOpExecutor[/c:plaintext][c:bracket_0]][/c:bracket_0][c:plaintext] [/c:plaintext][c:func]execute[/c:func][c:paren_0]([/c:paren_0][c:paramVal]RenameOperation([D:\\Temp\\0_temp1] -> [D:\\Temp\\0_temp12])[/c:paramVal][c:paren_0])[/c:paren_0][c:plaintext]: Completed Successfully [/c:plaintext][c:bracket_0][[/c:bracket_0][c:plaintext]34ms[/c:plaintext][c:bracket_0]][/c:bracket_0]",
    "Result: success": "[c:resultKey]Result[/c:resultKey][c:symbol]:[/c:symbol][c:resultVal]success[/c:resultVal]",
    "Result: [success, partial]":
        "[c:resultKey]Result[/c:resultKey][c:symbol]:[/c:symbol][c:bracket_0][[/c:bracket_0][c:resultVal]success[/c:resultVal][c:symbol],[/c:symbol][c:resultVal]partial[/c:resultVal][c:bracket_0]][/c:bracket_0]",
    "processData(result=[success, partial])": // Expected paramVal based on previous discussion
        "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:resultKey]result[/c:resultKey][c:symbol]=[/c:symbol][c:paramVal][success, partial][/c:paramVal][c:paren_0])[/c:paren_0]",
    "test(name=document (final).pdf)":
        "[c:func]test[/c:func][c:paren_0]([/c:paren_0][c:paramKey]name[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]document (final).pdf[/c:paramVal][c:paren_0])[/c:paren_0]",
    "test(p1 = val1 (internal paren), p2 = val2)":
        "[c:func]test[/c:func][c:paren_0]([/c:paren_0][c:paramKey]p1[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]val1 (internal paren)[/c:paramVal][c:symbol],[/c:symbol][c:paramKey]p2[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]val2[/c:paramVal][c:paren_0])[/c:paren_0]",
    "foo(bar=[a->b])":
        "[c:func]foo[/c:func][c:paren_0]([/c:paren_0][c:paramKey]bar[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal][a->b][/c:paramVal][c:paren_0])[/c:paren_0]",
    "text before func() text after":
        "[c:plaintext]text before[/c:plaintext][c:func]func[/c:func][c:paren_0]([/c:paren_0][c:paren_0])[/c:paren_0][c:plaintext]text after[/c:plaintext]",
    "[]": "[c:bracket_0][[/c:bracket_0][c:bracket_0]][/c:bracket_0]",
    "func([])": "[c:func]func[/c:func][c:paren_0]([/c:paren_0][c:paramVal][][/c:paramVal][c:paren_0])[/c:paren_0]",
    "func(val, )": "[c:func]func[/c:func][c:paren_0]([/c:paren_0][c:paramVal]val[/c:paramVal][c:symbol],[/c:symbol][c:paren_0])[/c:paren_0]",
    "func()": "[c:func]func[/c:func][c:paren_0]([/c:paren_0][c:paren_0])[/c:paren_0]",
  };

  testCases.forEach((input, expectedOutput) {
    print('\n--- Testing Input: $input');
    final result = parser.parse(input);
    if (result.isSuccess) {
      final List<TaggedSegment> segments = result.value;
      // segments.forEach((s) => print("  Raw Segment: ${s.type} - '${s.text}'"));
      final finalOutput = buildFinalString(segments);
      print('Actual Output: $finalOutput');
      print('Expected Output: $expectedOutput');
      if (finalOutput == expectedOutput) {
        print('Status: MATCH');
      } else {
        print('Status: MISMATCH <<<');
      }
    } else {
      print('Parsing failed: ${result.message} at position ${result.position}');
    }
  });
}
