import 'dart:async';
import 'dart:math';

import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:davi/davi.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:intl/intl.dart';
import 'package:mobx/mobx.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../app/util/byte_utils.dart';
import '../../../app/util/log_utils.dart';
import '../../rename/direct_rename_field.dart';
import '../../util/context_extensions.dart';
import '../../util/error_formatter.dart';
import '../../util/widgets/error_text.dart';
import '../../util/widgets/text_overflow_middle.dart';
import '../domain/display_file.dart';
import '../domain/sort.dart';

// FIXME: This should be taken from an i18n lib.
final dateFormat = DateFormat("yyyy-MM-dd HH:mm");
const nameColumnId = 'name';
const sizeColumnId = 'size';
const updateDateColumnId = 'updateDate';
const loadingDelay = Duration(milliseconds: 50);

class FileList extends HookWidget {
  FileList({super.key});

  final GlobalKey _fileListKey = GlobalKey();
  final DaviModel<DisplayFile> _model = DaviModel<DisplayFile>(
    columns: [
      // TODO: Set a minWidth for this column.
      DaviColumn(
        id: nameColumnId,
        name: 'Name',
        grow: 1,
        cellWidget: (params) {
          final buildContext = params.buildContext;
          final displayFile = params.data;
          final file = displayFile.file;
          return Row(children: [
            displayFile is ParentDirDisplayFile
                ? const Icon(Icons.keyboard_return_rounded, size: 22)
                : file.isFile
                    ? const Icon(Icons.file_copy_outlined, size: 18)
                    : const Icon(Icons.folder, color: Color(0xFFB9A560), size: 20),
            Expanded(
                child: Container(
                    padding: const EdgeInsets.only(left: 4),
                    // Use Observer to ensure this rebuilds when rename state changes
                    child: Observer(builder: (context) {
                      // We need to access directoryViewStore from the context
                      final directoryViewStore = context.directoryViewStore;
                      final isRenaming = directoryViewStore.renameStore.isRenaming;
                      final fileBeingRenamed = directoryViewStore.renameStore.fileBeingRenamed;

                      return isRenaming && fileBeingRenamed == displayFile
                          ? DirectRenameField(
                              key: ValueKey('rename-${displayFile.displayName}'),
                              file: displayFile,
                            )
                          : TextOverflowMiddle(
                              displayFile.displayName,
                              style: buildContext.theme.textTheme.bodyMedium,
                              onEllipsisTap: () => {}, // TODO: Implement this.
                            );
                    }))),
          ]);
        },
      ),
      // TODO: Set a minWidth for this column.
      DaviColumn(
          id: sizeColumnId,
          name: 'Size',
          // width: 100,
          cellWidget: (params) {
            final buildContext = params.buildContext;
            final displayFile = params.data;
            final file = displayFile.file;
            final error = file.error;
            return error == null
                ? Text(file.size?.asHumanReadableBytes ?? (file.isDirectory ? '<DIR>' : ''))
                : CustomTooltip(
                    message: ErrorFormatter.formatError(error),
                    child: Icon(
                      Icons.error_outline,
                      color: buildContext.theme.colorScheme.error,
                      size: 18,
                    ),
                  );
          }),
      // TODO: Set a minWidth for this column.
      DaviColumn(
          id: updateDateColumnId,
          name: 'Update Date',
          grow: 1,
          cellWidget: (params) {
            final displayFile = params.data;
            final file = displayFile.file;
            return Text(dateFormat.format(file.updateTime));
          }),
    ],
    ignoreDataComparators: true,
    sortingMode: SortingMode.alwaysSorted,
  );

  @override
  Widget build(BuildContext context) {
    final directoryViewStore = context.directoryViewStore;
    final paneStore = context.paneStore;

    final scrollController = useScrollController();
    final focusNode = useFocusNode(descendantsAreFocusable: false);

    // Update KeyBindContext when focus changes
    if (kDebugMode) {
      useEffect(() {
        void onFocusChange() {
          if (focusNode.hasFocus) {
            logger.finer('FileList: Focused');
          } else {
            logger.finer('FileList: Unfocsed');
          }
        }

        focusNode.addListener(onFocusChange);
        return () => focusNode.removeListener(onFocusChange);
      }, [focusNode]);
    }

    // State to track whether to show the loading indicator
    final showLoading = useState(false);

    // Timer for delayed loading indicator
    final loadingTimer = useRef<Timer?>(null);

    // Request focus only when this pane is the source pane
    useEffect(() {
      final disposer = reaction((_) => paneStore.isSource, (isSource) {
        if (isSource) {
          // Use a post-frame callback to ensure the focus request happens after the widget is fully built
          WidgetsBinding.instance.addPostFrameCallback((_) {
            focusNode.requestFocus();
          });
        }
      }, fireImmediately: true);
      return disposer.call;
    }, []);

    useExtraSpeedScrollController(scrollController, extraScrollSpeed: 120);

    // Initialize model with sort and onSort callback once
    useEffect(() {
      // Set up the onSort callback
      _model.onSort = (sortedColumns) {
        directoryViewStore.setSort(sortedColumns.map(daviColumnToSort).toList());
      };

      // Initial sort
      return autorun((_) => _model.sort(directoryViewStore.sort.map(sortToDaviSort).toList())).call;
    }, []);

    // Update model when focusedRowIndex changes
    useEffect(() {
      return reaction(
        (_) => directoryViewStore.focusedRowIndex,
        (_) => _model.notifyUpdate(),
      ).call;
    }, []);

    // Effect to handle loading state changes with delay
    useEffect(() {
      final disposer = reaction((_) => directoryViewStore.isLoading, (isLoading) {
        // Cancel any existing timer
        loadingTimer.value?.cancel();

        if (isLoading) {
          // Set isReady to false immediately when loading starts
          directoryViewStore.setIsReady(false);

          // Start a timer to show loading indicator after we've been loading for "loadingDelay"
          loadingTimer.value = Timer(loadingDelay, () {
            showLoading.value = true;
          });
        } else {
          // Immediately hide loading indicator when loading is complete
          showLoading.value = false;

          // Set isReady after the frame is rendered
          SchedulerBinding.instance.addPostFrameCallback((_) {
            directoryViewStore.setIsReady(true);
          });
        }
      }, fireImmediately: true);

      // Clean up timer when widget is disposed
      return () {
        loadingTimer.value?.cancel();
        disposer.call();
      };
    }, []);

    return Observer(builder: (context) {
      final isLoading = directoryViewStore.isLoading;
      final error = directoryViewStore.error;
      final files = directoryViewStore.files;

      // Only show loading UI if isLoading is true AND showLoading is true
      if (isLoading && showLoading.value) {
        final parentDir = directoryViewStore.dirDisplayFile;
        final files = [if (parentDir != null) parentDir];
        return Column(children: [
          SizedBox(
            height: parentDir != null ? 62 : 32,
            child: _fileList(context, files, scrollController, focusNode),
          ),
          Expanded(
              child: ListView.builder(
            shrinkWrap: true,
            padding: const EdgeInsets.all(4),
            itemCount: 100,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return Skeletonizer(child: Text("Loading..."));
              // return const SkeletonLine(
              //     style: SkeletonLineStyle(
              //         padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2.5)));
            },
          )),
        ]);
      }

      if (error != null) {
        final parentDir = directoryViewStore.dirDisplayFile;
        final files = [if (parentDir != null) parentDir];
        return Column(children: [
          SizedBox(
            height: parentDir != null ? 62 : 32,
            child: _fileList(context, files, scrollController, focusNode),
          ),
          Expanded(child: ErrorText(ErrorFormatter.formatError(error))),
        ]);
      }

      return _fileList(context, files, scrollController, focusNode);
    });
  }

  Widget _fileList(BuildContext context, List<DisplayFile> files, ScrollController scrollController, FocusNode focusNode) {
    final directoryViewStore = context.directoryViewStore;
    final paneStore = context.paneStore;

    // Update the model with the current files
    _model.replaceRows(files);

    return DaviTheme(
      data: DaviThemeData(
        decoration: paneStore.isSource ? BoxDecoration(color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(25)) : null,
        columnDividerThickness: 0.2,
        // scrollbar: TableScrollbarThemeData(horizontalOnlyWhenNeeded: false),
        header: const HeaderThemeData(
          bottomBorderThickness: 0,
        ),
        headerCell: HeaderCellThemeData(sortIconBuilder: (direction, _) {
          return direction == DaviSortDirection.ascending
              ? const Icon(Icons.arrow_upward_rounded, size: 16)
              : const Icon(Icons.arrow_downward_rounded, size: 16);
        }),
        row: RowThemeData(
          dividerThickness: 0,
          hoverBackground: (_) => context.theme.hoverColor,
          color: (rowIndex) {
            final focusedIndex = directoryViewStore.focusedRowIndex;

            // Check if this is the focused row
            if (rowIndex == focusedIndex) {
              // If pane is source, use the normal focus color
              if (paneStore.isSource) {
                return Colors.grey.shade300; // Light gray background for source pane focused row
              }
              // For target pane, use a more distinctive background color
              return Color(0xFFE3F2FD); // Light blue background for target pane focused row
            }
            return null;
          },
        ),
        cell: const CellThemeData(
          padding: EdgeInsets.symmetric(horizontal: 8),
          contentHeight: 22,
        ),
      ),
      child: Davi<DisplayFile>(
        key: _fileListKey,
        _model,
        verticalScrollController: scrollController,
        columnWidthBehavior: ColumnWidthBehavior.scrollable,
        onRowDoubleTap: directoryViewStore.executeFile,
        onRowTap: (file) {
          _onRowTap(context, file);
          // Request focus for the file list
          focusNode.requestFocus();
        },
      ),
    );
  }

  /// Handle row tap events
  void _onRowTap(BuildContext context, DisplayFile file) {
    final directoryViewStore = context.directoryViewStore;
    final paneStore = context.paneStore;

    directoryViewStore.setFocusedFile(file);
    paneStore.makeSource();
  }



  Sort daviColumnToSort(DaviColumn<DisplayFile> column) {
    final sort = column.sortDirection!;
    final direction = sort == DaviSortDirection.ascending ? SortDirection.ascending : SortDirection.descending;
    switch (column.id) {
      case nameColumnId:
        return Sort(SortType.name, direction, directoryFirst: true);
      case sizeColumnId:
        return Sort(SortType.size, direction, directoryFirst: false);
      case updateDateColumnId:
        return Sort(SortType.updateDate, direction, directoryFirst: false);
      default:
        throw Exception("Invalid column id: ${column.id}");
    }
  }

  DaviSort sortToDaviSort(Sort sort) {
    final direction = sort.direction == SortDirection.ascending ? DaviSortDirection.ascending : DaviSortDirection.descending;
    final columnId = switch (sort.type) {
      SortType.name => nameColumnId,
      SortType.size => sizeColumnId,
      SortType.updateDate => updateDateColumnId,
      SortType.createDate => updateDateColumnId, // Use updateDate column for createDate as fallback
    };
    return DaviSort(columnId, direction);
  }

  static final logger = loggerFor(FileList);
}

class CustomTooltip extends StatefulWidget {
  final Widget child;
  final String message;
  final TextStyle? textStyle;
  final Duration showDelay;

  const CustomTooltip({
    super.key,
    required this.child,
    required this.message,
    this.textStyle,
    this.showDelay = const Duration(milliseconds: 100),
  });

  @override
  State<CustomTooltip> createState() => _CustomTooltipState();
}

class _CustomTooltipState extends State<CustomTooltip> {
  // Timer for delayed tooltip display
  Timer? _showTooltipTimer;

  // Track mouse position
  Offset? _mousePosition;

  // Store tooltip entry
  OverlayEntry? _overlayEntry;

  // Force tooltip position to always be either above or below the cursor
  bool _forceAboveCursor = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _hideTooltip();
    _showTooltipTimer?.cancel();
    super.dispose();
  }

  void _showTooltip() {
    if (_overlayEntry != null || _mousePosition == null) return;

    // Use the mouse position directly for positioning
    final mousePos = _mousePosition!;

    // Get the overlay instance
    final OverlayState overlay = Overlay.of(context);

    // Get screen size to check boundaries
    final screenSize = MediaQuery.of(context).size;

    // Estimate the pane width - since we're in a dual-pane setup, use
    // approximately 70% of half the screen width as the max width constraint
    final estimatedPaneWidth = screenSize.width / 2;

    // Calculate maximum tooltip width but ensure it's not larger than available screen space
    // minus padding (40px) on both sides
    final maxTooltipWidth = min(estimatedPaneWidth * 0.7, screenSize.width - 80); // More conservative to avoid clipping

    // Horizontal positioning logic
    double leftPosition;

    // Check if the mouse is in the right half of the screen
    if (mousePos.dx > screenSize.width / 2) {
      // If mouse is on right side, align tooltip to not go off right edge
      leftPosition = min(mousePos.dx - 20, screenSize.width - maxTooltipWidth - 20);
    } else {
      // If mouse is on left side, start at mouse position
      leftPosition = max(20, mousePos.dx - 20);
    }

    // Use fixed height values for different tooltip types based on message length
    final bool isLongMessage = widget.message.length > 40;
    final tooltipHeight = isLongMessage ? 60.0 : 36.0;

    // Calculate vertical positioning
    double verticalOffset;

    // Check how much room we have below the cursor
    final spaceBelow = screenSize.height - mousePos.dy;

    // Position tooltip consistently relative to cursor
    if (spaceBelow > tooltipHeight + 20 && !_forceAboveCursor) {
      // Position below cursor with consistent minimal spacing
      verticalOffset = mousePos.dy + 15.0;
    } else {
      // Position above cursor with minimal spacing
      _forceAboveCursor = true;
      verticalOffset = max(10, mousePos.dy - tooltipHeight - 15.0);
    }

    // Create an overlay entry positioned relative to the mouse cursor
    _overlayEntry = OverlayEntry(
      builder: (context) {
        // Get text theme from context for proper text styling
        final textTheme = Theme.of(context).textTheme;

        return Positioned(
          left: leftPosition,
          top: verticalOffset,
          child: Material(
            color: Colors.transparent,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: maxTooltipWidth,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[850],
                borderRadius: BorderRadius.circular(6),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(76),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Text(
                widget.message,
                style: widget.textStyle ??
                    textTheme.bodyMedium?.copyWith(color: Colors.white) ??
                    const TextStyle(color: Colors.white, fontSize: 14),
                overflow: TextOverflow.visible,
                softWrap: true,
              ),
            ),
          ),
        );
      },
    );

    overlay.insert(_overlayEntry!);
  }

  void _hideTooltip() {
    _showTooltipTimer?.cancel();
    _overlayEntry?.remove();
    _overlayEntry = null;
    _forceAboveCursor = false; // Reset when tooltip is closed
  }

  void _handleMouseEnter(PointerEvent event) {
    _mousePosition = event.position;
    _showTooltipTimer?.cancel();
    _showTooltipTimer = Timer(widget.showDelay, _showTooltip);
  }

  void _handleMouseExit(PointerEvent event) {
    _mousePosition = null;
    _hideTooltip();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: _handleMouseEnter,
      onExit: _handleMouseExit,
      cursor: SystemMouseCursors.help,
      child: widget.child,
    );
  }
}
