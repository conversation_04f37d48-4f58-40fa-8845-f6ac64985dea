import 'dart:async';
import 'dart:io' show Platform;

import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';
import 'package:path/path.dart' as path;

import '../../app/domain/file.dart';
import '../../app/notification/notification_store.dart';
import '../../app/task/task.dart';
import '../../app/task/task_store.dart';
import '../../app/util/log_utils.dart';
import '../command/command_context.dart';
import '../command/command_context_repository.dart';
import '../directory_view/domain/display_file.dart';

part '.gen/rename_store.g.dart';

// Reserved Windows filenames
final reservedWindowsFilenames = [
  'con',
  'prn',
  'aux',
  'nul',
  'com1',
  'com2',
  'com3',
  'com4',
  'com5',
  'com6',
  'com7',
  'com8',
  'com9',
  'lpt1',
  'lpt2',
  'lpt3',
  'lpt4',
  'lpt5',
  'lpt6',
  'lpt7',
  'lpt8',
  'lpt9',
];

// Regex patterns for illegal characters
final illegalFATCharactersRegex = RegExp(r'[<>:"\\|?*]');
final illegalWindowsTrailingCharactersRegex = RegExp(r'[\\ .]+$');
final illegalPosixCharactersRegex = RegExp(r'[/]');
final illegalHFSCharactersRegex = RegExp(r'[/:]');
final controlCharacterRegex = RegExp(r'[\x00-\x1F\x7F]');

/// Manages file rename operations
class RenameStore = RenameStoreBase with _$RenameStore;

abstract class RenameStoreBase with Store {
  RenameStoreBase(this._taskStore, this._notificationStore, this._commandContextRepository);

  final JobStore _taskStore;
  final NotificationStore _notificationStore;
  final CommandContextRepository _commandContextRepository;

  /// The file currently being renamed
  @observable
  DisplayFile? fileBeingRenamed;

  /// The new file name being entered
  @observable
  String newFileName = 'placeholder';

  /// Stream controller for synchronizing rename operations across panes
  final _renameStreamController = StreamController<String>.broadcast();

  /// Stream of rename text changes
  Stream<String> get renameStream => _renameStreamController.stream;

  /// Whether we're currently synchronizing a rename operation
  bool _isSynchronizing = false;

  /// Whether a rename operation is in progress
  @computed
  bool get isRenaming => fileBeingRenamed != null;

  /// Validation error for the new file name, if any
  @computed
  String? get validationError {
    if (newFileName.isEmpty) {
      return 'Cannot be empty!';
    }

    // Check if the filename is a relative path
    if (newFileName == '.' || newFileName == '..') {
      return 'Cannot be \'.\' or \'..\'.';
    }

    // Check if the filename is of valid length
    if (newFileName.length > 255) {
      return 'Cannot be longer than 255 characters';
    }

    // Check for null character
    if (newFileName.contains(String.fromCharCode(0))) {
      return 'Contains a null character!';
    }

    // Check for control characters
    if (newFileName.contains(controlCharacterRegex)) {
      final match = controlCharacterRegex.firstMatch(newFileName);
      if (match != null) {
        return 'Invalid control character at position ${match.start}';
      }
      return 'Contains control characters!';
    }

    // Platform-specific validations
    if (Platform.isWindows) {
      // Check for illegal Windows characters
      if (newFileName.contains(illegalFATCharactersRegex)) {
        final match = illegalFATCharactersRegex.firstMatch(newFileName);
        if (match != null) {
          return 'Invalid character at position ${match.start}: \'${newFileName[match.start]}\'';
        }
        return 'Contains invalid characters!';
      }

      // Check for illegal Windows trailing characters
      if (newFileName.contains(illegalWindowsTrailingCharactersRegex)) {
        return 'Cannot end with a space or period';
      }

      // Check for reserved Windows filenames
      final nameWithoutExt = path.basenameWithoutExtension(newFileName).toLowerCase();
      if (reservedWindowsFilenames.contains(nameWithoutExt)) {
        return 'Is a reserved Windows name!';
      }
    } else if (Platform.isMacOS || Platform.isIOS) {
      // Check for illegal HFS characters
      if (newFileName.contains(illegalHFSCharactersRegex)) {
        final match = illegalHFSCharactersRegex.firstMatch(newFileName);
        if (match != null) {
          return 'Invalid character at position ${match.start}: \'${newFileName[match.start]}\'';
        }
        return 'Contains invalid characters!';
      }
    } else {
      // Check for illegal POSIX characters (Linux, Android, etc.)
      if (newFileName.contains(illegalPosixCharactersRegex)) {
        final match = illegalPosixCharactersRegex.firstMatch(newFileName);
        if (match != null) {
          return 'Invalid character at position ${match.start}: \'${newFileName[match.start]}\'';
        }
        return 'Contains invalid characters!';
      }
    }

    return null;
  }

  /// Start renaming a file
  @action
  void startRename(DisplayFile file) {
    if (kDebugMode) {
      logger.info('startRename($file)');
    }
    // Cancel any existing rename operation first
    if (fileBeingRenamed != null) {
      cancelRename();
    }

    if (file is ParentDirDisplayFile) {
      _notificationStore.notifyWarn('Cannot rename the parent directory!');
      return;
    }

    fileBeingRenamed = file;
    if (file is FileDisplayFile) {
      newFileName = file.file.name;
    }

    // Update the command context to indicate we're renaming
    _commandContextRepository.pushContext(const CommandContext(isRenaming: true));
  }

  /// Update the new file name
  @action
  void setRenamedFileName(String fileName, {bool sanitize = false, bool broadcast = true}) {
    // Optionally sanitize the file name to ensure it's valid
    final newValue = sanitize ? _sanitizeFileName(fileName) : fileName;

    // Only update if the value has changed
    if (newFileName != newValue) {
      newFileName = newValue;

      // Broadcast the change to other panes if not already synchronizing
      if (broadcast && !_isSynchronizing) {
        _renameStreamController.add(newValue);
      }
    }
  }

  /// Set up a listener for rename synchronization
  void setupRenameSyncListener() {
    // Listen to the rename stream
    renameStream.listen((newValue) {
      // Set a flag to prevent infinite loops
      _isSynchronizing = true;

      // Update the file name without broadcasting
      setRenamedFileName(newValue, broadcast: false);

      // Reset the flag
      _isSynchronizing = false;
    });
  }

  /// Accept the rename operation
  @action
  Future<void> acceptRename() async {
    final file = fileBeingRenamed;
    if (kDebugMode) {
      logger.info('acceptRename($file)');
    }

    // Safety check - don't proceed if there's no file being renamed
    if (file == null) {
      if (kDebugMode) {
        logger.warning('acceptRename($file) called without starting a rename!');
      }
      return;
    }

    if (validationError != null) {
      if (kDebugMode) {
        logger.severe('acceptRename($file) called with a validation error: $validationError');
      }
      return;
    }

    assert(file is FileDisplayFile, 'acceptRename($file) called with a non-file display file');

    final fileToRename = file.file;
    if (fileToRename.name != newFileName) {
      // Add a rename task
      _taskStore.addJobSpec(JobSpec.rename(file: fileToRename, newName: newFileName));
    }

    _finishRename();
  }

  /// Cancel the rename operation
  @action
  void cancelRename() {
    final file = fileBeingRenamed;
    if (file != null) {
      if (kDebugMode) {
        logger.info('cancelRename($file)');
      }

      _finishRename();
    } else if (kDebugMode) {
      if (kDebugMode) {
        logger.fine('cancelRename($file) called with no file being renamed!');
      }
    }
  }

  void _finishRename() {
    if (fileBeingRenamed != null) {
      // Update the command context to indicate we're no longer renaming
      _commandContextRepository.popContext(const CommandContext(isRenaming: true));

      fileBeingRenamed = null;
      newFileName = 'placeholder'; // something valid so the validation error doesn't trigger
    }
  }

  /// Dispose of resources
  void dispose() {
    _renameStreamController.close();
  }

  /// Get the file name without extension
  String getNameWithoutExtension(File file) {
    if (file.isDirectory) {
      return file.name;
    }
    return path.basenameWithoutExtension(file.name);
  }

  /// Sanitize a file name to ensure it's valid for the current operating system
  String _sanitizeFileName(String fileName, {String replacement = '_', String placeholder = 'untitled'}) {
    var result = fileName;

    // Replace control characters
    result = result.replaceAll(controlCharacterRegex, replacement);

    // Replace null character
    result = result.replaceAll(String.fromCharCode(0), replacement);

    if (Platform.isWindows) {
      // Replace illegal characters
      result = result.replaceAll(illegalFATCharactersRegex, replacement);

      // Replace trailing illegal characters
      result = result.replaceAll(illegalWindowsTrailingCharactersRegex, replacement);

      // Replace reserved file names
      final nameWithoutExt = path.basenameWithoutExtension(result).toLowerCase();
      if (reservedWindowsFilenames.contains(nameWithoutExt)) {
        // Add an underscore to avoid reserved name
        final extension = path.extension(result);
        result = '$nameWithoutExt$replacement$extension';
      }
    } else if (Platform.isMacOS || Platform.isIOS) {
      // Replace illegal HFS characters
      result = result.replaceAll(illegalHFSCharactersRegex, replacement);
    } else {
      // Replace illegal POSIX characters
      result = result.replaceAll(illegalPosixCharactersRegex, replacement);
    }

    // If the filename is empty or a relative path, replace it with a placeholder
    if (result.isEmpty || result == '.' || result == '..') {
      result = placeholder;
    }

    // Check length
    if (result.length > 255) {
      result = result.substring(0, 255);
    }

    return result;
  }

  static final logger = loggerFor(RenameStore);
}
