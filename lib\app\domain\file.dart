import 'package:mek_data_class/mek_data_class.dart';
import 'package:meta/meta.dart';

import '../task/task.dart';
import '../util/string_utils.dart';
import './path.dart';

part '.gen/file.g.dart';

@immutable
@DataClass(copyable: true)
class File extends _FileBase with _$File {
  File(super.path, super.stats, [super.lifecycle = FileLifecycle.exists, super.pendingOperations]);

  File withPath(RawPath path) => copyWith(path: path);

  File withStats(PathStats stats) => copyWith(stats: stats);

  /// Creates a new file with updated size and files total count
  // File withStats(int bytesTotal, int filesTotal) {
  //   final updatedStats = stats.copyWith(size: bytesTotal);
  //   return File(path, updatedStats, exists);
  // }

  static File fromPath(Path path) => File(path.path, path.stats);
  static int naturalComparator(File a, File b) => a.compareTo(b);
  static int caseInsensitiveNameComparator(File a, File b) => a.name.compareToIgnoreCase(b.name);

  @override
  String toString() =>
      "${isDirectory ? path.toString() : path.absolutePath.toString()}${lifecycle == FileLifecycle.exists ? '' : ' {$lifecycle}'}";
}

@immutable
abstract class _FileBase with PathUtils, StatsUtils implements Comparable<_FileBase> {
  _FileBase(this.path, this.stats, this.lifecycle, [this.pendingOperations]);

  // FIXME: Why not wrap a PathWithStats?
  @override
  final RawPath path;
  @override
  final PathStats stats;

  final FileLifecycle lifecycle;

  /// List of pending operations for this file
  final List<Operation>? pendingOperations;

  // TODO: Consider adding pendingStats.

  bool isParentOf(File other) => path.isParentOf(other.path);
  bool isChildOf(File other) => path.isChildOf(other.path);
  bool isAncestorOf(File other) => path.isAncestorOf(other.path);
  bool isSiblingOf(File other) => path.isSiblingOf(other.path);
  bool endsWith(String suffix) => path.endsWith(suffix);

  @override
  int compareTo(_FileBase other) => path.compareTo(other.path);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _FileBase && runtimeType == other.runtimeType && path == other.path && stats == other.stats && lifecycle == other.lifecycle;

  @override
  int get hashCode => path.hashCode ^ stats.hashCode ^ lifecycle.hashCode;
}

/// The type of file display in the UI
enum FileLifecycle {
  /// File exists in the file system and has no pending operations
  exists,

  /// File doesn't exist in the file system yet, but will when an operation is completed
  pendingAdd,

  /// File exists in the file system, but will be removed when an operation is completed
  pendingRemove,

  /// File exists in the file system, but will be updated/overwritten when an operation is completed
  pendingUpdate,
}
