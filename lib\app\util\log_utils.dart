import 'package:logging/logging.dart';

export 'package:logging/logging.dart';

extension LoggerExtension on Logger {
  bool get isFineEnabled => isLoggable(Level.FINE);
  bool get isFinerEnabled => isLoggable(Level.FINER);
  bool get isFinestEnabled => isLoggable(Level.FINEST);
}

Logger loggerFor(Type type, [Level? level]) {
  final logger = Logger(type.toString());
  if (level != null) {
    logger.level = level;
  }
  return logger;
}
