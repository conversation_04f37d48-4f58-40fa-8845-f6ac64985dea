import 'package:analyzer/dart/element/element.dart';
import 'package:build/build.dart';
import 'package:mobx_codegen/src/template/method_override.dart';
import 'package:mobx_codegen/src/template/store.dart';

import 'zip_with_prev_annotation.dart';

/// A generator for MobX [ZipWithPrev] annotations.
///
/// This generator processes methods annotated with [ZipWithPrev]
/// and generates code that creates computed properties that zip
/// the current value with its previous value.
class ZipWithPrevGenerator {
  /// Generates code for a method annotated with [ZipWithPrev].
  ///
  /// The [method] is the annotated method element.
  /// The [annotation] is the [ZipWithPrev] annotation instance.
  /// The [storeTemplate] is the template for the store class.
  ///
  /// Returns the generated code for the zip-with-prev property.
  String generate(
    MethodElement method,
    ZipWithPrev annotation,
    StoreTemplate storeTemplate,
  ) {
    final methodName = method.name;
    final returnType = method.returnType.getDisplayString(withNullability: true);
    final cacheFieldName = '_${methodName}ZipWithPrevCache';

    // Add a field to store the previous value and the computed instance
    final fields = '''
  Computed<List<$returnType>>? $cacheFieldName;
  $returnType? _${methodName}PrevValue;
''';

    // Generate the getter method
    final methodOverride = MethodOverrideTemplate();
    methodOverride.name = methodName;
    methodOverride.returnType = 'List<$returnType>';

    final body = '''
    $cacheFieldName ??= Computed<List<$returnType>>(() {
      final value = super.$methodName;
      final prevToUse = _${methodName}PrevValue ?? value;
      _${methodName}PrevValue = value;
      return [value, prevToUse];
    }, name: '${methodName}ZipWithPrev');
    return $cacheFieldName!.value;
''';

    return '''
@override
List<$returnType> get $methodName {
$body
}
$fields''';
  }
}

/// A builder for generating code for [ZipWithPrev] annotations.
class ZipWithPrevBuilder extends Builder {
  @override
  Map<String, List<String>> get buildExtensions => {
        '.dart': ['.zipwithprev.g.part'],
      };

  @override
  Future<void> build(BuildStep buildStep) async {
    // Implementation would go here, similar to other MobX generators
    // This is a simplified version for demonstration
    final resolver = buildStep.resolver;
    if (!await resolver.isLibrary(buildStep.inputId)) return;

    final library = await buildStep.inputLibrary;
    final generator = ZipWithPrevGenerator();

    // Process annotations and generate code
    // (Actual implementation would be more complex)
  }
}
