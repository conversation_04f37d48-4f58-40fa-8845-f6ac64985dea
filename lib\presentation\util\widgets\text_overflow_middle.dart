import 'package:dartx/dartx.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class TextOverflowMiddle extends HookWidget {
  final String text;
  final TextStyle? style;
  final void Function()? onEllipsisTap;

  final TextSpan _fullText;
  late final TextPainter _textPainter;
  late final int _textWidth;

  late final RichText _richFullText = RichText(text: _fullText);

  late final TextSpan _ellipsis =
      TextSpan(text: '...', style: style, recognizer: onEllipsisTap != null ? (TapGestureRecognizer()..onTap = onEllipsisTap) : null);

  TextOverflowMiddle(this.text, {this.style, this.onEllipsisTap, super.key}) : _fullText = TextSpan(text: text, style: style) {
    _textPainter = TextPainter(text: _fullText, maxLines: 1, textDirection: TextDirection.ltr)..layout();
    _textWidth = _textPainter.width.ceil();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_textWidth <= constraints.maxWidth) {
          return _richFullText;
        }

        final firstHalfWidth = constraints.maxWidth / 2 - _ellipsisWidth / 2;
        final lastHalfWidth = constraints.maxWidth - _ellipsisWidth / 2 - firstHalfWidth;

        // Subtract and add 1 from the indexes to account for inaccuracies that caused the text to
        // not break where it should. Probably half pixels, but I'm not sure. This helped.
        final lastIndexBeforeEllipsis = (_textPainter.getPositionForOffset(Offset(firstHalfWidth, 0)).offset - 1).coerceAtLeast(0);
        final firstIndexAfterEllipsis =
            (_textPainter.getPositionForOffset(Offset(_textWidth - lastHalfWidth, 0)).offset + 1).coerceAtMost(text.length - 1);

        final children = <TextSpan>[];
        children.add(TextSpan(text: text.substring(0, lastIndexBeforeEllipsis)));
        children.add(_ellipsis);
        children.add(TextSpan(text: text.substring(firstIndexAfterEllipsis)));

        return Tooltip(
          message: text,
          preferBelow: false,
          child: RichText(text: TextSpan(children: children, style: style)),
        );
      },
    );
  }

  int get _ellipsisWidth => _ellipsisWidthCache[style] ??= () {
        return _measureWidth(_ellipsis);
      }();

  int _measureWidth(TextSpan text) {
    final tp = TextPainter(text: text, maxLines: 1, textDirection: TextDirection.ltr)..layout();
    return tp.width.ceil();
  }

  // Cache ellipsis width per TextStyle, since it is unlikely to change.
  static final _ellipsisWidthCache = <TextStyle?, int>{};
}

// class TextOverflowMiddle extends StatelessWidget {
//   final String text;
//   final TextStyle? style;
//
//   const TextOverflowMiddle(this.text, {required this.style, super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     final ellipsis = '...';
//     final ellipsisTp = TextPainter(
//       text: TextSpan(text: ellipsis, style: style),
//       maxLines: 1,
//       textDirection: TextDirection.ltr,
//     )..layout();
//     final ellipsisWidth = ellipsisTp.width;
//
//     return LayoutBuilder(builder: (context, constraints) {
//       final textPainter = TextPainter(
//         text: TextSpan(text: text, style: style),
//         maxLines: 1,
//         textDirection: TextDirection.ltr,
//       );
//       textPainter.layout(maxWidth: constraints.maxWidth);
//
//       if (textPainter.didExceedMaxLines) {
//         final firstPart = text.substring(0, (text.length / 2).floor() - 1);
//         final secondPart = text.substring((text.length / 2).ceil());
//
//         return Stack(
//           children: [
//             Positioned(
//               left: 0,
//               child: ConstrainedBox(
//                 constraints: BoxConstraints(
//                   maxWidth: constraints.maxWidth / 2 - ellipsisWidth,
//                 ),
//                 child: Text(
//                   firstPart,
//                   style: style,
//                   overflow: TextOverflow.clip,
//                   maxLines: 1,
//                 ),
//               ),
//             ),
//             Positioned(
//               right: 0,
//               child: ConstrainedBox(
//                 constraints: BoxConstraints(
//                   maxWidth: constraints.maxWidth / 2 - ellipsisWidth,
//                 ),
//                 child: Text(
//                   secondPart,
//                   style: style,
//                   // textAlign: TextAlign.right,
//                   textDirection: TextDirection.rtl,
//                   overflow: TextOverflow.clip,
//                   maxLines: 1,
//                   softWrap: false,
//                 ),
//               ),
//             ),
//             Center(
//               // left: constraints.maxWidth / 2 - ellipsisWidth / 2,
//               child: Text(
//                 ellipsis,
//                 style: style,
//               ),
//             ),
//           ],
//         );
//       } else {
//         return Text(
//           text,
//           style: style,
//           overflow: TextOverflow.ellipsis,
//         );
//       }
//     });
//   }
// }
